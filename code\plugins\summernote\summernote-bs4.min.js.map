{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///webpack/bootstrap", "webpack:///external {\"root\":\"jQ<PERSON>y\",\"commonjs2\":\"jquery\",\"commonjs\":\"jquery\",\"amd\":\"jquery\"}", "webpack:///./src/js/base/renderer.js", "webpack:///(webpack)/buildin/amd-options.js", "webpack:///./src/js/base/summernote-en-US.js", "webpack:///./src/js/base/core/env.js", "webpack:///./src/js/base/core/func.js", "webpack:///./src/js/base/core/lists.js", "webpack:///./src/js/base/core/dom.js", "webpack:///./src/js/base/Context.js", "webpack:///./src/js/base/core/range.js", "webpack:///./src/js/summernote.js", "webpack:///./src/js/base/core/key.js", "webpack:///./src/js/base/editing/History.js", "webpack:///./src/js/base/editing/Style.js", "webpack:///./src/js/base/editing/Bullet.js", "webpack:///./src/js/base/editing/Typing.js", "webpack:///./src/js/base/editing/Table.js", "webpack:///./src/js/base/module/Editor.js", "webpack:///./src/js/base/core/async.js", "webpack:///./src/js/base/module/Clipboard.js", "webpack:///./src/js/base/module/Dropzone.js", "webpack:///./src/js/base/module/Codeview.js", "webpack:///./src/js/base/module/Statusbar.js", "webpack:///./src/js/base/module/Fullscreen.js", "webpack:///./src/js/base/module/Handle.js", "webpack:///./src/js/base/module/AutoLink.js", "webpack:///./src/js/base/module/AutoSync.js", "webpack:///./src/js/base/module/AutoReplace.js", "webpack:///./src/js/base/module/Placeholder.js", "webpack:///./src/js/base/module/Buttons.js", "webpack:///./src/js/base/module/Toolbar.js", "webpack:///./src/js/base/module/LinkDialog.js", "webpack:///./src/js/base/module/LinkPopover.js", "webpack:///./src/js/base/module/ImageDialog.js", "webpack:///./src/js/base/module/ImagePopover.js", "webpack:///./src/js/base/module/TablePopover.js", "webpack:///./src/js/base/module/VideoDialog.js", "webpack:///./src/js/base/module/HelpDialog.js", "webpack:///./src/js/base/module/AirPopover.js", "webpack:///./src/js/base/module/HintPopover.js", "webpack:///./src/js/base/settings.js", "webpack:///./src/js/bs4/ui.js", "webpack:///./src/js/bs4/settings.js"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "a", "i", "window", "__WEBPACK_EXTERNAL_MODULE__0__", "installedModules", "__webpack_require__", "moduleId", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "<PERSON><PERSON><PERSON>", "markup", "children", "options", "callback", "this", "$parent", "$node", "$", "contents", "html", "className", "addClass", "data", "each", "k", "v", "attr", "click", "on", "$container", "find", "for<PERSON>ach", "child", "render", "length", "append", "arguments", "Array", "isArray", "__webpack_amd_options__", "summernote", "lang", "extend", "font", "bold", "italic", "underline", "clear", "height", "strikethrough", "subscript", "superscript", "size", "sizeunit", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "resizeNone", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "useProtocol", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "output", "noSelection", "isSupportAmd", "genericFontFamilies", "validFontName", "fontName", "inArray", "toLowerCase", "browserVersion", "userAgent", "navigator", "isMSIE", "test", "matches", "exec", "parseFloat", "isEdge", "isSupportTouch", "MaxTouchPoints", "msMaxTouchPoints", "inputEventName", "isMac", "appVersion", "indexOf", "isFF", "is<PERSON><PERSON><PERSON>", "isWebkit", "isChrome", "<PERSON><PERSON><PERSON><PERSON>", "jqueryVersion", "fn", "j<PERSON>y", "isFontInstalled", "testFontName", "context", "document", "createElement", "getContext", "testSize", "originalWidth", "measureText", "width", "isW3CRangeSupport", "createRange", "idCounter", "eq", "itemA", "itemB", "eq2", "peq2", "propName", "ok", "fail", "self", "not", "f", "apply", "and", "fA", "fB", "item", "invoke", "obj", "method", "resetUniqueId", "uniqueId", "prefix", "id", "rect2bnd", "rect", "$document", "top", "scrollTop", "scrollLeft", "bottom", "invertObject", "inverted", "namespaceToCamel", "namespace", "split", "map", "substring", "toUpperCase", "join", "debounce", "func", "wait", "immediate", "timeout", "args", "later", "callNow", "clearTimeout", "setTimeout", "isValidUrl", "head", "array", "last", "tail", "slice", "contains", "initial", "prev", "idx", "next", "pred", "len", "all", "sum", "reduce", "memo", "from", "collection", "result", "isEmpty", "clusterBy", "aLast", "compact", "aResult", "push", "unique", "results", "NBSP_CHAR", "String", "fromCharCode", "isEditable", "node", "hasClass", "makePredByNodeName", "nodeName", "isText", "nodeType", "isVoid", "isPara", "isPre", "isLi", "isTable", "isData", "isInline", "isBodyContainer", "isList", "isHr", "isBlockquote", "isCell", "isAnchor", "isBody", "blankHTML", "env", "node<PERSON><PERSON><PERSON>", "nodeValue", "childNodes", "innerHTML", "paddingBlankHTML", "ancestor", "parentNode", "listAncestor", "ancestors", "el", "listNext", "nodes", "nextS<PERSON>ling", "insertAfter", "preceding", "parent", "insertBefore", "append<PERSON><PERSON><PERSON>", "appendChildNodes", "<PERSON><PERSON><PERSON><PERSON>", "isLeftEdgePoint", "point", "offset", "isRightEdgePoint", "isEdgePoint", "isLeftEdgeOf", "position", "isRightEdgeOf", "previousSibling", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prevPoint", "isSkipInnerOffset", "nextPoint", "nextTextNode", "getNextTextNode", "nextPointWithEmptyNode", "actual", "isSamePoint", "pointA", "pointB", "splitNode", "isSkipPaddingBlankHTML", "isNotSplitEdgePoint", "isDiscardEmptySplits", "splitText", "childNode", "clone", "cloneNode", "splitTree", "isRemoveChild", "removeNode", "<PERSON><PERSON><PERSON><PERSON>", "isTextarea", "stripLinebreaks", "val", "replace", "ZERO_WIDTH_NBSP_CHAR", "blank", "emptyPara", "isControlSizing", "isElement", "isPurePara", "isHeading", "isBlock", "isBodyInline", "isParaInline", "isDiv", "isBR", "isSpan", "isB", "isU", "isS", "isI", "isImg", "deepestChildIsEmpty", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "isEmptyAnchor", "isClosestSibling", "nodeA", "nodeB", "withClosest<PERSON><PERSON><PERSON>", "siblings", "isLeftEdgePointOf", "isRightEdgePointOf", "isVisiblePoint", "leftNode", "rightNode", "prevPointUntil", "nextPointUntil", "isCharPoint", "ch", "char<PERSON>t", "isSpacePoint", "walkPoint", "startPoint", "endPoint", "handler", "singleChildAncestor", "last<PERSON><PERSON><PERSON>", "filter", "listPrev", "listDescendant", "descendants", "fnWalk", "current", "commonAncestor", "wrap", "wrapperName", "wrapper", "makeOffsetPath", "reverse", "fromOffsetPath", "offsets", "splitPoint", "splitRoot", "container", "topAncestor", "pivot", "createText", "text", "createTextNode", "<PERSON><PERSON><PERSON><PERSON>", "newNode", "cssText", "isNewlineOnBlock", "match", "endSlash", "isEndOfInlineContainer", "isBlockNode", "trim", "posFromPlaceholder", "placeholder", "$placeholder", "pos", "outerHeight", "attachEvents", "events", "keys", "detachEvents", "off", "isCustomStyleTag", "classList", "Context", "$note", "memos", "layoutInfo", "ui", "ui_template", "initialize", "createLayout", "_initialize", "hide", "_destroy", "removeData", "removeLayout", "disabled", "isDisabled", "code", "dom", "disable", "now", "editor", "buttons", "plugins", "initializeModule", "removeModule", "removeMemo", "triggerEvent", "isActivated", "undefined", "codable", "editable", "editing", "callbacks", "trigger", "shouldInitialize", "ModuleClass", "withoutIntialize", "destroy", "event", "createInvokeHandler", "preventDefault", "$target", "target", "closest", "splits", "hasSeparator", "moduleName", "methodName", "textRangeToPoint", "textRange", "isStart", "prevContainer", "parentElement", "tester", "body", "createTextRange", "moveToElementText", "compareEndPoints", "textRangeStart", "curTextNode", "collapse", "<PERSON><PERSON><PERSON><PERSON>", "pointTester", "duplicate", "setEndPoint", "textCount", "cont", "pointToTextRange", "info", "textRangeInfo", "isCollapseToStart", "prevTextNodes", "collapseToStart", "moveStart", "type", "isExternalAPICalled", "hasInitOptions", "langInfo", "icons", "tooltip", "note", "first", "focus", "WrappedRange", "sc", "so", "ec", "eo", "isOnEditable", "makeIsOn", "isOnList", "isOnAnchor", "isOnCell", "isOnData", "w3cRange", "setStart", "setEnd", "nativeRng", "nativeRange", "selection", "getSelection", "rangeCount", "removeAllRanges", "addRange", "offsetTop", "Math", "abs", "getVisiblePoint", "isLeftToRight", "block", "hasRightNode", "hasLeftNode", "getEndPoint", "isCollapsed", "getStartPoint", "includeAncestor", "fullyContains", "leftEdgeNodes", "startAncestor", "endAncestor", "boundaryPoints", "getPoints", "isSameContainer", "rng", "emptyParents", "normalize", "inlineSiblings", "concat", "para", "wrapBodyInlineWithPara", "deleteContents", "contentsContainer", "reversed", "insertNode", "toString", "findAfter", "isNotTextPoint", "regex", "index", "path", "e", "paras", "getClientRects", "<PERSON><PERSON><PERSON><PERSON>", "createFromSelection", "bodyElement", "<PERSON><PERSON><PERSON><PERSON>", "createFromBodyElement", "createFromNode", "anchorNode", "getRangeAt", "startContainer", "startOffset", "endContainer", "endOffset", "textRangeEnd", "isTextNode", "createFromNodeBefore", "createFromNodeAfter", "createFromBookmark", "bookmark", "createFromParaBookmark", "KEY_MAP", "isEdit", "keyCode", "BACKSPACE", "TAB", "ENTER", "SPACE", "DELETE", "isMove", "LEFT", "UP", "RIGHT", "DOWN", "isNavigation", "HOME", "END", "PAGEUP", "PAGEDOWN", "nameFromCode", "History", "stack", "stackOffset", "$editable", "range", "snapshot", "recordUndo", "applySnapshot", "makeSnapshot", "historyLimit", "shift", "Style", "$obj", "propertyNames", "propertyName", "css", "styleInfo", "jQueryCSS", "fontSize", "parseInt", "expandClosestSibling", "onlyPartialContains", "nodesInRange", "tails", "elem", "$cont", "fromNode", "queryCommandState", "queryCommandValue", "isUnordered", "lineHeight", "toFixed", "anchor", "Bullet", "toggleList", "clustereds", "previousList", "findList", "wrapList", "appendToPrevious", "releaseList", "listName", "paraBookmark", "<PERSON><PERSON><PERSON><PERSON>", "diffLists", "listNode", "prevList", "nextList", "isEscapseToBody", "<PERSON><PERSON><PERSON><PERSON>", "headList", "parentItem", "newList", "findNextSiblings", "lastList", "middleList", "rootLists", "rootList", "listNodes", "Typing", "bullet", "tabsize", "tab", "nextPara", "blockquoteBreakingLevel", "emptyAnchors", "scrollIntoView", "TableResultAction", "where", "domTable", "_startPoint", "_virtualTable", "_actionCellList", "setVirtualTablePosition", "rowIndex", "cellIndex", "baseRow", "baseCell", "isRowSpan", "isColSpan", "isVirtualCell", "objPosition", "getActionCell", "virtualTableCellObj", "resultAction", "virtualRowPosition", "virtualColPosition", "recoverCellIndex", "newCellIndex", "addCellInfoToVirtual", "row", "cell", "cellHasColspan", "colSpan", "cellHasRowspan", "rowSpan", "isThisSelectedCell", "rowPos", "colPos", "rowspanNumber", "attributes", "rp", "rowspanIndex", "adjustStartPoint", "colspanNumber", "cp", "cellspanIndex", "isSelectedCell", "getDeleteResultActionToCell", "Column", "SubtractSpanCount", "Row", "isVirtual", "AddCell", "RemoveCell", "getAddResultActionToCell", "SumSpanCount", "Ignore", "getActionList", "fixedRow", "fixedCol", "actualPosition", "canContinue", "rowPosition", "colPosition", "requestAction", "Add", "Delete", "tagName", "rows", "cells", "createVirtualTable", "Table", "isShift", "nextCell", "currentTr", "trAttributes", "recoverAttributes", "actions", "idCell", "currentCell", "tdAttributes", "newTd", "removeAttr", "setAttribute", "before", "lastTrIndex", "after", "actionIndex", "resultStr", "attrList", "specified", "cellPos", "virtualPosition", "virtualTable", "hasRowspan", "nextRow", "cloneRow", "removeAttribute", "col<PERSON>ount", "rowCount", "tdHTML", "tds", "idxCol", "trHTML", "trs", "idxRow", "$table", "tableClassName", "Editor", "$editor", "<PERSON><PERSON><PERSON><PERSON>", "typing", "escape", "untab", "insertParagraph", "insertOrderedList", "insertUnorderedList", "formatPara", "insertHorizontalRule", "commands", "sCmd", "beforeCommand", "execCommand", "afterCommand", "wrapCommand", "fontStyling", "unit", "currentStyle", "fontSizeUnit", "formatBlock", "isLimited", "getLastRange", "setLastRange", "insertText", "textNode", "pasteHTML", "onApplyCustomStyle", "onFormatBlock", "hrNode", "stylePara", "createLink", "linkInfo", "linkUrl", "linkText", "isNewWindow", "checkProtocol", "additionalTextLength", "isTextChanged", "onCreateLink", "defaultProtocol", "anchors", "styleNodes", "createRangeFromList", "colorInfo", "foreColor", "backColor", "insertTable", "dim", "dimension", "createTable", "removeMedia", "restore<PERSON>arget", "detach", "floatMe", "toggleClass", "resize", "hasKeyShortCut", "isDefaultPrevented", "handleKeyMap", "preventDefaultEditableShortCuts", "recordEveryKeystroke", "spell<PERSON>heck", "disable<PERSON><PERSON><PERSON>", "airMode", "overrideContextMenu", "outerWidth", "maxHeight", "minHeight", "keyMap", "metaKey", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "keyName", "eventName", "tabDisable", "pad", "maxText<PERSON>ength", "lst", "thenCollapse", "commit", "styleWithCSS", "isPreventTrigger", "normalizeContent", "tabSize", "insertTab", "src", "param", "Deferred", "deferred", "$img", "one", "resolve", "reject", "display", "appendTo", "promise", "then", "$image", "min", "show", "files", "file", "filename", "maximumImageFileSize", "FileReader", "onload", "dataURL", "onerror", "err", "readAsDataURL", "readFileAsDataURL", "insertImage", "onImageUpload", "insertImagesAsDataURL", "currentRange", "spans", "firstSpan", "noteStatusOutput", "expand", "$anchor", "addRow", "addCol", "deleteRow", "deleteCol", "deleteTable", "bKeepRatio", "imageSize", "newRatio", "y", "x", "ratio", "is", "hasFocus", "Clipboard", "pasteByEvent", "clipboardData", "originalEvent", "items", "kind", "getAsFile", "getData", "Dropzone", "$eventListener", "documentEventHandlers", "$dropzone", "prependTo", "disableDragAndDrop", "onDrop", "attachDragAndDropEvent", "$dropzoneMessage", "onDragenter", "isCodeview", "hasEditorSize", "add", "onDragleave", "removeClass", "dataTransfer", "types", "content", "substr", "CodeView", "$codable", "CodeMirrorConstructor", "CodeMirror", "codemirror", "getDoc", "setValue", "save", "ESCAPE", "deactivate", "activate", "codeviewFilter", "codeviewFilterRegex", "codeviewIframeFilter", "whitelist", "codeviewIframeWhitelistSrc", "codeviewIframeWhitelistSrcBase", "tag", "RegExp", "prettifyHtml", "cmEditor", "fromTextArea", "tern", "server", "TernServer", "ternServer", "cm", "updateArgHints", "getValue", "setSize", "toTextArea", "purify", "isChange", "Statusbar", "$statusbar", "statusbar", "disableResizeEditor", "stopPropagation", "editableTop", "onMouseMove", "clientY", "minheight", "max", "Fullscreen", "$toolbar", "toolbar", "$window", "$scrollbar", "onResize", "resizeTo", "h", "setsize", "isFullscreen", "<PERSON><PERSON>", "$editingArea", "editingArea", "we", "update", "$handle", "disableResizeImage", "posStart", "clientX", "isImage", "$selection", "w", "origImageObj", "Image", "sizingText", "linkPattern", "AutoLink", "handleKeyup", "handleKeydown", "lastWordRange", "keyword", "urlText", "showDomainOnlyForAutolink", "linkTargetBlank", "wordRange", "getWordRange", "AutoSync", "AutoReplace", "PERIOD", "COMMA", "SEMICOLON", "SLASH", "previousKeydownCode", "lastWord", "j<PERSON><PERSON><PERSON>", "Node", "Placeholder", "inheritPlaceholder", "isShow", "toggle", "Buttons", "invertedKeyMap", "editor<PERSON><PERSON><PERSON>", "button", "addToolbarButtons", "addImagePopoverButtons", "addLinkPopoverButtons", "addTablePopoverButtons", "fontInstalledMap", "fontNamesIgnoreCheck", "buttonGroup", "icon", "$button", "currentTarget", "$recentColor", "colorButton", "dropdownButtonContents", "dropdown", "$dropdown", "$holder", "palette", "colors", "colorsName", "customColors", "change", "$chip", "$picker", "$palette", "prepend", "$color", "$currentButton", "magic", "styleTags", "title", "template", "styleIdx", "styleLen", "representShortcut", "createInvokeHandlerAndUpdateState", "eraser", "addDefaultFonts", "fontname", "isFontDeservedToAdd", "fontNames", "dropdownCheck", "checkClassName", "menuCheck", "fontSizes", "fontSizeUnits", "colorPalette", "unorderedlist", "orderedlist", "justifyLeft", "alignLeft", "justifyCenter", "alignCenter", "justifyRight", "alignRight", "justifyFull", "alignJustify", "textHeight", "lineHeights", "insertTableMaxSize", "col", "mousedown", "tableMoveHandler", "picture", "minus", "arrowsAlt", "question", "rollback", "trash", "rowAbove", "rowBelow", "colBefore", "colAfter", "rowRemove", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "groups", "groupIdx", "groupLen", "group", "groupName", "$group", "btn", "updateBtnStates", "$item", "isChecked", "infos", "selector", "toggleBtnActive", "posOffset", "$dimensionDisplay", "$catcher", "$highlighted", "$unhighlighted", "offsetX", "pos<PERSON><PERSON><PERSON>", "pageX", "pageY", "offsetY", "ceil", "<PERSON><PERSON><PERSON>", "isFollowing", "followScroll", "toolbarContainer", "changeContainer", "followingToolbar", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "toolbarHeight", "statusbarHeight", "otherBarHeight", "otherStaticBar", "currentOffset", "editorOffsetTop", "activateOffset", "deactivateOffsetBottom", "marginTop", "zIndex", "isIncludeCodeview", "$btn", "toggleBtn", "LinkDialog", "$body", "dialogsInBody", "disableLinkTarget", "checkbox", "checked", "footer", "$dialog", "dialog", "fade", "dialogsFade", "hideDialog", "$input", "$linkBtn", "$linkText", "$linkUrl", "$openInNewWindow", "$useProtocol", "onDialogShown", "toggleLinkBtn", "bindEnter<PERSON>ey", "isNewWindowChecked", "prop", "useProtocolChecked", "onDialogHidden", "state", "showDialog", "showLinkDialog", "LinkPopover", "popover", "$popover", "$content", "href", "containerOffset", "ImageDialog", "imageLimitation", "floor", "log", "readableSize", "pow", "showImageDialog", "onImageLinkInsert", "$imageInput", "$imageUrl", "$imageBtn", "replaceWith", "ImagePopover", "popatmouse", "TablePopover", "VideoDialog", "$video", "ytMatch", "igMatch", "vMatch", "vimMatch", "dmMatch", "youkuMatch", "qqMatch", "qqMatch2", "mp4Match", "oggMatch", "webmMatch", "fbMatch", "youtubeId", "start", "ytMatchForStart", "vid", "encodeURIComponent", "showVideoDialog", "createVideoNode", "$videoUrl", "$videoBtn", "HelpDialog", "createShortcutList", "command", "$row", "showHelpDialog", "AirPopover", "hidable", "onContextmenu", "air", "forcelyOpen", "<PERSON>nt<PERSON><PERSON><PERSON>", "hint", "direction", "hintDirection", "hints", "matching<PERSON><PERSON>", "hideArrow", "innerHeight", "$current", "$next", "selectItem", "$nextGroup", "$prev", "$prevGroup", "nodeFromItem", "rangeCompute", "hintSelect", "hintIdx", "moveUp", "moveDown", "search", "searchKeyword", "createItemTemplates", "hintMode", "getWordsRange", "getWordsMatchRange", "empty", "bnd", "createGroup", "version", "Codeview", "toolbarPosition", "codeviewKeepButton", "tabDisabled", "textareaAutoSync", "onBeforeCommand", "onBlur", "onBlurCodeview", "onChange", "onChangeCodeview", "onEnter", "onFocus", "onImageUploadError", "onInit", "onKeydown", "onKeyup", "onMousedown", "onMouseup", "onPaste", "onScroll", "htmlMode", "lineNumbers", "pc", "mac", "renderer", "airEditor", "airEditable", "option", "iconClassName", "editorOptions", "rowSize", "colSize", "colorName", "placement", "codeviewButton", "isEnable", "isActive", "modal", "interface"], "mappings": ";CAAA,SAA2CA,EAAMC,GAChD,GAAsB,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,EAAQG,QAAQ,gBAC7B,GAAqB,mBAAXC,QAAyBA,OAAOC,IAC9CD,OAAO,CAAC,UAAWJ,OACf,CACJ,IAAIM,EAAuB,iBAAZL,QAAuBD,EAAQG,QAAQ,WAAaH,EAAQD,EAAa,QACxF,IAAI,IAAIQ,KAAKD,GAAuB,iBAAZL,QAAuBA,QAAUF,GAAMQ,GAAKD,EAAEC,IAPxE,CASGC,QAAQ,SAASC,GACpB,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUX,QAGnC,IAAIC,EAASQ,EAAiBE,GAAY,CACzCL,EAAGK,EACHC,GAAG,EACHZ,QAAS,IAUV,OANAa,EAAQF,GAAUG,KAAKb,EAAOD,QAASC,EAAQA,EAAOD,QAASU,GAG/DT,EAAOW,GAAI,EAGJX,EAAOD,QA0Df,OArDAU,EAAoBK,EAAIF,EAGxBH,EAAoBM,EAAIP,EAGxBC,EAAoBO,EAAI,SAASjB,EAASkB,EAAMC,GAC3CT,EAAoBU,EAAEpB,EAASkB,IAClCG,OAAOC,eAAetB,EAASkB,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhET,EAAoBe,EAAI,SAASzB,GACX,oBAAX0B,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAetB,EAAS0B,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAetB,EAAS,aAAc,CAAE4B,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBO,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAASnC,GAChC,IAAIkB,EAASlB,GAAUA,EAAO8B,WAC7B,WAAwB,OAAO9B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAS,EAAoBO,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRT,EAAoBU,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG5B,EAAoB+B,EAAI,GAIjB/B,EAAoBA,EAAoBgC,EAAI,I,kBClFrDzC,EAAOD,QAAUQ,G,kcCEXmC,E,WACJ,WAAYC,EAAQC,EAAUC,EAASC,I,4FAAU,SAC/CC,KAAKJ,OAASA,EACdI,KAAKH,SAAWA,EAChBG,KAAKF,QAAUA,EACfE,KAAKD,SAAWA,E,sDAGXE,GACL,IAAMC,EAAQC,IAAEH,KAAKJ,QAoBrB,GAlBII,KAAKF,SAAWE,KAAKF,QAAQM,UAC/BF,EAAMG,KAAKL,KAAKF,QAAQM,UAGtBJ,KAAKF,SAAWE,KAAKF,QAAQQ,WAC/BJ,EAAMK,SAASP,KAAKF,QAAQQ,WAG1BN,KAAKF,SAAWE,KAAKF,QAAQU,MAC/BL,IAAEM,KAAKT,KAAKF,QAAQU,MAAM,SAACE,EAAGC,GAC5BT,EAAMU,KAAK,QAAUF,EAAGC,MAIxBX,KAAKF,SAAWE,KAAKF,QAAQe,OAC/BX,EAAMY,GAAG,QAASd,KAAKF,QAAQe,OAG7Bb,KAAKH,SAAU,CACjB,IAAMkB,EAAab,EAAMc,KAAK,4BAC9BhB,KAAKH,SAASoB,SAAQ,SAACC,GACrBA,EAAMC,OAAOJ,EAAWK,OAASL,EAAab,MAgBlD,OAZIF,KAAKD,UACPC,KAAKD,SAASG,EAAOF,KAAKF,SAGxBE,KAAKF,SAAWE,KAAKF,QAAQC,UAC/BC,KAAKF,QAAQC,SAASG,GAGpBD,GACFA,EAAQoB,OAAOnB,GAGVA,O,gCAII,KACbjB,OAAQ,SAACW,EAAQG,GACf,OAAO,WACL,IAAMD,EAAkC,WAAxB,EAAOwB,UAAU,IAAkBA,UAAU,GAAKA,UAAU,GACxEzB,EAAW0B,MAAMC,QAAQF,UAAU,IAAMA,UAAU,GAAK,GAI5D,OAHIxB,GAAWA,EAAQD,WACrBA,EAAWC,EAAQD,UAEd,IAAIF,EAASC,EAAQC,EAAUC,EAASC,O,iBC9DrD,YACA9C,EAAOD,QAAUyE,I,kECCjBtB,IAAEuB,WAAavB,IAAEuB,YAAc,CAC7BC,KAAM,IAGRxB,IAAEyB,OAAOzB,IAAEuB,WAAWC,KAAM,CAC1B,QAAS,CACPE,KAAM,CACJC,KAAM,OACNC,OAAQ,SACRC,UAAW,YACXC,MAAO,oBACPC,OAAQ,cACRhE,KAAM,cACNiE,cAAe,gBACfC,UAAW,YACXC,YAAa,cACbC,KAAM,YACNC,SAAU,kBAEZC,MAAO,CACLA,MAAO,UACPC,OAAQ,eACRC,WAAY,cACZC,WAAY,cACZC,cAAe,iBACfC,WAAY,gBACZC,UAAW,aACXC,WAAY,cACZC,UAAW,eACXC,aAAc,iBACdC,YAAa,gBACbC,eAAgB,mBAChBC,UAAW,cACXC,cAAe,0BACfC,UAAW,qBACXC,gBAAiB,oBACjBC,gBAAiB,oBACjBC,qBAAsB,8BACtBC,IAAK,YACLC,OAAQ,eACRC,SAAU,YAEZC,MAAO,CACLA,MAAO,QACPC,UAAW,aACXrB,OAAQ,eACRiB,IAAK,YACLK,UAAW,2DAEbC,KAAM,CACJA,KAAM,OACNvB,OAAQ,cACRwB,OAAQ,SACRC,KAAM,OACNC,cAAe,kBACfT,IAAK,mCACLU,gBAAiB,qBACjBC,YAAa,wBAEfC,MAAO,CACLA,MAAO,QACPC,YAAa,gBACbC,YAAa,gBACbC,WAAY,kBACZC,YAAa,mBACbC,OAAQ,aACRC,OAAQ,gBACRC,SAAU,gBAEZC,GAAI,CACFrC,OAAQ,0BAEVsC,MAAO,CACLA,MAAO,QACPtF,EAAG,SACHuF,WAAY,QACZC,IAAK,OACLC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,YAENC,MAAO,CACLC,UAAW,iBACXC,QAAS,gBAEX5F,QAAS,CACP6F,KAAM,OACNC,WAAY,cACZC,SAAU,aAEZC,UAAW,CACTA,UAAW,YACXC,QAAS,UACTC,OAAQ,SACRC,KAAM,aACNC,OAAQ,eACRC,MAAO,cACPC,QAAS,gBAEXC,MAAO,CACLC,OAAQ,eACRC,KAAM,aACNC,WAAY,mBACZC,WAAY,aACZC,YAAa,cACbC,eAAgB,kBAChBC,MAAO,QACPC,eAAgB,mBAChBC,SAAU,UAEZC,SAAU,CACRC,UAAW,qBACXC,MAAO,QACPC,eAAgB,kBAChBC,OAAQ,SACRC,oBAAqB,uBACrBC,cAAe,iBACfC,UAAW,cAEb3B,KAAM,CACJ,OAAU,SACV,gBAAmB,mBACnB,KAAQ,wBACR,KAAQ,wBACR,IAAO,MACP,MAAS,QACT,KAAQ,mBACR,OAAU,qBACV,UAAa,wBACb,cAAiB,4BACjB,aAAgB,gBAChB,YAAe,iBACf,cAAiB,mBACjB,aAAgB,kBAChB,YAAe,iBACf,oBAAuB,wBACvB,kBAAqB,sBACrB,QAAW,+BACX,OAAU,8BACV,WAAc,sDACd,SAAY,sCACZ,SAAY,sCACZ,SAAY,sCACZ,SAAY,sCACZ,SAAY,sCACZ,SAAY,sCACZ,qBAAwB,yBACxB,kBAAmB,oBAErB4B,QAAS,CACPC,KAAM,OACNC,KAAM,QAERC,YAAa,CACXA,YAAa,qBACbC,OAAQ,6BAEVC,OAAQ,CACNC,YAAa,yBClKnB,IAAMC,EAAiC,mBAAX3K,QAAyBA,KAQ/C4K,EAAsB,CAAC,aAAc,QAAS,YAAa,UAAW,WAE5E,SAASC,EAAcC,GACrB,OAAoE,IAA5D9H,IAAE+H,QAAQD,EAASE,cAAeJ,GAAnC,WAAsEE,EAAtE,KAAoFA,EAoB7F,IAEIG,EAFEC,EAAYC,UAAUD,UACtBE,EAAS,gBAAgBC,KAAKH,GAEpC,GAAIE,EAAQ,CACV,IAAIE,EAAU,mBAAmBC,KAAKL,GAClCI,IACFL,EAAiBO,WAAWF,EAAQ,MAEtCA,EAAU,sCAAsCC,KAAKL,MAEnDD,EAAiBO,WAAWF,EAAQ,KAIxC,IAAMG,EAAS,YAAYJ,KAAKH,GAE1BQ,EACF,iBAAkBtL,QAClB+K,UAAUQ,eAAiB,GAC3BR,UAAUS,iBAAmB,EAI3BC,EAAkBT,EAAU,8DAAgE,QAUnF,GACbU,MAAOX,UAAUY,WAAWC,QAAQ,QAAU,EAC9CZ,SACAK,SACAQ,MAAOR,GAAU,WAAWJ,KAAKH,GACjCgB,UAAW,aAAab,KAAKH,GAC7BiB,UAAWV,GAAU,UAAUJ,KAAKH,GACpCkB,UAAWX,GAAU,UAAUJ,KAAKH,GACpCmB,UAAWZ,GAAU,UAAUJ,KAAKH,KAAgB,UAAUG,KAAKH,GACnED,iBACAqB,cAAed,WAAWxI,IAAEuJ,GAAGC,QAC/B7B,eACAe,iBACAe,gBA/DF,SAAyB3B,GACvB,IAAM4B,EAA4B,kBAAb5B,EAA+B,cAAgB,gBAKhE6B,EADSC,SAASC,cAAc,UACfC,WAAW,MAEhCH,EAAQjI,KAAOqI,UAAkBL,EAAe,IAChD,IAAMM,EAAgBL,EAAQM,YAPb,mBAOmCC,MAKpD,OAHAP,EAAQjI,KAAOqI,SAAiBlC,EAAcC,GAAY,MAAQ4B,EAAe,IAG1EM,IAFOL,EAAQM,YAVL,mBAU2BC,OAoD5CC,oBAAqBP,SAASQ,YAC9BvB,iBACAjB,sBACAC,iBC1BF,IAAIwC,EAAY,EA8GD,OACbC,GA7JF,SAAYC,GACV,OAAO,SAASC,GACd,OAAOD,IAAUC,IA4JnBC,IAxJF,SAAaF,EAAOC,GAClB,OAAOD,IAAUC,GAwJjBE,KArJF,SAAcC,GACZ,OAAO,SAASJ,EAAOC,GACrB,OAAOD,EAAMI,KAAcH,EAAMG,KAoJnCC,GAhJF,WACE,OAAO,GAgJPC,KA7IF,WACE,OAAO,GA6IPC,KA9HF,SAAc5N,GACZ,OAAOA,GA8HP6N,IA3IF,SAAaC,GACX,OAAO,WACL,OAAQA,EAAEC,MAAMD,EAAG7J,aA0IrB+J,IAtIF,SAAaC,EAAIC,GACf,OAAO,SAASC,GACd,OAAOF,EAAGE,IAASD,EAAGC,KAqIxBC,OA7HF,SAAgBC,EAAKC,GACnB,OAAO,WACL,OAAOD,EAAIC,GAAQP,MAAMM,EAAKpK,aA4HhCsK,cAlHF,WACEpB,EAAY,GAkHZqB,SA1GF,SAAkBC,GAChB,IAAMC,IAAOvB,EAAY,GACzB,OAAOsB,EAASA,EAASC,EAAKA,GAyG9BC,SAzFF,SAAkBC,GAChB,IAAMC,EAAY/L,IAAE4J,UACpB,MAAO,CACLoC,IAAKF,EAAKE,IAAMD,EAAUE,YAC1BnG,KAAMgG,EAAKhG,KAAOiG,EAAUG,aAC5BhC,MAAO4B,EAAK9F,MAAQ8F,EAAKhG,KACzB/D,OAAQ+J,EAAKK,OAASL,EAAKE,MAoF7BI,aA3EF,SAAsBb,GACpB,IAAMc,EAAW,GACjB,IAAK,IAAMtN,KAAOwM,EACZrN,OAAOkB,UAAUC,eAAe1B,KAAK4N,EAAKxM,KAC5CsN,EAASd,EAAIxM,IAAQA,GAGzB,OAAOsN,GAqEPC,iBA7DF,SAA0BC,EAAWZ,GAEnC,OADAA,EAASA,GAAU,IACHY,EAAUC,MAAM,KAAKC,KAAI,SAAS1O,GAChD,OAAOA,EAAK2O,UAAU,EAAG,GAAGC,cAAgB5O,EAAK2O,UAAU,MAC1DE,KAAK,KA0DRC,SA7CF,SAAkBC,EAAMC,EAAMC,GAC5B,IAAIC,EACJ,OAAO,WACL,IAAMtD,EAAU9J,KACVqN,EAAO/L,UACPgM,EAAQ,WACZF,EAAU,KACLD,GACHF,EAAK7B,MAAMtB,EAASuD,IAGlBE,EAAUJ,IAAcC,EAC9BI,aAAaJ,GACbA,EAAUK,WAAWH,EAAOJ,GACxBK,GACFN,EAAK7B,MAAMtB,EAASuD,KA+BxBK,WArBF,SAAoBhK,GAElB,MADmB,6EACD8E,KAAK9E,KC5JzB,SAASiK,EAAKC,GACZ,OAAOA,EAAM,GAQf,SAASC,EAAKD,GACZ,OAAOA,EAAMA,EAAMxM,OAAS,GAiB9B,SAAS0M,EAAKF,GACZ,OAAOA,EAAMG,MAAM,GA8BrB,SAASC,EAASJ,EAAOpC,GACvB,GAAIoC,GAASA,EAAMxM,QAAUoK,EAAM,CACjC,GAAIoC,EAAMzE,QACR,OAAgC,IAAzByE,EAAMzE,QAAQqC,GAChB,GAAIoC,EAAMI,SAEf,OAAOJ,EAAMI,SAASxC,GAG1B,OAAO,EAyHM,OACbmC,OACAE,OACAI,QA7KF,SAAiBL,GACf,OAAOA,EAAMG,MAAM,EAAGH,EAAMxM,OAAS,IA6KrC0M,OACAI,KArBF,SAAcN,EAAOpC,GACnB,GAAIoC,GAASA,EAAMxM,QAAUoK,EAAM,CACjC,IAAM2C,EAAMP,EAAMzE,QAAQqC,GAC1B,OAAgB,IAAT2C,EAAa,KAAOP,EAAMO,EAAM,GAEzC,OAAO,MAiBPC,KAlCF,SAAcR,EAAOpC,GACnB,GAAIoC,GAASA,EAAMxM,QAAUoK,EAAM,CACjC,IAAM2C,EAAMP,EAAMzE,QAAQqC,GAC1B,OAAgB,IAAT2C,EAAa,KAAOP,EAAMO,EAAM,GAEzC,OAAO,MA8BPnN,KAjKF,SAAc4M,EAAOS,GACnB,IAAK,IAAIF,EAAM,EAAGG,EAAMV,EAAMxM,OAAQ+M,EAAMG,EAAKH,IAAO,CACtD,IAAM3C,EAAOoC,EAAMO,GACnB,GAAIE,EAAK7C,GACP,OAAOA,IA8JXwC,WACAO,IAvJF,SAAaX,EAAOS,GAClB,IAAK,IAAIF,EAAM,EAAGG,EAAMV,EAAMxM,OAAQ+M,EAAMG,EAAKH,IAC/C,IAAKE,EAAKT,EAAMO,IACd,OAAO,EAGX,OAAO,GAkJPK,IA1HF,SAAaZ,EAAOlE,GAElB,OADAA,EAAKA,GAAMuD,EAAKhC,KACT2C,EAAMa,QAAO,SAASC,EAAM/N,GACjC,OAAO+N,EAAOhF,EAAG/I,KAChB,IAuHHgO,KAhHF,SAAcC,GAIZ,IAHA,IAAMC,EAAS,GACTzN,EAASwN,EAAWxN,OACtB+M,GAAO,IACFA,EAAM/M,GACbyN,EAAOV,GAAOS,EAAWT,GAE3B,OAAOU,GA0GPC,QApGF,SAAiBlB,GACf,OAAQA,IAAUA,EAAMxM,QAoGxB2N,UA1FF,SAAmBnB,EAAOlE,GACxB,OAAKkE,EAAMxM,OACG0M,EAAKF,GACNa,QAAO,SAASC,EAAM/N,GACjC,IAAMqO,EAAQnB,EAAKa,GAMnB,OALIhF,EAAGmE,EAAKmB,GAAQrO,GAClBqO,EAAMA,EAAM5N,QAAUT,EAEtB+N,EAAKA,EAAKtN,QAAU,CAACT,GAEhB+N,IACN,CAAC,CAACf,EAAKC,MAVkB,IA0F5BqB,QAvEF,SAAiBrB,GAEf,IADA,IAAMsB,EAAU,GACPf,EAAM,EAAGG,EAAMV,EAAMxM,OAAQ+M,EAAMG,EAAKH,IAC3CP,EAAMO,IAAQe,EAAQC,KAAKvB,EAAMO,IAEvC,OAAOe,GAmEPE,OA3DF,SAAgBxB,GAGd,IAFA,IAAMyB,EAAU,GAEPlB,EAAM,EAAGG,EAAMV,EAAMxM,OAAQ+M,EAAMG,EAAKH,IAC1CH,EAASqB,EAASzB,EAAMO,KAC3BkB,EAAQF,KAAKvB,EAAMO,IAIvB,OAAOkB,IC3JHC,EAAYC,OAAOC,aAAa,KAWtC,SAASC,EAAWC,GAClB,OAAOA,GAAQvP,IAAEuP,GAAMC,SAAS,iBAuBlC,SAASC,EAAmBC,GAE1B,OADAA,EAAWA,EAAS/C,cACb,SAAS4C,GACd,OAAOA,GAAQA,EAAKG,SAAS/C,gBAAkB+C,GAYnD,SAASC,EAAOJ,GACd,OAAOA,GAA0B,IAAlBA,EAAKK,SAmBtB,SAASC,EAAON,GACd,OAAOA,GAAQ,2DAA2DlH,KAAKkH,EAAKG,SAAS/C,eAG/F,SAASmD,EAAOP,GACd,OAAID,EAAWC,KAKRA,GAAQ,sBAAsBlH,KAAKkH,EAAKG,SAAS/C,gBAO1D,IAAMoD,EAAQN,EAAmB,OAE3BO,EAAOP,EAAmB,MAMhC,IAAMQ,EAAUR,EAAmB,SAE7BS,EAAST,EAAmB,QAElC,SAASU,EAASZ,GAChB,QAAQa,EAAgBb,IAChBc,EAAOd,IACPe,EAAKf,IACLO,EAAOP,IACPU,EAAQV,IACRgB,EAAahB,IACbW,EAAOX,IAGjB,SAASc,EAAOd,GACd,OAAOA,GAAQ,UAAUlH,KAAKkH,EAAKG,SAAS/C,eAG9C,IAAM2D,EAAOb,EAAmB,MAEhC,SAASe,EAAOjB,GACd,OAAOA,GAAQ,UAAUlH,KAAKkH,EAAKG,SAAS/C,eAG9C,IAAM4D,EAAed,EAAmB,cAExC,SAASW,EAAgBb,GACvB,OAAOiB,EAAOjB,IAASgB,EAAahB,IAASD,EAAWC,GAG1D,IAAMkB,EAAWhB,EAAmB,KAUpC,IAAMiB,EAASjB,EAAmB,QAwClC,IAAMkB,EAAYC,EAAIxI,QAAUwI,EAAI3I,eAAiB,GAAK,SAAW,OASrE,SAAS4I,EAAWtB,GAClB,OAAII,EAAOJ,GACFA,EAAKuB,UAAU7P,OAGpBsO,EACKA,EAAKwB,WAAW9P,OAGlB,EAuBT,SAAS0N,EAAQY,GACf,IAAMpB,EAAM0C,EAAWtB,GAEvB,OAAY,IAARpB,KAEQwB,EAAOJ,IAAiB,IAARpB,GAAaoB,EAAKyB,YAAcL,MAGjDtL,EAAM+I,IAAImB,EAAKwB,WAAYpB,IAA8B,KAAnBJ,EAAKyB,YAWxD,SAASC,EAAiB1B,GACnBM,EAAON,IAAUsB,EAAWtB,KAC/BA,EAAKyB,UAAYL,GAUrB,SAASO,EAAS3B,EAAMrB,GACtB,KAAOqB,GAAM,CACX,GAAIrB,EAAKqB,GAAS,OAAOA,EACzB,GAAID,EAAWC,GAAS,MAExBA,EAAOA,EAAK4B,WAEd,OAAO,KA4BT,SAASC,EAAa7B,EAAMrB,GAC1BA,EAAOA,GAAQpB,EAAKjC,KAEpB,IAAMwG,EAAY,GAQlB,OAPAH,EAAS3B,GAAM,SAAS+B,GAKtB,OAJKhC,EAAWgC,IACdD,EAAUrC,KAAKsC,GAGVpD,EAAKoD,MAEPD,EAiDT,SAASE,EAAShC,EAAMrB,GACtBA,EAAOA,GAAQpB,EAAKjC,KAGpB,IADA,IAAM2G,EAAQ,GACPjC,IACDrB,EAAKqB,IACTiC,EAAMxC,KAAKO,GACXA,EAAOA,EAAKkC,YAEd,OAAOD,EAiDT,SAASE,EAAYnC,EAAMoC,GACzB,IAAM1D,EAAO0D,EAAUF,YACnBG,EAASD,EAAUR,WAMvB,OALIlD,EACF2D,EAAOC,aAAatC,EAAMtB,GAE1B2D,EAAOE,YAAYvC,GAEdA,EAST,SAASwC,EAAiBxC,EAAMyC,GAI9B,OAHAhS,IAAEM,KAAK0R,GAAQ,SAAShE,EAAKjN,GAC3BwO,EAAKuC,YAAY/Q,MAEZwO,EAST,SAAS0C,EAAgBC,GACvB,OAAwB,IAAjBA,EAAMC,OASf,SAASC,EAAiBF,GACxB,OAAOA,EAAMC,SAAWtB,EAAWqB,EAAM3C,MAS3C,SAAS8C,EAAYH,GACnB,OAAOD,EAAgBC,IAAUE,EAAiBF,GAUpD,SAASI,EAAa/C,EAAM2B,GAC1B,KAAO3B,GAAQA,IAAS2B,GAAU,CAChC,GAAuB,IAAnBqB,GAAShD,GACX,OAAO,EAETA,EAAOA,EAAK4B,WAGd,OAAO,EAUT,SAASqB,GAAcjD,EAAM2B,GAC3B,IAAKA,EACH,OAAO,EAET,KAAO3B,GAAQA,IAAS2B,GAAU,CAChC,GAAIqB,GAAShD,KAAUsB,EAAWtB,EAAK4B,YAAc,EACnD,OAAO,EAET5B,EAAOA,EAAK4B,WAGd,OAAO,EA4BT,SAASoB,GAAShD,GAEhB,IADA,IAAI4C,EAAS,EACL5C,EAAOA,EAAKkD,iBAClBN,GAAU,EAEZ,OAAOA,EAGT,SAASO,GAAYnD,GACnB,SAAUA,GAAQA,EAAKwB,YAAcxB,EAAKwB,WAAW9P,QAUvD,SAAS0R,GAAUT,EAAOU,GACxB,IAAIrD,EACA4C,EAEJ,GAAqB,IAAjBD,EAAMC,OAAc,CACtB,GAAI7C,EAAW4C,EAAM3C,MACnB,OAAO,KAGTA,EAAO2C,EAAM3C,KAAK4B,WAClBgB,EAASI,GAASL,EAAM3C,WACfmD,GAAYR,EAAM3C,MAE3B4C,EAAStB,EADTtB,EAAO2C,EAAM3C,KAAKwB,WAAWmB,EAAMC,OAAS,KAG5C5C,EAAO2C,EAAM3C,KACb4C,EAASS,EAAoB,EAAIV,EAAMC,OAAS,GAGlD,MAAO,CACL5C,KAAMA,EACN4C,OAAQA,GAWZ,SAASU,GAAUX,EAAOU,GACxB,IAAIrD,EAAM4C,EAEV,GAAItB,EAAWqB,EAAM3C,QAAU2C,EAAMC,OAAQ,CAC3C,GAAI7C,EAAW4C,EAAM3C,MACnB,OAAO,KAGT,IAAIuD,EAAeC,GAAgBb,EAAM3C,MACrCuD,GACFvD,EAAOuD,EACPX,EAAS,IAET5C,EAAO2C,EAAM3C,KAAK4B,WAClBgB,EAASI,GAASL,EAAM3C,MAAQ,QAEzBmD,GAAYR,EAAM3C,OAC3BA,EAAO2C,EAAM3C,KAAKwB,WAAWmB,EAAMC,QACnCA,EAAS,IAET5C,EAAO2C,EAAM3C,KACb4C,EAASS,EAAoB/B,EAAWqB,EAAM3C,MAAQ2C,EAAMC,OAAS,GAGvE,MAAO,CACL5C,KAAMA,EACN4C,OAAQA,GAWZ,SAASa,GAAuBd,EAAOU,GACrC,IAAIrD,EAAM4C,EAGV,GAAIxD,EAAQuD,EAAM3C,MAIhB,MAAO,CACLA,KAJFA,EAAO2C,EAAM3C,KAAKkC,YAKhBU,OAJFA,EAAS,GAQX,GAAItB,EAAWqB,EAAM3C,QAAU2C,EAAMC,OAAQ,CAC3C,GAAI7C,EAAW4C,EAAM3C,MACnB,OAAO,KAGT,IAAIuD,EAAeC,GAAgBb,EAAM3C,MACrCuD,GACFvD,EAAOuD,EACPX,EAAS,IAET5C,EAAO2C,EAAM3C,KAAK4B,WAClBgB,EAASI,GAASL,EAAM3C,MAAQ,GAI9BD,EAAWC,KACbA,EAAO2C,EAAM3C,KAAKkC,YAClBU,EAAS,QAEN,GAAIO,GAAYR,EAAM3C,OAG3B,GADA4C,EAAS,EACLxD,EAFJY,EAAO2C,EAAM3C,KAAKwB,WAAWmB,EAAMC,SAGjC,OAAO,UAMT,GAHA5C,EAAO2C,EAAM3C,KACb4C,EAASS,EAAoB/B,EAAWqB,EAAM3C,MAAQ2C,EAAMC,OAAS,EAEjExD,EAAQY,GACV,OAAO,KAIX,MAAO,CACLA,KAAMA,EACN4C,OAAQA,GAOZ,SAASY,GAAgBE,GACvB,GAAKA,EAAOxB,aACRwB,EAAOrB,SAAWqB,EAAOxB,YAAYG,OACzC,OAAIjC,EAAOsD,EAAOxB,aAAqBwB,EAAOxB,YACvCsB,GAAgBE,EAAOxB,aAUhC,SAASyB,GAAYC,EAAQC,GAC3B,OAAOD,EAAO5D,OAAS6D,EAAO7D,MAAQ4D,EAAOhB,SAAWiB,EAAOjB,OAiKjE,SAASkB,GAAUnB,EAAOvS,GACxB,IAAI2T,EAAyB3T,GAAWA,EAAQ2T,uBAC1CC,EAAsB5T,GAAWA,EAAQ4T,oBACzCC,EAAuB7T,GAAWA,EAAQ6T,qBAOhD,GALIA,IACFF,GAAyB,GAIvBjB,EAAYH,KAAWvC,EAAOuC,EAAM3C,OAASgE,GAAsB,CACrE,GAAItB,EAAgBC,GAClB,OAAOA,EAAM3C,KACR,GAAI6C,EAAiBF,GAC1B,OAAOA,EAAM3C,KAAKkC,YAKtB,GAAI9B,EAAOuC,EAAM3C,MACf,OAAO2C,EAAM3C,KAAKkE,UAAUvB,EAAMC,QAElC,IAAMuB,EAAYxB,EAAM3C,KAAKwB,WAAWmB,EAAMC,QACxCwB,EAAQjC,EAAYQ,EAAM3C,KAAKqE,WAAU,GAAQ1B,EAAM3C,MAQ7D,OAPAwC,EAAiB4B,EAAOpC,EAASmC,IAE5BJ,IACHrC,EAAiBiB,EAAM3C,MACvB0B,EAAiB0C,IAGfH,IACE7E,EAAQuD,EAAM3C,OAChB/L,GAAO0O,EAAM3C,MAEXZ,EAAQgF,KACVnQ,GAAOmQ,GACAzB,EAAM3C,KAAKkC,aAIfkC,EAgBX,SAASE,GAAUlX,EAAMuV,EAAOvS,GAE9B,IAAM0R,EAAYD,EAAac,EAAM3C,KAAMzC,EAAKxC,GAAG3N,IAEnD,OAAK0U,EAAUpQ,OAEiB,IAArBoQ,EAAUpQ,OACZoS,GAAUnB,EAAOvS,GAGnB0R,EAAU/C,QAAO,SAASiB,EAAMqC,GAKrC,OAJIrC,IAAS2C,EAAM3C,OACjBA,EAAO8D,GAAUnB,EAAOvS,IAGnB0T,GAAU,CACf9D,KAAMqC,EACNO,OAAQ5C,EAAOgD,GAAShD,GAAQsB,EAAWe,IAC1CjS,MAbI,KA0DX,SAASb,GAAO4Q,GACd,OAAO9F,SAASC,cAAc6F,GAehC,SAASlM,GAAO+L,EAAMuE,GACpB,GAAKvE,GAASA,EAAK4B,WAAnB,CACA,GAAI5B,EAAKwE,WAAc,OAAOxE,EAAKwE,WAAWD,GAE9C,IAAMlC,EAASrC,EAAK4B,WACpB,IAAK2C,EAAe,CAElB,IADA,IAAMtC,EAAQ,GACLrU,EAAI,EAAGgR,EAAMoB,EAAKwB,WAAW9P,OAAQ9D,EAAIgR,EAAKhR,IACrDqU,EAAMxC,KAAKO,EAAKwB,WAAW5T,IAG7B,IAAK,IAAIA,EAAI,EAAGgR,EAAMqD,EAAMvQ,OAAQ9D,EAAIgR,EAAKhR,IAC3CyU,EAAOC,aAAaL,EAAMrU,GAAIoS,GAIlCqC,EAAOoC,YAAYzE,IAgDrB,IAAM0E,GAAaxE,EAAmB,YAMtC,SAAShR,GAAMsB,EAAOmU,GACpB,IAAMC,EAAMF,GAAWlU,EAAM,IAAMA,EAAMoU,MAAQpU,EAAMG,OACvD,OAAIgU,EACKC,EAAIC,QAAQ,UAAW,IAEzBD,EAiEM,QAEbhF,YAEAkF,qBA9lC2B,SAgmC3BC,MAAO3D,EAEP4D,UAAW,MAAF,OAAQ5D,EAAR,QACTlB,qBACAH,aACAkF,gBA/kCF,SAAyBjF,GACvB,OAAOA,GAAQvP,IAAEuP,GAAMC,SAAS,wBA+kChCG,SACA8E,UA1iCF,SAAmBlF,GACjB,OAAOA,GAA0B,IAAlBA,EAAKK,UA0iCpBC,SACAC,SACA4E,WAhhCF,SAAoBnF,GAClB,OAAOO,EAAOP,KAAUS,EAAKT,IAghC7BoF,UAzhCF,SAAmBpF,GACjB,OAAOA,GAAQ,UAAUlH,KAAKkH,EAAKG,SAAS/C,gBAyhC5CwD,WACAyE,QAAS9H,EAAK/B,IAAIoF,GAClB0E,aA5+BF,SAAsBtF,GACpB,OAAOY,EAASZ,KAAU2B,EAAS3B,EAAMO,IA4+BzCY,SACAoE,aAl/BF,SAAsBvF,GACpB,OAAOY,EAASZ,MAAW2B,EAAS3B,EAAMO,IAk/B1CC,QACAM,SACAJ,UACAC,SACAM,SACAD,eACAH,kBACAK,WACAsE,MAAOtF,EAAmB,OAC1BO,OACAgF,KAAMvF,EAAmB,MACzBwF,OAAQxF,EAAmB,QAC3ByF,IAAKzF,EAAmB,KACxB0F,IAAK1F,EAAmB,KACxB2F,IAAK3F,EAAmB,KACxB4F,IAAK5F,EAAmB,KACxB6F,MAAO7F,EAAmB,OAC1BwE,cACAsB,oBA17BF,SAA6BhG,GAC3B,GACE,GAA+B,OAA3BA,EAAKiG,mBAAmE,KAArCjG,EAAKiG,kBAAkBxE,UAAkB,YACxEzB,EAAOA,EAAKiG,mBAEtB,OAAO7G,EAAQY,IAs7BfZ,UACA8G,cAAe3I,EAAK5B,IAAIuF,EAAU9B,GAClC+G,iBAv/BF,SAA0BC,EAAOC,GAC/B,OAAOD,EAAMlE,cAAgBmE,GACtBD,EAAMlD,kBAAoBmD,GAs/BjCC,oBA5+BF,SAA6BtG,EAAMrB,GACjCA,EAAOA,GAAQpB,EAAKlC,GAEpB,IAAMkL,EAAW,GAQjB,OAPIvG,EAAKkD,iBAAmBvE,EAAKqB,EAAKkD,kBACpCqD,EAAS9G,KAAKO,EAAKkD,iBAErBqD,EAAS9G,KAAKO,GACVA,EAAKkC,aAAevD,EAAKqB,EAAKkC,cAChCqE,EAAS9G,KAAKO,EAAKkC,aAEdqE,GAk+BPjF,aACAoB,kBACAG,mBACAC,cACAC,eACAE,iBACAuD,kBA5pBF,SAA2B7D,EAAOhB,GAChC,OAAOe,EAAgBC,IAAUI,EAAaJ,EAAM3C,KAAM2B,IA4pB1D8E,mBAnpBF,SAA4B9D,EAAOhB,GACjC,OAAOkB,EAAiBF,IAAUM,GAAcN,EAAM3C,KAAM2B,IAmpB5DyB,aACAE,aACAG,0BACAE,eACA+C,eAteF,SAAwB/D,GACtB,GAAIvC,EAAOuC,EAAM3C,QAAUmD,GAAYR,EAAM3C,OAASZ,EAAQuD,EAAM3C,MAClE,OAAO,EAGT,IAAM2G,EAAWhE,EAAM3C,KAAKwB,WAAWmB,EAAMC,OAAS,GAChDgE,EAAYjE,EAAM3C,KAAKwB,WAAWmB,EAAMC,QAC9C,QAAM+D,IAAYrG,EAAOqG,IAAgBC,IAAatG,EAAOsG,KAge7DC,eAldF,SAAwBlE,EAAOhE,GAC7B,KAAOgE,GAAO,CACZ,GAAIhE,EAAKgE,GACP,OAAOA,EAGTA,EAAQS,GAAUT,GAGpB,OAAO,MA0cPmE,eAhcF,SAAwBnE,EAAOhE,GAC7B,KAAOgE,GAAO,CACZ,GAAIhE,EAAKgE,GACP,OAAOA,EAGTA,EAAQW,GAAUX,GAGpB,OAAO,MAwbPoE,YA/aF,SAAqBpE,GACnB,IAAKvC,EAAOuC,EAAM3C,MAChB,OAAO,EAGT,IAAMgH,EAAKrE,EAAM3C,KAAKuB,UAAU0F,OAAOtE,EAAMC,OAAS,GACtD,OAAOoE,GAAc,MAAPA,GAAcA,IAAOpH,GA0anCsH,aAjaF,SAAsBvE,GACpB,IAAKvC,EAAOuC,EAAM3C,MAChB,OAAO,EAGT,IAAMgH,EAAKrE,EAAM3C,KAAKuB,UAAU0F,OAAOtE,EAAMC,OAAS,GACtD,MAAc,MAAPoE,GAAcA,IAAOpH,GA4Z5BuH,UAjZF,SAAmBC,EAAYC,EAAUC,EAASjE,GAGhD,IAFA,IAAIV,EAAQyE,EAELzE,IACL2E,EAAQ3E,IAEJgB,GAAYhB,EAAO0E,KAHX,CAUZ1E,EAAQc,GAAuBd,EAHVU,GACF+D,EAAWpH,OAAS2C,EAAM3C,MAC1BqH,EAASrH,OAAS2C,EAAM3C,QAsY7C2B,WACA4F,oBAr5BF,SAA6BvH,EAAMrB,GAGjC,IAFAqB,EAAOA,EAAK4B,WAEL5B,GACoB,IAArBsB,EAAWtB,IADJ,CAEX,GAAIrB,EAAKqB,GAAS,OAAOA,EACzB,GAAID,EAAWC,GAAS,MAExBA,EAAOA,EAAK4B,WAEd,OAAO,MA44BPC,eACA2F,aAn3BF,SAAsBxH,EAAMrB,GAC1B,IAAMmD,EAAYD,EAAa7B,GAC/B,OAAOlK,EAAMqI,KAAK2D,EAAU2F,OAAO9I,KAk3BnCqD,WACA0F,SA51BF,SAAkB1H,EAAMrB,GACtBA,EAAOA,GAAQpB,EAAKjC,KAGpB,IADA,IAAM2G,EAAQ,GACPjC,IACDrB,EAAKqB,IACTiC,EAAMxC,KAAKO,GACXA,EAAOA,EAAKkD,gBAEd,OAAOjB,GAo1BP0F,eAzzBF,SAAwB3H,EAAMrB,GAC5B,IAAMiJ,EAAc,GAapB,OAZAjJ,EAAOA,GAAQpB,EAAKlC,GAGpB,SAAUwM,EAAOC,GACX9H,IAAS8H,GAAWnJ,EAAKmJ,IAC3BF,EAAYnI,KAAKqI,GAEnB,IAAK,IAAIrJ,EAAM,EAAGG,EAAMkJ,EAAQtG,WAAW9P,OAAQ+M,EAAMG,EAAKH,IAC5DoJ,EAAOC,EAAQtG,WAAW/C,IAL9B,CAOGuB,GAEI4H,GA4yBPG,eA52BF,SAAwB3B,EAAOC,GAE7B,IADA,IAAMvE,EAAYD,EAAauE,GACtB1W,EAAI2W,EAAO3W,EAAGA,EAAIA,EAAEkS,WAC3B,GAAIE,EAAUrI,QAAQ/J,IAAM,EAAG,OAAOA,EAExC,OAAO,MAw2BPsY,KAnyBF,SAAchI,EAAMiI,GAClB,IAAM5F,EAASrC,EAAK4B,WACdsG,EAAUzX,IAAE,IAAMwX,EAAc,KAAK,GAK3C,OAHA5F,EAAOC,aAAa4F,EAASlI,GAC7BkI,EAAQ3F,YAAYvC,GAEbkI,GA6xBP/F,cACAK,mBACAQ,YACAG,eACAgF,eAtYF,SAAwBxG,EAAU3B,GAEhC,OADkB6B,EAAa7B,EAAMzC,EAAKxC,GAAG4G,IAC5BzE,IAAI8F,IAAUoF,WAqY/BC,eA1XF,SAAwB1G,EAAU2G,GAEhC,IADA,IAAIR,EAAUnG,EACL/T,EAAI,EAAGgR,EAAM0J,EAAQ5W,OAAQ9D,EAAIgR,EAAKhR,IAE3Cka,EADEA,EAAQtG,WAAW9P,QAAU4W,EAAQ1a,GAC7Bka,EAAQtG,WAAWsG,EAAQtG,WAAW9P,OAAS,GAE/CoW,EAAQtG,WAAW8G,EAAQ1a,IAGzC,OAAOka,GAkXPxD,aACAiE,WA9QF,SAAoB5F,EAAO/B,GAIzB,IAII4H,EAAWC,EAJT9J,EAAOiC,EAAWL,EAASM,EAC3BiB,EAAYD,EAAac,EAAM3C,KAAMrB,GACrC+J,EAAc5S,EAAMqI,KAAK2D,IAAca,EAAM3C,KAG/CrB,EAAK+J,IACPF,EAAY1G,EAAUA,EAAUpQ,OAAS,GACzC+W,EAAYC,GAGZD,GADAD,EAAYE,GACU9G,WAIxB,IAAI+G,EAAQH,GAAalE,GAAUkE,EAAW7F,EAAO,CACnDoB,uBAAwBnD,EACxBoD,oBAAqBpD,IAQvB,OAJK+H,GAASF,IAAc9F,EAAM3C,OAChC2I,EAAQhG,EAAM3C,KAAKwB,WAAWmB,EAAMC,SAG/B,CACLgE,UAAW+B,EACXF,UAAWA,IAiPblZ,UACAqZ,WA1OF,SAAoBC,GAClB,OAAOxO,SAASyO,eAAeD,IA0O/B5U,UACA8U,YAvMF,SAAqB/I,EAAMrB,GACzB,KAAOqB,IACDD,EAAWC,IAAUrB,EAAKqB,IADnB,CAKX,IAAMqC,EAASrC,EAAK4B,WACpB3N,GAAO+L,GACPA,EAAOqC,IAgMTwC,QAnLF,SAAiB7E,EAAMG,GACrB,GAAIH,EAAKG,SAAS/C,gBAAkB+C,EAAS/C,cAC3C,OAAO4C,EAGT,IAAMgJ,EAAUzZ,GAAO4Q,GAUvB,OARIH,EAAK3K,MAAM4T,UACbD,EAAQ3T,MAAM4T,QAAUjJ,EAAK3K,MAAM4T,SAGrCzG,EAAiBwG,EAASlT,EAAMmJ,KAAKe,EAAKwB,aAC1CW,EAAY6G,EAAShJ,GACrB/L,GAAO+L,GAEAgJ,GAqKPrY,KA5IF,SAAcH,EAAO0Y,GACnB,IAAIhZ,EAAShB,GAAMsB,GAEnB,GAAI0Y,EAAkB,CAUpBhZ,GARAA,EAASA,EAAO2U,QADC,yCACiB,SAASsE,EAAOC,EAAU5a,GAC1DA,EAAOA,EAAK4O,cACZ,IAAMiM,EAAyB,8BAA8BvQ,KAAKtK,MACnC4a,EACzBE,EAAc,4CAA4CxQ,KAAKtK,GAErE,OAAO2a,GAAUE,GAA0BC,EAAe,KAAO,QAEnDC,OAGlB,OAAOrZ,GA6HPhB,SACAsa,mBA3HF,SAA4BC,GAC1B,IAAMC,EAAejZ,IAAEgZ,GACjBE,EAAMD,EAAa9G,SACnBpQ,EAASkX,EAAaE,aAAY,GAExC,MAAO,CACLrT,KAAMoT,EAAIpT,KACVkG,IAAKkN,EAAIlN,IAAMjK,IAqHjBqX,aAjHF,SAAsBrZ,EAAOsZ,GAC3Bnb,OAAOob,KAAKD,GAAQvY,SAAQ,SAAS/B,GACnCgB,EAAMY,GAAG5B,EAAKsa,EAAOta,QAgHvBwa,aA5GF,SAAsBxZ,EAAOsZ,GAC3Bnb,OAAOob,KAAKD,GAAQvY,SAAQ,SAAS/B,GACnCgB,EAAMyZ,IAAIza,EAAKsa,EAAOta,QA2GxB0a,iBA/FF,SAA0BlK,GACxB,OAAOA,IAASI,EAAOJ,IAASlK,EAAMwI,SAAS0B,EAAKmK,UAAW,mB,2KCxlC5CC,G,WAKnB,WAAYC,EAAOja,I,4FAAS,SAC1BE,KAAK+Z,MAAQA,EAEb/Z,KAAKga,MAAQ,GACbha,KAAKnC,QAAU,GACfmC,KAAKia,WAAa,GAClBja,KAAKF,QAAUK,IAAEyB,QAAO,EAAM,GAAI9B,GAGlCK,IAAEuB,WAAWwY,GAAK/Z,IAAEuB,WAAWyY,YAAYna,KAAKF,SAChDE,KAAKka,GAAK/Z,IAAEuB,WAAWwY,GAEvBla,KAAKoa,a,4DAUL,OAHApa,KAAKia,WAAaja,KAAKka,GAAGG,aAAara,KAAK+Z,OAC5C/Z,KAAKsa,cACLta,KAAK+Z,MAAMQ,OACJva,O,gCAOPA,KAAKwa,WACLxa,KAAK+Z,MAAMU,WAAW,cACtBza,KAAKka,GAAGQ,aAAa1a,KAAK+Z,MAAO/Z,KAAKia,c,8BAOtC,IAAMU,EAAW3a,KAAK4a,aACtB5a,KAAK6a,KAAKC,GAAIpG,WACd1U,KAAKwa,WACLxa,KAAKsa,cAEDK,GACF3a,KAAK+a,Y,oCAIK,WAEZ/a,KAAKF,QAAQiM,GAAKkB,EAAKpB,SAAS1L,IAAE6a,OAElChb,KAAKF,QAAQqY,UAAYnY,KAAKF,QAAQqY,WAAanY,KAAKia,WAAWgB,OAGnE,IAAMC,EAAU/a,IAAEyB,OAAO,GAAI5B,KAAKF,QAAQob,SAC1C7c,OAAOob,KAAKyB,GAASja,SAAQ,SAAC/B,GAC5B,EAAKwP,KAAK,UAAYxP,EAAKgc,EAAQhc,OAGrC,IAAMrB,EAAUsC,IAAEyB,OAAO,GAAI5B,KAAKF,QAAQjC,QAASsC,IAAEuB,WAAWyZ,SAAW,IAG3E9c,OAAOob,KAAK5b,GAASoD,SAAQ,SAAC/B,GAC5B,EAAKjC,OAAOiC,EAAKrB,EAAQqB,IAAM,MAGjCb,OAAOob,KAAKzZ,KAAKnC,SAASoD,SAAQ,SAAC/B,GACjC,EAAKkc,iBAAiBlc,Q,iCAIf,WAETb,OAAOob,KAAKzZ,KAAKnC,SAASia,UAAU7W,SAAQ,SAAC/B,GAC3C,EAAKmc,aAAanc,MAGpBb,OAAOob,KAAKzZ,KAAKga,OAAO/Y,SAAQ,SAAC/B,GAC/B,EAAKoc,WAAWpc,MAGlBc,KAAKub,aAAa,UAAWvb,Q,2BAG1BK,GACH,IAAMmb,EAAcxb,KAAKyL,OAAO,wBAEhC,QAAagQ,IAATpb,EAEF,OADAL,KAAKyL,OAAO,iBACL+P,EAAcxb,KAAKia,WAAWyB,QAAQpH,MAAQtU,KAAKia,WAAW0B,SAAStb,OAE1Emb,EACFxb,KAAKyL,OAAO,gBAAiBpL,GAE7BL,KAAKia,WAAW0B,SAAStb,KAAKA,GAEhCL,KAAK+Z,MAAMzF,IAAIjU,GACfL,KAAKub,aAAa,SAAUlb,EAAML,KAAKia,WAAW0B,Y,mCAKpD,MAA4D,UAArD3b,KAAKia,WAAW0B,SAAS/a,KAAK,qB,+BAIrCZ,KAAKia,WAAW0B,SAAS/a,KAAK,mBAAmB,GACjDZ,KAAKyL,OAAO,oBAAoB,GAChCzL,KAAKub,aAAa,WAAW,GAC7Bvb,KAAKF,QAAQ8b,SAAU,I,gCAKnB5b,KAAKyL,OAAO,yBACdzL,KAAKyL,OAAO,uBAEdzL,KAAKia,WAAW0B,SAAS/a,KAAK,mBAAmB,GACjDZ,KAAKF,QAAQ8b,SAAU,EACvB5b,KAAKyL,OAAO,sBAAsB,GAElCzL,KAAKub,aAAa,WAAW,K,qCAI7B,IAAM7O,EAAYlH,EAAMmI,KAAKrM,WACvB+L,EAAO7H,EAAMsI,KAAKtI,EAAMmJ,KAAKrN,YAE7BvB,EAAWC,KAAKF,QAAQ+b,UAAU5O,EAAKR,iBAAiBC,EAAW,OACrE3M,GACFA,EAASqL,MAAMpL,KAAK+Z,MAAM,GAAI1M,GAEhCrN,KAAK+Z,MAAM+B,QAAQ,cAAgBpP,EAAWW,K,uCAG/BnO,GACf,IAAMjC,EAAS+C,KAAKnC,QAAQqB,GAC5BjC,EAAO8e,iBAAmB9e,EAAO8e,kBAAoB9O,EAAKlC,GACrD9N,EAAO8e,qBAKR9e,EAAOmd,YACTnd,EAAOmd,aAILnd,EAAOuc,QACTsB,GAAIvB,aAAavZ,KAAK+Z,MAAO9c,EAAOuc,W,6BAIjCta,EAAK8c,EAAaC,GACvB,GAAyB,IAArB3a,UAAUF,OACZ,OAAOpB,KAAKnC,QAAQqB,GAGtBc,KAAKnC,QAAQqB,GAAO,IAAI8c,EAAYhc,MAE/Bic,GACHjc,KAAKob,iBAAiBlc,K,mCAIbA,GACX,IAAMjC,EAAS+C,KAAKnC,QAAQqB,GACxBjC,EAAO8e,qBACL9e,EAAOuc,QACTsB,GAAIpB,aAAa1Z,KAAK+Z,MAAO9c,EAAOuc,QAGlCvc,EAAOif,SACTjf,EAAOif,kBAIJlc,KAAKnC,QAAQqB,K,2BAGjBA,EAAKwM,GACR,GAAyB,IAArBpK,UAAUF,OACZ,OAAOpB,KAAKga,MAAM9a,GAEpBc,KAAKga,MAAM9a,GAAOwM,I,iCAGTxM,GACLc,KAAKga,MAAM9a,IAAQc,KAAKga,MAAM9a,GAAKgd,SACrClc,KAAKga,MAAM9a,GAAKgd,iBAGXlc,KAAKga,MAAM9a,K,wDAMcwN,EAAW9N,GAAO,WAClD,OAAO,SAACud,GACN,EAAKC,oBAAoB1P,EAAW9N,EAApC,CAA2Cud,GAC3C,EAAK1Q,OAAO,iC,0CAIIiB,EAAW9N,GAAO,WACpC,OAAO,SAACud,GACNA,EAAME,iBACN,IAAMC,EAAUnc,IAAEgc,EAAMI,QACxB,EAAK9Q,OAAOiB,EAAW9N,GAAS0d,EAAQE,QAAQ,gBAAgBhc,KAAK,SAAU8b,M,+BAKjF,IAAM5P,EAAYlH,EAAMmI,KAAKrM,WACvB+L,EAAO7H,EAAMsI,KAAKtI,EAAMmJ,KAAKrN,YAE7Bmb,EAAS/P,EAAUC,MAAM,KACzB+P,EAAeD,EAAOrb,OAAS,EAC/Bub,EAAaD,GAAgBlX,EAAMmI,KAAK8O,GACxCG,EAAaF,EAAelX,EAAMqI,KAAK4O,GAAUjX,EAAMmI,KAAK8O,GAE5Dxf,EAAS+C,KAAKnC,QAAQ8e,GAAc,UAC1C,OAAKA,GAAc3c,KAAK4c,GACf5c,KAAK4c,GAAYxR,MAAMpL,KAAMqN,GAC3BpQ,GAAUA,EAAO2f,IAAe3f,EAAO8e,mBACzC9e,EAAO2f,GAAYxR,MAAMnO,EAAQoQ,QADnC,O,yMC7NX,SAASwP,GAAiBC,EAAWC,GACnC,IACIzK,EAGA0K,EAJA7E,EAAY2E,EAAUG,gBAGpBC,EAASnT,SAASoT,KAAKC,kBAEvBlM,EAAa1L,EAAMmJ,KAAKwJ,EAAUjH,YACxC,IAAKoB,EAAS,EAAGA,EAASpB,EAAW9P,OAAQkR,IAC3C,IAAIwI,GAAIhL,OAAOoB,EAAWoB,IAA1B,CAIA,GADA4K,EAAOG,kBAAkBnM,EAAWoB,IAChC4K,EAAOI,iBAAiB,eAAgBR,IAAc,EACxD,MAEFE,EAAgB9L,EAAWoB,GAG7B,GAAe,IAAXA,GAAgBwI,GAAIhL,OAAOoB,EAAWoB,EAAS,IAAK,CACtD,IAAMiL,EAAiBxT,SAASoT,KAAKC,kBACjCI,EAAc,KAClBD,EAAeF,kBAAkBL,GAAiB7E,GAClDoF,EAAeE,UAAUT,GACzBQ,EAAcR,EAAgBA,EAAcpL,YAAcuG,EAAUuF,WAEpE,IAAMC,EAAcb,EAAUc,YAC9BD,EAAYE,YAAY,eAAgBN,GAGxC,IAFA,IAAIO,EAAYH,EAAYpF,KAAKhE,QAAQ,UAAW,IAAInT,OAEjD0c,EAAYN,EAAYvM,UAAU7P,QAAUoc,EAAY5L,aAC7DkM,GAAaN,EAAYvM,UAAU7P,OACnCoc,EAAcA,EAAY5L,YAId4L,EAAYvM,UAEtB8L,GAAWS,EAAY5L,aAAekJ,GAAIhL,OAAO0N,EAAY5L,cAC/DkM,IAAcN,EAAYvM,UAAU7P,SACpC0c,GAAaN,EAAYvM,UAAU7P,OACnCoc,EAAcA,EAAY5L,aAG5BuG,EAAYqF,EACZlL,EAASwL,EAGX,MAAO,CACLC,KAAM5F,EACN7F,OAAQA,GASZ,SAAS0L,GAAiB3L,GACxB,IA0BMyK,EAAY/S,SAASoT,KAAKC,kBAC1Ba,EA3BgB,SAAhBC,EAAyB/F,EAAW7F,GACxC,IAAI5C,EAAMyO,EAEV,GAAIrD,GAAIhL,OAAOqI,GAAY,CACzB,IAAMiG,EAAgBtD,GAAI1D,SAASe,EAAWlL,EAAK/B,IAAI4P,GAAIhL,SACrDkN,EAAgBxX,EAAMqI,KAAKuQ,GAAexL,gBAChDlD,EAAOsN,GAAiB7E,EAAU7G,WAClCgB,GAAU9M,EAAMgJ,IAAIhJ,EAAMsI,KAAKsQ,GAAgBtD,GAAI9J,YACnDmN,GAAqBnB,MAChB,CAEL,GADAtN,EAAOyI,EAAUjH,WAAWoB,IAAW6F,EACnC2C,GAAIhL,OAAOJ,GACb,OAAOwO,EAAcxO,EAAM,GAG7B4C,EAAS,EACT6L,GAAoB,EAGtB,MAAO,CACLzO,KAAMA,EACN2O,gBAAiBF,EACjB7L,OAAQA,GAKC4L,CAAc7L,EAAM3C,KAAM2C,EAAMC,QAK7C,OAHAwK,EAAUO,kBAAkBY,EAAKvO,MACjCoN,EAAUW,SAASQ,EAAKI,iBACxBvB,EAAUwB,UAAU,YAAaL,EAAK3L,QAC/BwK,ECrGT3c,IAAEuJ,GAAG9H,OAAO,CAOVF,WAAY,WACV,IAAM6c,EAAOpe,IAAEoe,KAAK/Y,EAAMmI,KAAKrM,YACzBkd,EAA+B,WAATD,EACtBE,EAA0B,WAATF,EAEjBze,EAAUK,IAAEyB,OAAO,GAAIzB,IAAEuB,WAAW5B,QAAS2e,EAAiBjZ,EAAMmI,KAAKrM,WAAa,IAG5FxB,EAAQ4e,SAAWve,IAAEyB,QAAO,EAAM,GAAIzB,IAAEuB,WAAWC,KAAK,SAAUxB,IAAEuB,WAAWC,KAAK7B,EAAQ6B,OAC5F7B,EAAQ6e,MAAQxe,IAAEyB,QAAO,EAAM,GAAIzB,IAAEuB,WAAW5B,QAAQ6e,MAAO7e,EAAQ6e,OACvE7e,EAAQ8e,QAA8B,SAApB9e,EAAQ8e,SAAsB7N,EAAIlI,eAAiB/I,EAAQ8e,QAE7E5e,KAAKS,MAAK,SAAC0N,EAAK0Q,GACd,IAAM9E,EAAQ5Z,IAAE0e,GAChB,IAAK9E,EAAMvZ,KAAK,cAAe,CAC7B,IAAMsJ,EAAU,IAAIgQ,GAAQC,EAAOja,GACnCia,EAAMvZ,KAAK,aAAcsJ,GACzBiQ,EAAMvZ,KAAK,cAAc+a,aAAa,OAAQzR,EAAQmQ,gBAI1D,IAAMF,EAAQ/Z,KAAK8e,QACnB,GAAI/E,EAAM3Y,OAAQ,CAChB,IAAM0I,EAAUiQ,EAAMvZ,KAAK,cAC3B,GAAIge,EACF,OAAO1U,EAAQ2B,OAAOL,MAAMtB,EAAStE,EAAMmJ,KAAKrN,YACvCxB,EAAQif,OACjBjV,EAAQ2B,OAAO,gBAInB,OAAOzL,Q,ID2ELgf,G,WACJ,WAAYC,EAAIC,EAAIC,EAAIC,I,4FAAI,SAC1Bpf,KAAKif,GAAKA,EACVjf,KAAKkf,GAAKA,EACVlf,KAAKmf,GAAKA,EACVnf,KAAKof,GAAKA,EAGVpf,KAAKqf,aAAerf,KAAKsf,SAASxE,GAAIrL,YAEtCzP,KAAKuf,SAAWvf,KAAKsf,SAASxE,GAAItK,QAElCxQ,KAAKwf,WAAaxf,KAAKsf,SAASxE,GAAIlK,UAEpC5Q,KAAKyf,SAAWzf,KAAKsf,SAASxE,GAAInK,QAElC3Q,KAAK0f,SAAW1f,KAAKsf,SAASxE,GAAIzK,Q,6DAKlC,GAAIU,EAAIzG,kBAAmB,CACzB,IAAMqV,EAAW5V,SAASQ,cAI1B,OAHAoV,EAASC,SAAS5f,KAAKif,GAAIjf,KAAKkf,IAChCS,EAASE,OAAO7f,KAAKmf,GAAInf,KAAKof,IAEvBO,EAEP,IAAM7C,EAAYkB,GAAiB,CACjCtO,KAAM1P,KAAKif,GACX3M,OAAQtS,KAAKkf,KAQf,OALApC,EAAUe,YAAY,WAAYG,GAAiB,CACjDtO,KAAM1P,KAAKmf,GACX7M,OAAQtS,KAAKof,MAGRtC,I,kCAKT,MAAO,CACLmC,GAAIjf,KAAKif,GACTC,GAAIlf,KAAKkf,GACTC,GAAInf,KAAKmf,GACTC,GAAIpf,KAAKof,M,sCAKX,MAAO,CACL1P,KAAM1P,KAAKif,GACX3M,OAAQtS,KAAKkf,M,oCAKf,MAAO,CACLxP,KAAM1P,KAAKmf,GACX7M,OAAQtS,KAAKof,M,+BAQf,IAAMU,EAAY9f,KAAK+f,cACvB,GAAIhP,EAAIzG,kBAAmB,CACzB,IAAM0V,EAAYjW,SAASkW,eACvBD,EAAUE,WAAa,GACzBF,EAAUG,kBAEZH,EAAUI,SAASN,QAEnBA,EAAUnY,SAGZ,OAAO3H,O,qCAQMmY,GACb,IAAMjW,EAAS/B,IAAEgY,GAAWjW,SAK5B,OAJIiW,EAAU/L,UAAYlK,EAASlC,KAAKif,GAAGoB,YACzClI,EAAU/L,WAAakU,KAAKC,IAAIpI,EAAU/L,UAAYlK,EAASlC,KAAKif,GAAGoB,YAGlErgB,O,kCAaP,IAAMwgB,EAAkB,SAASnO,EAAOoO,GACtC,IAAKpO,EACH,OAAOA,EAUT,GAAIyI,GAAI1E,eAAe/D,MAChByI,GAAItI,YAAYH,IAChByI,GAAIvI,iBAAiBF,KAAWoO,GAChC3F,GAAI1I,gBAAgBC,IAAUoO,GAC9B3F,GAAIvI,iBAAiBF,IAAUoO,GAAiB3F,GAAI9K,OAAOqC,EAAM3C,KAAKkC,cACtEkJ,GAAI1I,gBAAgBC,KAAWoO,GAAiB3F,GAAI9K,OAAOqC,EAAM3C,KAAKkD,kBACtEkI,GAAI/F,QAAQ1C,EAAM3C,OAASoL,GAAIhM,QAAQuD,EAAM3C,OAChD,OAAO2C,EAKX,IAAMqO,EAAQ5F,GAAIzJ,SAASgB,EAAM3C,KAAMoL,GAAI/F,SACvC4L,GAAe,EAEnB,IAAKA,EAAc,CACjB,IAAM7N,EAAYgI,GAAIhI,UAAUT,IAAU,CAAE3C,KAAM,MAClDiR,GAAgB7F,GAAI5E,kBAAkB7D,EAAOqO,IAAU5F,GAAI9K,OAAO8C,EAAUpD,SAAW+Q,EAGzF,IAAIG,GAAc,EAClB,IAAKA,EAAa,CAChB,IAAM5N,EAAY8H,GAAI9H,UAAUX,IAAU,CAAE3C,KAAM,MAClDkR,GAAe9F,GAAI3E,mBAAmB9D,EAAOqO,IAAU5F,GAAI9K,OAAOgD,EAAUtD,QAAU+Q,EAGxF,GAAIE,GAAgBC,EAAa,CAE/B,GAAI9F,GAAI1E,eAAe/D,GACrB,OAAOA,EAGToO,GAAiBA,EAKnB,OAFkBA,EAAgB3F,GAAItE,eAAesE,GAAI9H,UAAUX,GAAQyI,GAAI1E,gBAC3E0E,GAAIvE,eAAeuE,GAAIhI,UAAUT,GAAQyI,GAAI1E,kBAC7B/D,GAGhB0E,EAAWyJ,EAAgBxgB,KAAK6gB,eAAe,GAC/C/J,EAAa9W,KAAK8gB,cAAgB/J,EAAWyJ,EAAgBxgB,KAAK+gB,iBAAiB,GAEzF,OAAO,IAAI/B,EACTlI,EAAWpH,KACXoH,EAAWxE,OACXyE,EAASrH,KACTqH,EAASzE,U,4BAaPjE,EAAMvO,GACVuO,EAAOA,GAAQpB,EAAKlC,GAEpB,IAAMiW,EAAkBlhB,GAAWA,EAAQkhB,gBACrCC,EAAgBnhB,GAAWA,EAAQmhB,cAGnCnK,EAAa9W,KAAK+gB,gBAClBhK,EAAW/W,KAAK6gB,cAEhBlP,EAAQ,GACRuP,EAAgB,GA0BtB,OAxBApG,GAAIjE,UAAUC,EAAYC,GAAU,SAAS1E,GAK3C,IAAI3C,EAJAoL,GAAIrL,WAAW4C,EAAM3C,QAKrBuR,GACEnG,GAAI1I,gBAAgBC,IACtB6O,EAAc/R,KAAKkD,EAAM3C,MAEvBoL,GAAIvI,iBAAiBF,IAAU7M,EAAMwI,SAASkT,EAAe7O,EAAM3C,QACrEA,EAAO2C,EAAM3C,OAGfA,EADSsR,EACFlG,GAAIzJ,SAASgB,EAAM3C,KAAMrB,GAEzBgE,EAAM3C,KAGXA,GAAQrB,EAAKqB,IACfiC,EAAMxC,KAAKO,OAEZ,GAEIlK,EAAM4J,OAAOuC,K,uCAQpB,OAAOmJ,GAAIrD,eAAezX,KAAKif,GAAIjf,KAAKmf,M,6BASnC9Q,GACL,IAAM8S,EAAgBrG,GAAIzJ,SAASrR,KAAKif,GAAI5Q,GACtC+S,EAActG,GAAIzJ,SAASrR,KAAKmf,GAAI9Q,GAE1C,IAAK8S,IAAkBC,EACrB,OAAO,IAAIpC,EAAahf,KAAKif,GAAIjf,KAAKkf,GAAIlf,KAAKmf,GAAInf,KAAKof,IAG1D,IAAMiC,EAAiBrhB,KAAKshB,YAY5B,OAVIH,IACFE,EAAepC,GAAKkC,EACpBE,EAAenC,GAAK,GAGlBkC,IACFC,EAAelC,GAAKiC,EACpBC,EAAejC,GAAKtE,GAAI9J,WAAWoQ,IAG9B,IAAIpC,EACTqC,EAAepC,GACfoC,EAAenC,GACfmC,EAAelC,GACfkC,EAAejC,M,+BAQVjB,GACP,OAAIA,EACK,IAAIa,EAAahf,KAAKif,GAAIjf,KAAKkf,GAAIlf,KAAKif,GAAIjf,KAAKkf,IAEjD,IAAIF,EAAahf,KAAKmf,GAAInf,KAAKof,GAAIpf,KAAKmf,GAAInf,KAAKof,M,kCAQ1D,IAAMmC,EAAkBvhB,KAAKif,KAAOjf,KAAKmf,GACnCkC,EAAiBrhB,KAAKshB,YAgB5B,OAdIxG,GAAIhL,OAAO9P,KAAKmf,MAAQrE,GAAItI,YAAYxS,KAAK6gB,gBAC/C7gB,KAAKmf,GAAGvL,UAAU5T,KAAKof,IAGrBtE,GAAIhL,OAAO9P,KAAKif,MAAQnE,GAAItI,YAAYxS,KAAK+gB,mBAC/CM,EAAepC,GAAKjf,KAAKif,GAAGrL,UAAU5T,KAAKkf,IAC3CmC,EAAenC,GAAK,EAEhBqC,IACFF,EAAelC,GAAKkC,EAAepC,GACnCoC,EAAejC,GAAKpf,KAAKof,GAAKpf,KAAKkf,KAIhC,IAAIF,EACTqC,EAAepC,GACfoC,EAAenC,GACfmC,EAAelC,GACfkC,EAAejC,M,uCASjB,GAAIpf,KAAK8gB,cACP,OAAO9gB,KAGT,IAAMwhB,EAAMxhB,KAAK4T,YACXjC,EAAQ6P,EAAI7P,MAAM,KAAM,CAC5BsP,eAAe,IAIX5O,EAAQyI,GAAIvE,eAAeiL,EAAIT,iBAAiB,SAAS1O,GAC7D,OAAQ7M,EAAMwI,SAAS2D,EAAOU,EAAM3C,SAGhC+R,EAAe,GAerB,OAdAthB,IAAEM,KAAKkR,GAAO,SAASxD,EAAKuB,GAE1B,IAAMqC,EAASrC,EAAK4B,WAChBe,EAAM3C,OAASqC,GAAqC,IAA3B+I,GAAI9J,WAAWe,IAC1C0P,EAAatS,KAAK4C,GAEpB+I,GAAInX,OAAO+L,GAAM,MAInBvP,IAAEM,KAAKghB,GAAc,SAAStT,EAAKuB,GACjCoL,GAAInX,OAAO+L,GAAM,MAGZ,IAAIsP,EACT3M,EAAM3C,KACN2C,EAAMC,OACND,EAAM3C,KACN2C,EAAMC,QACNoP,c,+BAMKrT,GACP,OAAO,WACL,IAAMgD,EAAWyJ,GAAIzJ,SAASrR,KAAKif,GAAI5Q,GACvC,QAASgD,GAAaA,IAAayJ,GAAIzJ,SAASrR,KAAKmf,GAAI9Q,M,mCAQhDA,GACX,IAAKyM,GAAI1I,gBAAgBpS,KAAK+gB,iBAC5B,OAAO,EAGT,IAAMrR,EAAOoL,GAAIzJ,SAASrR,KAAKif,GAAI5Q,GACnC,OAAOqB,GAAQoL,GAAIrI,aAAazS,KAAKif,GAAIvP,K,oCAOzC,OAAO1P,KAAKif,KAAOjf,KAAKmf,IAAMnf,KAAKkf,KAAOlf,KAAKof,K,+CAS/C,GAAItE,GAAIvK,gBAAgBvQ,KAAKif,KAAOnE,GAAIhM,QAAQ9O,KAAKif,IAEnD,OADAjf,KAAKif,GAAG9N,UAAY2J,GAAIpG,UACjB,IAAIsK,EAAahf,KAAKif,GAAGvB,WAAY,EAAG1d,KAAKif,GAAGvB,WAAY,GAQrE,IAMItF,EANEoJ,EAAMxhB,KAAK0hB,YACjB,GAAI5G,GAAI7F,aAAajV,KAAKif,KAAOnE,GAAI7K,OAAOjQ,KAAKif,IAC/C,OAAOuC,EAKT,GAAI1G,GAAIxK,SAASkR,EAAIvC,IAAK,CACxB,IAAMzN,EAAYsJ,GAAIvJ,aAAaiQ,EAAIvC,GAAIhS,EAAK/B,IAAI4P,GAAIxK,WACxD8H,EAAc5S,EAAMqI,KAAK2D,GACpBsJ,GAAIxK,SAAS8H,KAChBA,EAAc5G,EAAUA,EAAUpQ,OAAS,IAAMogB,EAAIvC,GAAG/N,WAAWsQ,EAAItC,UAGzE9G,EAAcoJ,EAAIvC,GAAG/N,WAAWsQ,EAAItC,GAAK,EAAIsC,EAAItC,GAAK,EAAI,GAG5D,GAAI9G,EAAa,CAEf,IAAIuJ,EAAiB7G,GAAI1D,SAASgB,EAAa0C,GAAI7F,cAAc6C,UAIjE,IAHA6J,EAAiBA,EAAeC,OAAO9G,GAAIpJ,SAAS0G,EAAYxG,YAAakJ,GAAI7F,gBAG9D7T,OAAQ,CACzB,IAAMygB,EAAO/G,GAAIpD,KAAKlS,EAAMmI,KAAKgU,GAAiB,KAClD7G,GAAI5I,iBAAiB2P,EAAMrc,EAAMsI,KAAK6T,KAI1C,OAAO3hB,KAAK0hB,c,iCASHhS,GACT,IAAI8R,EAAMxhB,MAEN8a,GAAIhL,OAAOJ,IAASoL,GAAIxK,SAASZ,MACnC8R,EAAMxhB,KAAK8hB,yBAAyBC,kBAGtC,IAAM9D,EAAOnD,GAAI7C,WAAWuJ,EAAIT,gBAAiBjG,GAAIxK,SAASZ,IAU9D,OATIuO,EAAK3H,WACP2H,EAAK3H,UAAUhF,WAAWU,aAAatC,EAAMuO,EAAK3H,WAC9CwE,GAAIhM,QAAQmP,EAAK3H,YAAcwE,GAAI7K,OAAOP,IAC5CuO,EAAK3H,UAAUhF,WAAW6C,YAAY8J,EAAK3H,YAG7C2H,EAAK9F,UAAUlG,YAAYvC,GAGtBA,I,gCAMC9P,GACRA,EAASO,IAAE8Y,KAAKrZ,GAEhB,IAAMoiB,EAAoB7hB,IAAE,eAAeE,KAAKT,GAAQ,GACpDsR,EAAa1L,EAAMmJ,KAAKqT,EAAkB9Q,YAGxCsQ,EAAMxhB,KACRiiB,GAAW,EAcf,OAZIT,EAAItC,IAAM,IACZhO,EAAaA,EAAW4G,UACxBmK,GAAW,GAGb/Q,EAAaA,EAAWtE,KAAI,SAASiH,GACnC,OAAO2N,EAAIU,WAAWrO,MAGpBoO,IACF/Q,EAAaA,EAAW4G,WAEnB5G,I,iCASP,IAAM4O,EAAY9f,KAAK+f,cACvB,OAAOhP,EAAIzG,kBAAoBwV,EAAUqC,WAAarC,EAAUvH,O,mCASrD6J,GACX,IAAIrL,EAAW/W,KAAK6gB,cAEpB,IAAK/F,GAAIrE,YAAYM,GACnB,OAAO/W,KAGT,IAAM8W,EAAagE,GAAIvE,eAAeQ,GAAU,SAAS1E,GACvD,OAAQyI,GAAIrE,YAAYpE,MAS1B,OANI+P,IACFrL,EAAW+D,GAAItE,eAAeO,GAAU,SAAS1E,GAC/C,OAAQyI,GAAIrE,YAAYpE,OAIrB,IAAI2M,EACTlI,EAAWpH,KACXoH,EAAWxE,OACXyE,EAASrH,KACTqH,EAASzE,U,oCAUC8P,GACZ,IAAIrL,EAAW/W,KAAK6gB,cAEhBwB,EAAiB,SAAShQ,GAC5B,OAAQyI,GAAIrE,YAAYpE,KAAWyI,GAAIlE,aAAavE,IAGtD,GAAIgQ,EAAetL,GACjB,OAAO/W,KAGT,IAAI8W,EAAagE,GAAIvE,eAAeQ,EAAUsL,GAM9C,OAJID,IACFrL,EAAW+D,GAAItE,eAAeO,EAAUsL,IAGnC,IAAIrD,EACTlI,EAAWpH,KACXoH,EAAWxE,OACXyE,EAASrH,KACTqH,EAASzE,U,yCAeMgQ,GACjB,IAAIvL,EAAW/W,KAAK6gB,cAEhB/J,EAAagE,GAAIvE,eAAeQ,GAAU,SAAS1E,GACrD,IAAKyI,GAAIrE,YAAYpE,KAAWyI,GAAIlE,aAAavE,GAC/C,OAAO,EAET,IAAImP,EAAM,IAAIxC,EACZ3M,EAAM3C,KACN2C,EAAMC,OACNyE,EAASrH,KACTqH,EAASzE,QAEPzD,EAASyT,EAAM5Z,KAAK8Y,EAAIW,YAC5B,OAAOtT,GAA2B,IAAjBA,EAAO0T,SAGtBf,EAAM,IAAIxC,EACZlI,EAAWpH,KACXoH,EAAWxE,OACXyE,EAASrH,KACTqH,EAASzE,QAGPiG,EAAOiJ,EAAIW,WACXtT,EAASyT,EAAM5Z,KAAK6P,GAExB,OAAI1J,GAAUA,EAAO,GAAGzN,SAAWmX,EAAKnX,OAC/BogB,EAEA,O,+BASF7F,GACP,MAAO,CACLjc,EAAG,CACD8iB,KAAM1H,GAAIjD,eAAe8D,EAAU3b,KAAKif,IACxC3M,OAAQtS,KAAKkf,IAEfuD,EAAG,CACDD,KAAM1H,GAAIjD,eAAe8D,EAAU3b,KAAKmf,IACxC7M,OAAQtS,KAAKof,O,mCAUNsD,GACX,MAAO,CACLhjB,EAAG,CACD8iB,KAAMhd,EAAMsI,KAAKgN,GAAIjD,eAAerS,EAAMmI,KAAK+U,GAAQ1iB,KAAKif,KAC5D3M,OAAQtS,KAAKkf,IAEfuD,EAAG,CACDD,KAAMhd,EAAMsI,KAAKgN,GAAIjD,eAAerS,EAAMqI,KAAK6U,GAAQ1iB,KAAKmf,KAC5D7M,OAAQtS,KAAKof,O,uCAWjB,OADkBpf,KAAK+f,cACN4C,sB,kCAWN,IAUb1jB,OAAQ,SAASggB,EAAIC,EAAIC,EAAIC,GAC3B,GAAyB,IAArB9d,UAAUF,OACZ,OAAO,IAAI4d,GAAaC,EAAIC,EAAIC,EAAIC,GAC/B,GAAyB,IAArB9d,UAAUF,OAGnB,OAAO,IAAI4d,GAAaC,EAAIC,EAF5BC,EAAKF,EACLG,EAAKF,GAGL,IAAI0D,EAAe5iB,KAAK6iB,sBAExB,IAAKD,GAAqC,IAArBthB,UAAUF,OAAc,CAC3C,IAAI0hB,EAAcxhB,UAAU,GAI5B,OAHIwZ,GAAIrL,WAAWqT,KACjBA,EAAcA,EAAYC,WAErB/iB,KAAKgjB,sBAAsBF,EAAahI,GAAIpG,YAAcpT,UAAU,GAAG6P,WAEhF,OAAOyR,GAIXI,sBAAuB,SAASF,GAAwC,IAA3B3E,EAA2B,wDAClEyE,EAAe5iB,KAAKijB,eAAeH,GACvC,OAAOF,EAAanF,SAASU,IAG/B0E,oBAAqB,WACnB,IAAI5D,EAAIC,EAAIC,EAAIC,EAChB,GAAIrO,EAAIzG,kBAAmB,CACzB,IAAM0V,EAAYjW,SAASkW,eAC3B,IAAKD,GAAsC,IAAzBA,EAAUE,WAC1B,OAAO,KACF,GAAIpF,GAAIjK,OAAOmP,EAAUkD,YAG9B,OAAO,KAGT,IAAMpD,EAAYE,EAAUmD,WAAW,GACvClE,EAAKa,EAAUsD,eACflE,EAAKY,EAAUuD,YACflE,EAAKW,EAAUwD,aACflE,EAAKU,EAAUyD,cACV,CACL,IAAMzG,EAAY/S,SAASiW,UAAUzV,cAC/BiZ,EAAe1G,EAAUc,YAC/B4F,EAAa/F,UAAS,GACtB,IAAMF,EAAiBT,EACvBS,EAAeE,UAAS,GAExB,IAAI3G,EAAa+F,GAAiBU,GAAgB,GAC9CxG,EAAW8F,GAAiB2G,GAAc,GAG1C1I,GAAIhL,OAAOgH,EAAWpH,OAASoL,GAAI1I,gBAAgB0E,IACrDgE,GAAI2I,WAAW1M,EAASrH,OAASoL,GAAIvI,iBAAiBwE,IACtDA,EAASrH,KAAKkC,cAAgBkF,EAAWpH,OACzCoH,EAAaC,GAGfkI,EAAKnI,EAAWiH,KAChBmB,EAAKpI,EAAWxE,OAChB6M,EAAKpI,EAASgH,KACdqB,EAAKrI,EAASzE,OAGhB,OAAO,IAAI0M,GAAaC,EAAIC,EAAIC,EAAIC,IAWtC6D,eAAgB,SAASvT,GACvB,IAAIuP,EAAKvP,EACLwP,EAAK,EACLC,EAAKzP,EACL0P,EAAKtE,GAAI9J,WAAWmO,GAexB,OAZIrE,GAAI9K,OAAOiP,KACbC,EAAKpE,GAAI1D,SAAS6H,GAAI7d,OAAS,EAC/B6d,EAAKA,EAAG3N,YAENwJ,GAAI3F,KAAKgK,IACXC,EAAKtE,GAAI1D,SAAS+H,GAAI/d,OAAS,EAC/B+d,EAAKA,EAAG7N,YACCwJ,GAAI9K,OAAOmP,KACpBC,EAAKtE,GAAI1D,SAAS+H,GAAI/d,OACtB+d,EAAKA,EAAG7N,YAGHtR,KAAKf,OAAOggB,EAAIC,EAAIC,EAAIC,IASjCsE,qBAAsB,SAAShU,GAC7B,OAAO1P,KAAKijB,eAAevT,GAAM+N,UAAS,IAS5CkG,oBAAqB,SAASjU,GAC5B,OAAO1P,KAAKijB,eAAevT,GAAM+N,YAYnCmG,mBAAoB,SAASjI,EAAUkI,GACrC,IAAM5E,EAAKnE,GAAI/C,eAAe4D,EAAUkI,EAASnkB,EAAE8iB,MAC7CtD,EAAK2E,EAASnkB,EAAE4S,OAChB6M,EAAKrE,GAAI/C,eAAe4D,EAAUkI,EAASpB,EAAED,MAC7CpD,EAAKyE,EAASpB,EAAEnQ,OACtB,OAAO,IAAI0M,GAAaC,EAAIC,EAAIC,EAAIC,IAYtC0E,uBAAwB,SAASD,EAAUnB,GACzC,IAAMxD,EAAK2E,EAASnkB,EAAE4S,OAChB8M,EAAKyE,EAASpB,EAAEnQ,OAChB2M,EAAKnE,GAAI/C,eAAevS,EAAMmI,KAAK+U,GAAQmB,EAASnkB,EAAE8iB,MACtDrD,EAAKrE,GAAI/C,eAAevS,EAAMqI,KAAK6U,GAAQmB,EAASpB,EAAED,MAE5D,OAAO,IAAIxD,GAAaC,EAAIC,EAAIC,EAAIC,KE15BlC2E,GAAU,CACd,UAAa,EACb,IAAO,EACP,MAAS,GACT,OAAU,GACV,MAAS,GACT,OAAU,GAGV,KAAQ,GACR,GAAM,GACN,MAAS,GACT,KAAQ,GAGR,KAAQ,GACR,KAAQ,GACR,KAAQ,GACR,KAAQ,GACR,KAAQ,GACR,KAAQ,GACR,KAAQ,GACR,KAAQ,GACR,KAAQ,GAGR,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GACL,EAAK,GAEL,MAAS,IACT,YAAe,IACf,UAAa,IACb,aAAgB,IAGhB,KAAQ,GACR,IAAO,GACP,OAAU,GACV,SAAY,IAWC,IAObC,OAAQ,SAACC,GACP,OAAOze,EAAMwI,SAAS,CACpB+V,GAAQG,UACRH,GAAQI,IACRJ,GAAQK,MACRL,GAAQM,MACRN,GAAQO,QACPL,IAQLM,OAAQ,SAACN,GACP,OAAOze,EAAMwI,SAAS,CACpB+V,GAAQS,KACRT,GAAQU,GACRV,GAAQW,MACRX,GAAQY,MACPV,IAQLW,aAAc,SAACX,GACb,OAAOze,EAAMwI,SAAS,CACpB+V,GAAQc,KACRd,GAAQe,IACRf,GAAQgB,OACRhB,GAAQiB,UACPf,IAMLgB,aAAchY,EAAKV,aAAawX,IAChClJ,KAAMkJ,I,2KC7GamB,G,WACnB,WAAYpb,I,4FAAS,SACnB9J,KAAKmlB,MAAQ,GACbnlB,KAAKolB,aAAe,EACpBplB,KAAK8J,QAAUA,EACf9J,KAAKqlB,UAAYvb,EAAQmQ,WAAW0B,SACpC3b,KAAK2b,SAAW3b,KAAKqlB,UAAU,G,8DAI/B,IAAM7D,EAAM8D,GAAMrmB,OAAOe,KAAK2b,UAG9B,MAAO,CACLvb,SAAUJ,KAAKqlB,UAAUhlB,OACzBwjB,SAAYrC,GAAOA,EAAInC,eAAkBmC,EAAIqC,SAAS7jB,KAAK2b,UAJvC,CAAEjc,EAAG,CAAE8iB,KAAM,GAAIlQ,OAAQ,GAAKmQ,EAAG,CAAED,KAAM,GAAIlQ,OAAQ,O,oCAQ/DiT,GACc,OAAtBA,EAASnlB,UACXJ,KAAKqlB,UAAUhlB,KAAKklB,EAASnlB,UAEL,OAAtBmlB,EAAS1B,UACXyB,GAAM1B,mBAAmB5jB,KAAK2b,SAAU4J,EAAS1B,UAAUlc,W,+BAWzD3H,KAAKqlB,UAAUhlB,SAAWL,KAAKmlB,MAAMnlB,KAAKolB,aAAahlB,UACzDJ,KAAKwlB,aAIPxlB,KAAKolB,YAAc,EAGnBplB,KAAKylB,cAAczlB,KAAKmlB,MAAMnlB,KAAKolB,gB,+BASnCplB,KAAKmlB,MAAQ,GAGbnlB,KAAKolB,aAAe,EAGpBplB,KAAKwlB,e,8BASLxlB,KAAKmlB,MAAQ,GAGbnlB,KAAKolB,aAAe,EAGpBplB,KAAKqlB,UAAUhlB,KAAK,IAGpBL,KAAKwlB,e,6BAQDxlB,KAAKqlB,UAAUhlB,SAAWL,KAAKmlB,MAAMnlB,KAAKolB,aAAahlB,UACzDJ,KAAKwlB,aAGHxlB,KAAKolB,YAAc,IACrBplB,KAAKolB,cACLplB,KAAKylB,cAAczlB,KAAKmlB,MAAMnlB,KAAKolB,iB,6BAQjCplB,KAAKmlB,MAAM/jB,OAAS,EAAIpB,KAAKolB,cAC/BplB,KAAKolB,cACLplB,KAAKylB,cAAczlB,KAAKmlB,MAAMnlB,KAAKolB,iB,mCAQrCplB,KAAKolB,cAGDplB,KAAKmlB,MAAM/jB,OAASpB,KAAKolB,cAC3BplB,KAAKmlB,MAAQnlB,KAAKmlB,MAAMpX,MAAM,EAAG/N,KAAKolB,cAIxCplB,KAAKmlB,MAAMhW,KAAKnP,KAAK0lB,gBAGjB1lB,KAAKmlB,MAAM/jB,OAASpB,KAAK8J,QAAQhK,QAAQ6lB,eAC3C3lB,KAAKmlB,MAAMS,QACX5lB,KAAKolB,aAAe,Q,6MCrHLS,G,uLAcTC,EAAMC,GACd,GAAIhV,EAAItH,cAAgB,IAAK,CAC3B,IAAMoF,EAAS,GAIf,OAHA1O,IAAEM,KAAKslB,GAAe,SAAC5X,EAAK6X,GAC1BnX,EAAOmX,GAAgBF,EAAKG,IAAID,MAE3BnX,EAET,OAAOiX,EAAKG,IAAIF,K,+BAST7lB,GACP,IACMgmB,EAAYlmB,KAAKmmB,UAAUjmB,EADd,CAAC,cAAe,YAAa,aAAc,kBAAmB,iBAC1B,GAEjDkmB,EAAWlmB,EAAM,GAAG6E,MAAMqhB,UAAYF,EAAU,aAKtD,OAHAA,EAAU,aAAeG,SAASD,EAAU,IAC5CF,EAAU,kBAAoBE,EAASvN,MAAM,YAEtCqN,I,gCASC1E,EAAK0E,GACb/lB,IAAEM,KAAK+gB,EAAI7P,MAAMmJ,GAAI7K,OAAQ,CAC3B+Q,iBAAiB,KACf,SAAC7S,EAAK0T,GACR1hB,IAAE0hB,GAAMoE,IAAIC,Q,iCAcL1E,EAAK1hB,GACd0hB,EAAMA,EAAI5N,YAEV,IAAM/D,EAAY/P,GAAWA,EAAQ+P,UAAa,OAC5CyW,KAA0BxmB,IAAWA,EAAQwmB,sBAC7CC,KAAyBzmB,IAAWA,EAAQymB,qBAElD,GAAI/E,EAAIV,cACN,MAAO,CAACU,EAAIU,WAAWpH,GAAI7b,OAAO4Q,KAGpC,IAAIxB,EAAOyM,GAAIlL,mBAAmBC,GAC5B8B,EAAQ6P,EAAI7P,MAAMmJ,GAAIhL,OAAQ,CAClCmR,eAAe,IACdrU,KAAI,SAAC2L,GACN,OAAOuC,GAAI7D,oBAAoBsB,EAAMlK,IAASyM,GAAIpD,KAAKa,EAAM1I,MAG/D,GAAIyW,EAAsB,CACxB,GAAIC,EAAqB,CACvB,IAAMC,EAAehF,EAAI7P,QAEzBtD,EAAOpB,EAAK5B,IAAIgD,GAAM,SAACqB,GACrB,OAAOlK,EAAMwI,SAASwY,EAAc9W,MAIxC,OAAOiC,EAAM/E,KAAI,SAAC8C,GAChB,IAAMuG,EAAW6E,GAAI9E,oBAAoBtG,EAAMrB,GACzCV,EAAOnI,EAAMmI,KAAKsI,GAClBwQ,EAAQjhB,EAAMsI,KAAKmI,GAKzB,OAJA9V,IAAEM,KAAKgmB,GAAO,SAACtY,EAAKuY,GAClB5L,GAAI5I,iBAAiBvE,EAAM+Y,EAAKxV,YAChC4J,GAAInX,OAAO+iB,MAENlhB,EAAMmI,KAAKsI,MAGpB,OAAOtE,I,8BAUH6P,GACN,IAAMmF,EAAQxmB,IAAG2a,GAAIlG,UAAU4M,EAAIvC,IAA0BuC,EAAIvC,GAAxBuC,EAAIvC,GAAG3N,YAC5C4U,EAAYlmB,KAAK4mB,SAASD,GAI9B,IACET,EAAY/lB,IAAEyB,OAAOskB,EAAW,CAC9B,YAAanc,SAAS8c,kBAAkB,QAAU,OAAS,SAC3D,cAAe9c,SAAS8c,kBAAkB,UAAY,SAAW,SACjE,iBAAkB9c,SAAS8c,kBAAkB,aAAe,YAAc,SAC1E,iBAAkB9c,SAAS8c,kBAAkB,aAAe,YAAc,SAC1E,mBAAoB9c,SAAS8c,kBAAkB,eAAiB,cAAgB,SAChF,qBAAsB9c,SAAS8c,kBAAkB,iBAAmB,gBAAkB,SACtF,cAAe9c,SAAS+c,kBAAkB,aAAeZ,EAAU,iBAErE,MAAOzD,IAKT,GAAKjB,EAAIjC,WAEF,CACL,IACMwH,EADe,CAAC,SAAU,OAAQ,oBAAqB,UAC5B5d,QAAQ+c,EAAU,qBAAuB,EAC1EA,EAAU,cAAgBa,EAAc,YAAc,eAJtDb,EAAU,cAAgB,OAO5B,IAAMrE,EAAO/G,GAAIzJ,SAASmQ,EAAIvC,GAAInE,GAAI7K,QACtC,GAAI4R,GAAQA,EAAK9c,MAAM,eACrBmhB,EAAU,eAAiBrE,EAAK9c,MAAMiiB,eACjC,CACL,IAAMA,EAAaX,SAASH,EAAU,eAAgB,IAAMG,SAASH,EAAU,aAAc,IAC7FA,EAAU,eAAiBc,EAAWC,QAAQ,GAOhD,OAJAf,EAAUgB,OAAS1F,EAAIhC,cAAgB1E,GAAIzJ,SAASmQ,EAAIvC,GAAInE,GAAIlK,UAChEsV,EAAU1U,UAAYsJ,GAAIvJ,aAAaiQ,EAAIvC,GAAInE,GAAIrL,YACnDyW,EAAUZ,MAAQ9D,EAEX0E,O,6MC5JUiB,G,+LAIDxL,GAChB3b,KAAKonB,WAAW,KAAMzL,K,0CAMJA,GAClB3b,KAAKonB,WAAW,KAAMzL,K,6BAMjBA,GAAU,WACT6F,EAAM8D,GAAMrmB,OAAO0c,GAAUmG,yBAE7BY,EAAQlB,EAAI7P,MAAMmJ,GAAI7K,OAAQ,CAAE+Q,iBAAiB,IACjDqG,EAAa7hB,EAAMuJ,UAAU2T,EAAOzV,EAAKpC,KAAK,eAEpD1K,IAAEM,KAAK4mB,GAAY,SAAClZ,EAAKuU,GACvB,IAAM/U,EAAOnI,EAAMmI,KAAK+U,GACxB,GAAI5H,GAAI3K,KAAKxC,GAAO,CAClB,IAAM2Z,EAAe,EAAKC,SAAS5Z,EAAKiF,iBACpC0U,EACF5E,EACG9V,KAAI,SAAAiV,GAAI,OAAIyF,EAAarV,YAAY4P,OAExC,EAAK2F,SAAS9E,EAAO/U,EAAK2D,WAAWzB,UACrC6S,EACG9V,KAAI,SAACiV,GAAD,OAAUA,EAAKvQ,cACnB1E,KAAI,SAACiV,GAAD,OAAU,EAAK4F,iBAAiB5F,YAGzC1hB,IAAEM,KAAKiiB,GAAO,SAACvU,EAAK0T,GAClB1hB,IAAE0hB,GAAMoE,IAAI,cAAc,SAAC9X,EAAKmG,GAC9B,OAAQ+R,SAAS/R,EAAK,KAAO,GAAK,YAM1CkN,EAAI7Z,W,8BAMEgU,GAAU,WACV6F,EAAM8D,GAAMrmB,OAAO0c,GAAUmG,yBAE7BY,EAAQlB,EAAI7P,MAAMmJ,GAAI7K,OAAQ,CAAE+Q,iBAAiB,IACjDqG,EAAa7hB,EAAMuJ,UAAU2T,EAAOzV,EAAKpC,KAAK,eAEpD1K,IAAEM,KAAK4mB,GAAY,SAAClZ,EAAKuU,GACvB,IAAM/U,EAAOnI,EAAMmI,KAAK+U,GACpB5H,GAAI3K,KAAKxC,GACX,EAAK+Z,YAAY,CAAChF,IAElBviB,IAAEM,KAAKiiB,GAAO,SAACvU,EAAK0T,GAClB1hB,IAAE0hB,GAAMoE,IAAI,cAAc,SAAC9X,EAAKmG,GAE9B,OADAA,EAAO+R,SAAS/R,EAAK,KAAO,GACf,GAAKA,EAAM,GAAK,YAMrCkN,EAAI7Z,W,iCAQKggB,EAAUhM,GAAU,WACvB6F,EAAM8D,GAAMrmB,OAAO0c,GAAUmG,yBAE/BY,EAAQlB,EAAI7P,MAAMmJ,GAAI7K,OAAQ,CAAE+Q,iBAAiB,IAC/C6C,EAAWrC,EAAIoG,aAAalF,GAC5B2E,EAAa7hB,EAAMuJ,UAAU2T,EAAOzV,EAAKpC,KAAK,eAGpD,GAAIrF,EAAMxE,KAAK0hB,EAAO5H,GAAIjG,YAAa,CACrC,IAAIgT,EAAe,GACnB1nB,IAAEM,KAAK4mB,GAAY,SAAClZ,EAAKuU,GACvBmF,EAAeA,EAAajG,OAAO,EAAK4F,SAAS9E,EAAOiF,OAE1DjF,EAAQmF,MAEH,CACL,IAAMC,EAAYtG,EAAI7P,MAAMmJ,GAAItK,OAAQ,CACtCwQ,iBAAiB,IAChB7J,QAAO,SAAC4Q,GACT,OAAQ5nB,IAAE0P,SAASkY,EAAUJ,MAG3BG,EAAU1mB,OACZjB,IAAEM,KAAKqnB,GAAW,SAAC3Z,EAAK4Z,GACtBjN,GAAIvG,QAAQwT,EAAUJ,MAGxBjF,EAAQ1iB,KAAK0nB,YAAYL,GAAY,GAIzC/B,GAAMxB,uBAAuBD,EAAUnB,GAAO/a,W,+BAQvC+a,EAAOiF,GACd,IAAMha,EAAOnI,EAAMmI,KAAK+U,GAClB7U,EAAOrI,EAAMqI,KAAK6U,GAElBsF,EAAWlN,GAAItK,OAAO7C,EAAKiF,kBAAoBjF,EAAKiF,gBACpDqV,EAAWnN,GAAItK,OAAO3C,EAAK+D,cAAgB/D,EAAK+D,YAEhDmW,EAAWC,GAAYlN,GAAIjJ,YAAYiJ,GAAI7b,OAAO0oB,GAAY,MAAO9Z,GAe3E,OAZA6U,EAAQA,EAAM9V,KAAI,SAACiV,GACjB,OAAO/G,GAAIjG,WAAWgN,GAAQ/G,GAAIvG,QAAQsN,EAAM,MAAQA,KAI1D/G,GAAI5I,iBAAiB6V,EAAUrF,GAE3BuF,IACFnN,GAAI5I,iBAAiB6V,EAAUviB,EAAMmJ,KAAKsZ,EAAS/W,aACnD4J,GAAInX,OAAOskB,IAGNvF,I,kCAUG2E,EAAYa,GAAiB,WACnCC,EAAgB,GA+EpB,OA7EAhoB,IAAEM,KAAK4mB,GAAY,SAAClZ,EAAKuU,GACvB,IAAM/U,EAAOnI,EAAMmI,KAAK+U,GAClB7U,EAAOrI,EAAMqI,KAAK6U,GAElB0F,EAAWF,EAAkBpN,GAAI5D,aAAavJ,EAAMmN,GAAItK,QAAU7C,EAAK2D,WACvE+W,EAAaD,EAAS9W,WAE5B,GAAqC,OAAjC8W,EAAS9W,WAAWzB,SACtB6S,EAAM9V,KAAI,SAAAiV,GACR,IAAMyG,EAAU,EAAKC,iBAAiB1G,GAElCwG,EAAWzW,YACbyW,EAAW/W,WAAWU,aACpB6P,EACAwG,EAAWzW,aAGbyW,EAAW/W,WAAWW,YAAY4P,GAGhCyG,EAAQlnB,SACV,EAAKomB,SAASc,EAASF,EAASvY,UAChCgS,EAAK5P,YAAYqW,EAAQ,GAAGhX,gBAIC,IAA7B8W,EAASvoB,SAASuB,QACpBinB,EAAWlU,YAAYiU,GAGY,IAAjCC,EAAWnX,WAAW9P,QACxBinB,EAAW/W,WAAW6C,YAAYkU,OAE/B,CACL,IAAMG,EAAWJ,EAASlX,WAAW9P,OAAS,EAAI0Z,GAAI9G,UAAUoU,EAAU,CACxE1Y,KAAM7B,EAAKyD,WACXgB,OAAQwI,GAAIpI,SAAS7E,GAAQ,GAC5B,CACD4F,wBAAwB,IACrB,KAECgV,EAAa3N,GAAI9G,UAAUoU,EAAU,CACzC1Y,KAAM/B,EAAK2D,WACXgB,OAAQwI,GAAIpI,SAAS/E,IACpB,CACD8F,wBAAwB,IAG1BiP,EAAQwF,EAAkBpN,GAAIzD,eAAeoR,EAAY3N,GAAI3K,MACzD3K,EAAMmJ,KAAK8Z,EAAWvX,YAAYiG,OAAO2D,GAAI3K,OAG7C+X,GAAoBpN,GAAItK,OAAO4X,EAAS9W,cAC1CoR,EAAQA,EAAM9V,KAAI,SAACiV,GACjB,OAAO/G,GAAIvG,QAAQsN,EAAM,SAI7B1hB,IAAEM,KAAK+E,EAAMmJ,KAAK+T,GAAO5K,WAAW,SAAC3J,EAAK0T,GACxC/G,GAAIjJ,YAAYgQ,EAAMuG,MAIxB,IAAMM,EAAYljB,EAAMyJ,QAAQ,CAACmZ,EAAUK,EAAYD,IACvDroB,IAAEM,KAAKioB,GAAW,SAACva,EAAKwa,GACtB,IAAMC,EAAY,CAACD,GAAU/G,OAAO9G,GAAIzD,eAAesR,EAAU7N,GAAItK,SACrErQ,IAAEM,KAAKmoB,EAAU9Q,WAAW,SAAC3J,EAAK4Z,GAC3BjN,GAAI9J,WAAW+W,IAClBjN,GAAInX,OAAOokB,GAAU,SAM7BI,EAAgBA,EAAcvG,OAAOc,MAGhCyF,I,uCAYQzY,GACf,OAAOA,EAAKkD,gBACRkI,GAAI5I,iBAAiBxC,EAAKkD,gBAAiB,CAAClD,IAC5C1P,KAAKwnB,SAAS,CAAC9X,GAAO,Q,+BAWnBA,GACP,OAAOA,EACHlK,EAAMxE,KAAK0O,EAAK7P,UAAU,SAAAqB,GAAK,MAAI,CAAC,KAAM,MAAMiI,QAAQjI,EAAM2O,WAAa,KAC3E,O,uCAWWH,GAEf,IADA,IAAMuG,EAAW,GACVvG,EAAKkC,aACVqE,EAAS9G,KAAKO,EAAKkC,aACnBlC,EAAOA,EAAKkC,YAEd,OAAOqE,O,6MChRU4S,G,WACnB,WAAY/e,I,4FAAS,SAEnB9J,KAAK8oB,OAAS,IAAI3B,GAClBnnB,KAAKF,QAAUgK,EAAQhK,Q,yDASf0hB,EAAKuH,GACb,IAAMC,EAAMlO,GAAIxC,WAAW,IAAI/W,MAAMwnB,EAAU,GAAGhc,KAAK+N,GAAIxL,aAC3DkS,EAAMA,EAAIO,kBACNG,WAAW8G,GAAK,IAEpBxH,EAAM8D,GAAMrmB,OAAO+pB,EAAKD,IACpBphB,W,sCAcUgU,EAAU6F,GAOxBA,GAHAA,GAHAA,EAAMA,GAAO8D,GAAMrmB,OAAO0c,IAGhBoG,kBAGAD,yBAGV,IAEImH,EAFE/Q,EAAY4C,GAAIzJ,SAASmQ,EAAIvC,GAAInE,GAAI7K,QAI3C,GAAIiI,EAAW,CAEb,GAAI4C,GAAI3K,KAAK+H,KAAe4C,GAAIhM,QAAQoJ,IAAc4C,GAAIpF,oBAAoBwC,IAG5E,YADAlY,KAAK8oB,OAAO1B,WAAWlP,EAAU5G,WAAWzB,UAG5C,IAAI7K,EAAa,KAOjB,GAN6C,IAAzChF,KAAKF,QAAQopB,wBACflkB,EAAa8V,GAAIzJ,SAAS6G,EAAW4C,GAAIpK,cACS,IAAzC1Q,KAAKF,QAAQopB,0BACtBlkB,EAAa8V,GAAI5D,aAAagB,EAAW4C,GAAIpK,eAG3C1L,EAAY,CAEdikB,EAAW9oB,IAAE2a,GAAIpG,WAAW,GAGxBoG,GAAIvI,iBAAiBiP,EAAIT,kBAAoBjG,GAAI3F,KAAKqM,EAAIvC,GAAGrN,cAC/DzR,IAAEqhB,EAAIvC,GAAGrN,aAAajO,SAExB,IAAMgJ,EAAQmO,GAAI9G,UAAUhP,EAAYwc,EAAIT,gBAAiB,CAAEpN,sBAAsB,IACjFhH,EACFA,EAAM2E,WAAWU,aAAaiX,EAAUtc,GAExCmO,GAAIjJ,YAAYoX,EAAUjkB,OAEvB,CACLikB,EAAWnO,GAAI9G,UAAUkE,EAAWsJ,EAAIT,iBAGxC,IAAIoI,EAAerO,GAAIzD,eAAea,EAAW4C,GAAIlF,eACrDuT,EAAeA,EAAavH,OAAO9G,GAAIzD,eAAe4R,EAAUnO,GAAIlF,gBAEpEzV,IAAEM,KAAK0oB,GAAc,SAAChb,EAAK+Y,GACzBpM,GAAInX,OAAOujB,OAIRpM,GAAIhG,UAAUmU,IAAanO,GAAI5K,MAAM+Y,IAAanO,GAAIlB,iBAAiBqP,KAAcnO,GAAIhM,QAAQma,KACpGA,EAAWnO,GAAIvG,QAAQ0U,EAAU,WAKlC,CACL,IAAM7a,EAAOoT,EAAIvC,GAAG/N,WAAWsQ,EAAItC,IACnC+J,EAAW9oB,IAAE2a,GAAIpG,WAAW,GACxBtG,EACFoT,EAAIvC,GAAGjN,aAAaiX,EAAU7a,GAE9BoT,EAAIvC,GAAGhN,YAAYgX,GAIvB3D,GAAMrmB,OAAOgqB,EAAU,GAAGvH,YAAY/Z,SAASyhB,eAAezN,Q,yMCtGlE,IAAM0N,GAAoB,SAApBA,EAA6BvS,EAAYwS,EAAOniB,EAAQoiB,GAC5D,IAAMC,EAAc,CAAE,OAAU,EAAG,OAAU,GACvCC,EAAgB,GAChBC,EAAkB,GA+BxB,SAASC,EAAwBC,EAAUC,EAAWC,EAASC,EAAUC,EAAWC,EAAWC,GAC7F,IAAMC,EAAc,CAClB,QAAWL,EACX,SAAYC,EACZ,UAAaC,EACb,UAAaC,EACb,UAAaC,GAEVT,EAAcG,KACjBH,EAAcG,GAAY,IAE5BH,EAAcG,GAAUC,GAAaM,EASvC,SAASC,EAAcC,EAAqBC,EAAcC,EAAoBC,GAC5E,MAAO,CACL,SAAYH,EAAoBN,SAChC,OAAUO,EACV,aAAgB,CACd,SAAYC,EACZ,UAAaC,IAWnB,SAASC,EAAiBb,EAAUC,GAClC,IAAKJ,EAAcG,GACjB,OAAOC,EAET,IAAKJ,EAAcG,GAAUC,GAC3B,OAAOA,EAIT,IADA,IAAIa,EAAeb,EACZJ,EAAcG,GAAUc,IAE7B,GADAA,KACKjB,EAAcG,GAAUc,GAC3B,OAAOA,EAWb,SAASC,EAAqBC,EAAKC,GACjC,IAAMhB,EAAYY,EAAiBG,EAAIhB,SAAUiB,EAAKhB,WAChDiB,EAAkBD,EAAKE,QAAU,EACjCC,EAAkBH,EAAKI,QAAU,EACjCC,EAAsBN,EAAIhB,WAAaJ,EAAY2B,QAAUN,EAAKhB,YAAcL,EAAY4B,OAClGzB,EAAwBiB,EAAIhB,SAAUC,EAAWe,EAAKC,EAAMG,EAAgBF,GAAgB,GAG5F,IAAMO,EAAgBR,EAAKS,WAAWL,QAAU5E,SAASwE,EAAKS,WAAWL,QAAQrsB,MAAO,IAAM,EAC9F,GAAIysB,EAAgB,EAClB,IAAK,IAAIE,EAAK,EAAGA,EAAKF,EAAeE,IAAM,CACzC,IAAMC,EAAeZ,EAAIhB,SAAW2B,EACpCE,EAAiBD,EAAc3B,EAAWgB,EAAMK,GAChDvB,EAAwB6B,EAAc3B,EAAWe,EAAKC,GAAM,EAAMC,GAAgB,GAKtF,IAAMY,EAAgBb,EAAKS,WAAWP,QAAU1E,SAASwE,EAAKS,WAAWP,QAAQnsB,MAAO,IAAM,EAC9F,GAAI8sB,EAAgB,EAClB,IAAK,IAAIC,EAAK,EAAGA,EAAKD,EAAeC,IAAM,CACzC,IAAMC,EAAgBnB,EAAiBG,EAAIhB,SAAWC,EAAY8B,GAClEF,EAAiBb,EAAIhB,SAAUgC,EAAef,EAAMK,GACpDvB,EAAwBiB,EAAIhB,SAAUgC,EAAehB,EAAKC,EAAMG,GAAgB,GAAM,IAa5F,SAASS,EAAiB7B,EAAUC,EAAWgB,EAAMgB,GAC/CjC,IAAaJ,EAAY2B,QAAU3B,EAAY4B,QAAUP,EAAKhB,WAAagB,EAAKhB,WAAaA,IAAcgC,GAC7GrC,EAAY4B,SAsBhB,SAASU,EAA4BjB,GACnC,OAAQvB,GACN,KAAKD,EAAkBC,MAAMyC,OAC3B,GAAIlB,EAAKZ,UACP,OAAOZ,EAAkBiB,aAAa0B,kBAExC,MACF,KAAK3C,EAAkBC,MAAM2C,IAC3B,IAAKpB,EAAKqB,WAAarB,EAAKb,UAC1B,OAAOX,EAAkBiB,aAAa6B,QACjC,GAAItB,EAAKb,UACd,OAAOX,EAAkBiB,aAAa0B,kBAI5C,OAAO3C,EAAkBiB,aAAa8B,WAQxC,SAASC,EAAyBxB,GAChC,OAAQvB,GACN,KAAKD,EAAkBC,MAAMyC,OAC3B,GAAIlB,EAAKZ,UACP,OAAOZ,EAAkBiB,aAAagC,aACjC,GAAIzB,EAAKb,WAAaa,EAAKqB,UAChC,OAAO7C,EAAkBiB,aAAaiC,OAExC,MACF,KAAKlD,EAAkBC,MAAM2C,IAC3B,GAAIpB,EAAKb,UACP,OAAOX,EAAkBiB,aAAagC,aACjC,GAAIzB,EAAKZ,WAAaY,EAAKqB,UAChC,OAAO7C,EAAkBiB,aAAaiC,OAI5C,OAAOlD,EAAkBiB,aAAa6B,QAexCnsB,KAAKwsB,cAAgB,WAMnB,IALA,IAAMC,EAAYnD,IAAUD,EAAkBC,MAAM2C,IAAOzC,EAAY2B,QAAU,EAC3EuB,EAAYpD,IAAUD,EAAkBC,MAAMyC,OAAUvC,EAAY4B,QAAU,EAEhFuB,EAAiB,EACjBC,GAAc,EACXA,GAAa,CAClB,IAAMC,EAAeJ,GAAY,EAAKA,EAAWE,EAC3CG,EAAeJ,GAAY,EAAKA,EAAWC,EAC3C/B,EAAMnB,EAAcoD,GAC1B,IAAKjC,EAEH,OADAgC,GAAc,EACPlD,EAET,IAAMmB,EAAOD,EAAIkC,GACjB,IAAKjC,EAEH,OADA+B,GAAc,EACPlD,EAIT,IAAIY,EAAejB,EAAkBiB,aAAaiC,OAClD,OAAQplB,GACN,KAAKkiB,EAAkB0D,cAAcC,IACnC1C,EAAe+B,EAAyBxB,GACxC,MACF,KAAKxB,EAAkB0D,cAAcE,OACnC3C,EAAewB,EAA4BjB,GAG/CnB,EAAgBva,KAAKib,EAAcS,EAAMP,EAAcuC,EAAaC,IACpEH,IAGF,OAAOjD,GAtOF5S,GAAeA,EAAWoW,UAAiD,OAArCpW,EAAWoW,QAAQ/kB,eAA+D,OAArC2O,EAAWoW,QAAQ/kB,iBAI3GqhB,EAAY4B,OAAStU,EAAW+S,UAC3B/S,EAAWmG,eAAkBnG,EAAWmG,cAAciQ,SAA8D,OAAnDpW,EAAWmG,cAAciQ,QAAQ/kB,gBAIvGqhB,EAAY2B,OAASrU,EAAWmG,cAAc2M,WAqHhD,WAEE,IADA,IAAMuD,EAAO5D,EAAS4D,KACbvD,EAAW,EAAGA,EAAWuD,EAAK/rB,OAAQwoB,IAE7C,IADA,IAAMwD,EAAQD,EAAKvD,GAAUwD,MACpBvD,EAAY,EAAGA,EAAYuD,EAAMhsB,OAAQyoB,IAChDc,EAAqBwC,EAAKvD,GAAWwD,EAAMvD,IAuD/CwD,IAqDJhE,GAAkBC,MAAQ,CAAE,IAAO,EAAG,OAAU,GAKhDD,GAAkB0D,cAAgB,CAAE,IAAO,EAAG,OAAU,GAKxD1D,GAAkBiB,aAAe,CAAE,OAAU,EAAG,kBAAqB,EAAG,WAAc,EAAG,QAAW,EAAG,aAAgB,G,IASlGgD,G,iLAOf9L,EAAK+L,GACP,IAAM1C,EAAO/P,GAAIzJ,SAASmQ,EAAI/J,iBAAkBqD,GAAInK,QAC9CrM,EAAQwW,GAAIzJ,SAASwZ,EAAM/P,GAAI1K,SAC/Bgd,EAAQtS,GAAIzD,eAAe/S,EAAOwW,GAAInK,QAEtC6c,EAAWhoB,EAAM+nB,EAAU,OAAS,QAAQH,EAAOvC,GACrD2C,GACFlI,GAAMrmB,OAAOuuB,EAAU,GAAG7lB,W,6BAWvB6Z,EAAK9O,GAWV,IAVA,IAAMmY,EAAO/P,GAAIzJ,SAASmQ,EAAI/J,iBAAkBqD,GAAInK,QAE9C8c,EAAYttB,IAAE0qB,GAAMrO,QAAQ,MAC5BkR,EAAe1tB,KAAK2tB,kBAAkBF,GACtCptB,EAAOF,IAAE,MAAQutB,EAAe,UAIhCE,EAFS,IAAIvE,GAAkBwB,EAAMxB,GAAkBC,MAAM2C,IACjE5C,GAAkB0D,cAAcC,IAAK7sB,IAAEstB,GAAWjR,QAAQ,SAAS,IAC9CgQ,gBAEdqB,EAAS,EAAGA,EAASD,EAAQxsB,OAAQysB,IAAU,CACtD,IAAMC,EAAcF,EAAQC,GACtBE,EAAe/tB,KAAK2tB,kBAAkBG,EAAY/D,UACxD,OAAQ+D,EAAY3mB,QAClB,KAAKkiB,GAAkBiB,aAAa6B,QAClC9rB,EAAKgB,OAAO,MAAQ0sB,EAAe,IAAMjT,GAAIrG,MAAQ,SACrD,MACF,KAAK4U,GAAkBiB,aAAagC,aAEhC,GAAiB,QAAb5Z,IACiBob,EAAY/D,SAAShY,OACI+b,EAAY/D,SAASvN,QAAQ,MAAMoN,SAAvC,IAAoD6D,EAAU,GAAG7D,SACnF,CACpB,IAAMoE,EAAQ7tB,IAAE,eAAekB,OAAOlB,IAAE,MAAQ4tB,EAAe,IAAMjT,GAAIrG,MAAQ,SAASwZ,WAAW,YAAY5tB,OACjHA,EAAKgB,OAAO2sB,GACZ,MAGJ,IAAI3C,EAAgBhF,SAASyH,EAAY/D,SAASkB,QAAS,IAC3DI,IACAyC,EAAY/D,SAASmE,aAAa,UAAW7C,IAMrD,GAAiB,QAAb3Y,EACF+a,EAAUU,OAAO9tB,OACZ,CAEL,GADwBwqB,EAAKI,QAAU,EACnB,CAClB,IAAMmD,EAAcX,EAAU,GAAG7D,UAAYiB,EAAKI,QAAU,GAE5D,YADA9qB,IAAEA,IAAEstB,GAAW1b,SAAS/Q,KAAK,MAAMotB,IAAcC,MAAMluB,IAAEE,IAG3DotB,EAAUY,MAAMhuB,M,6BAWbmhB,EAAK9O,GACV,IAAMmY,EAAO/P,GAAIzJ,SAASmQ,EAAI/J,iBAAkBqD,GAAInK,QAC9Cia,EAAMzqB,IAAE0qB,GAAMrO,QAAQ,MACVrc,IAAEyqB,GAAK3U,WACf9G,KAAKyb,GAMf,IAJA,IAEMgD,EAFS,IAAIvE,GAAkBwB,EAAMxB,GAAkBC,MAAMyC,OACjE1C,GAAkB0D,cAAcC,IAAK7sB,IAAEyqB,GAAKpO,QAAQ,SAAS,IACxCgQ,gBAEd8B,EAAc,EAAGA,EAAcV,EAAQxsB,OAAQktB,IAAe,CACrE,IAAMR,EAAcF,EAAQU,GACtBP,EAAe/tB,KAAK2tB,kBAAkBG,EAAY/D,UACxD,OAAQ+D,EAAY3mB,QAClB,KAAKkiB,GAAkBiB,aAAa6B,QACjB,UAAbzZ,EACFvS,IAAE2tB,EAAY/D,UAAUsE,MAAM,MAAQN,EAAe,IAAMjT,GAAIrG,MAAQ,SAEvEtU,IAAE2tB,EAAY/D,UAAUoE,OAAO,MAAQJ,EAAe,IAAMjT,GAAIrG,MAAQ,SAE1E,MACF,KAAK4U,GAAkBiB,aAAagC,aAClC,GAAiB,UAAb5Z,EAAsB,CACxB,IAAIgZ,EAAgBrF,SAASyH,EAAY/D,SAASgB,QAAS,IAC3DW,IACAoC,EAAY/D,SAASmE,aAAa,UAAWxC,QAE7CvrB,IAAE2tB,EAAY/D,UAAUoE,OAAO,MAAQJ,EAAe,IAAMjT,GAAIrG,MAAQ,a,wCAahEhD,GAChB,IAAI8c,EAAY,GAEhB,IAAK9c,EACH,OAAO8c,EAKT,IAFA,IAAMC,EAAW/c,EAAG6Z,YAAc,GAEzBhuB,EAAI,EAAGA,EAAIkxB,EAASptB,OAAQ9D,IACI,OAAnCkxB,EAASlxB,GAAGY,KAAKiK,eAIjBqmB,EAASlxB,GAAGmxB,YACdF,GAAa,IAAMC,EAASlxB,GAAGY,KAAO,KAAQswB,EAASlxB,GAAGsB,MAAQ,KAItE,OAAO2vB,I,gCASC/M,GAUR,IATA,IAAMqJ,EAAO/P,GAAIzJ,SAASmQ,EAAI/J,iBAAkBqD,GAAInK,QAC9Cia,EAAMzqB,IAAE0qB,GAAMrO,QAAQ,MACtBkS,EAAU9D,EAAI/qB,SAAS,UAAU0iB,MAAMpiB,IAAE0qB,IACzCM,EAASP,EAAI,GAAGhB,SAIhBgE,EAFS,IAAIvE,GAAkBwB,EAAMxB,GAAkBC,MAAM2C,IACjE5C,GAAkB0D,cAAcE,OAAQ9sB,IAAEyqB,GAAKpO,QAAQ,SAAS,IAC3CgQ,gBAEd8B,EAAc,EAAGA,EAAcV,EAAQxsB,OAAQktB,IACtD,GAAKV,EAAQU,GAAb,CAIA,IAAMvE,EAAW6D,EAAQU,GAAavE,SAChC4E,EAAkBf,EAAQU,GAAaM,aACvCC,EAAc9E,EAASkB,SAAWlB,EAASkB,QAAU,EACvDI,EAAiBwD,EAAcxI,SAAS0D,EAASkB,QAAS,IAAM,EACpE,OAAQ2C,EAAQU,GAAannB,QAC3B,KAAKkiB,GAAkBiB,aAAaiC,OAClC,SACF,KAAKlD,GAAkBiB,aAAa6B,QAEhC,IAAM2C,EAAUlE,EAAIxc,KAAK,MAAM,GAC/B,IAAK0gB,EAAW,SAChB,IAAMC,EAAWnE,EAAI,GAAGwC,MAAMsB,GAC1BG,IACExD,EAAgB,GAClBA,IACAyD,EAAQ9c,aAAa+c,EAAUD,EAAQ1B,MAAMsB,IAC7CI,EAAQ1B,MAAMsB,GAASR,aAAa,UAAW7C,GAC/CyD,EAAQ1B,MAAMsB,GAASvd,UAAY,IACR,IAAlBka,IACTyD,EAAQ9c,aAAa+c,EAAUD,EAAQ1B,MAAMsB,IAC7CI,EAAQ1B,MAAMsB,GAASM,gBAAgB,WACvCF,EAAQ1B,MAAMsB,GAASvd,UAAY,KAIzC,SACF,KAAKkY,GAAkBiB,aAAa0B,kBAC9B6C,IACExD,EAAgB,GAClBA,IACAtB,EAASmE,aAAa,UAAW7C,GAC7BsD,EAAgB/E,WAAauB,GAAUpB,EAASF,YAAc6E,IAAW3E,EAAS5Y,UAAY,KACvE,IAAlBka,IACTtB,EAASiF,gBAAgB,WACrBL,EAAgB/E,WAAauB,GAAUpB,EAASF,YAAc6E,IAAW3E,EAAS5Y,UAAY,MAGtG,SACF,KAAKkY,GAAkBiB,aAAa8B,WAElC,UAGNxB,EAAIjnB,W,gCASI6d,GASR,IARA,IAAMqJ,EAAO/P,GAAIzJ,SAASmQ,EAAI/J,iBAAkBqD,GAAInK,QAC9Cia,EAAMzqB,IAAE0qB,GAAMrO,QAAQ,MACtBkS,EAAU9D,EAAI/qB,SAAS,UAAU0iB,MAAMpiB,IAAE0qB,IAIzC+C,EAFS,IAAIvE,GAAkBwB,EAAMxB,GAAkBC,MAAMyC,OACjE1C,GAAkB0D,cAAcE,OAAQ9sB,IAAEyqB,GAAKpO,QAAQ,SAAS,IAC3CgQ,gBAEd8B,EAAc,EAAGA,EAAcV,EAAQxsB,OAAQktB,IACtD,GAAKV,EAAQU,GAGb,OAAQV,EAAQU,GAAannB,QAC3B,KAAKkiB,GAAkBiB,aAAaiC,OAClC,SACF,KAAKlD,GAAkBiB,aAAa0B,kBAEhC,IAAMjC,EAAW6D,EAAQU,GAAavE,SAEtC,GADoBA,EAASgB,SAAWhB,EAASgB,QAAU,EAC3C,CACd,IAAIW,EAAiB3B,EAASgB,QAAW1E,SAAS0D,EAASgB,QAAS,IAAM,EACtEW,EAAgB,GAClBA,IACA3B,EAASmE,aAAa,UAAWxC,GAC7B3B,EAASF,YAAc6E,IAAW3E,EAAS5Y,UAAY,KAChC,IAAlBua,IACT3B,EAASiF,gBAAgB,WACrBjF,EAASF,YAAc6E,IAAW3E,EAAS5Y,UAAY,KAIjE,SACF,KAAKkY,GAAkBiB,aAAa8B,WAClCtR,GAAInX,OAAOiqB,EAAQU,GAAavE,UAAU,GAC1C,Y,kCAYIkF,EAAUC,EAAUpvB,GAG9B,IAFA,IACIqvB,EADEC,EAAM,GAEHC,EAAS,EAAGA,EAASJ,EAAUI,IACtCD,EAAIjgB,KAAK,OAAS2L,GAAIrG,MAAQ,SAEhC0a,EAASC,EAAIriB,KAAK,IAIlB,IAFA,IACIuiB,EADEC,EAAM,GAEHC,EAAS,EAAGA,EAASN,EAAUM,IACtCD,EAAIpgB,KAAK,OAASggB,EAAS,SAE7BG,EAASC,EAAIxiB,KAAK,IAClB,IAAM0iB,EAAStvB,IAAE,UAAYmvB,EAAS,YAKtC,OAJIxvB,GAAWA,EAAQ4vB,gBACrBD,EAAOlvB,SAAST,EAAQ4vB,gBAGnBD,EAAO,K,kCASJjO,GACV,IAAMqJ,EAAO/P,GAAIzJ,SAASmQ,EAAI/J,iBAAkBqD,GAAInK,QACpDxQ,IAAE0qB,GAAMrO,QAAQ,SAAS7Y,c,yMCnjB7B,IAKqBgsB,G,WACnB,WAAY7lB,GAAS,Y,4FAAA,SACnB9J,KAAK8J,QAAUA,EAEf9J,KAAK+Z,MAAQjQ,EAAQmQ,WAAW4E,KAChC7e,KAAK4vB,QAAU9lB,EAAQmQ,WAAWgB,OAClCjb,KAAKqlB,UAAYvb,EAAQmQ,WAAW0B,SACpC3b,KAAKF,QAAUgK,EAAQhK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ4e,SAEzB1e,KAAK2b,SAAW3b,KAAKqlB,UAAU,GAC/BrlB,KAAK6vB,UAAY,KACjB7vB,KAAKulB,SAAW,KAEhBvlB,KAAK+E,MAAQ,IAAI8gB,GACjB7lB,KAAKsE,MAAQ,IAAIgpB,GACjBttB,KAAK8vB,OAAS,IAAIjH,GAAO/e,GACzB9J,KAAK8oB,OAAS,IAAI3B,GAClBnnB,KAAKuH,QAAU,IAAI2d,GAAQpb,GAE3B9J,KAAK8J,QAAQ4E,KAAK,cAAe1O,KAAK2B,KAAKgE,KAAKoqB,QAChD/vB,KAAK8J,QAAQ4E,KAAK,YAAa1O,KAAK2B,KAAKgE,KAAK6B,MAC9CxH,KAAK8J,QAAQ4E,KAAK,YAAa1O,KAAK2B,KAAKgE,KAAK8B,MAC9CzH,KAAK8J,QAAQ4E,KAAK,WAAY1O,KAAK2B,KAAKgE,KAAKqjB,KAC7ChpB,KAAK8J,QAAQ4E,KAAK,aAAc1O,KAAK2B,KAAKgE,KAAKqqB,OAC/ChwB,KAAK8J,QAAQ4E,KAAK,uBAAwB1O,KAAK2B,KAAKgE,KAAKsqB,iBACzDjwB,KAAK8J,QAAQ4E,KAAK,yBAA0B1O,KAAK2B,KAAKgE,KAAKuqB,mBAC3DlwB,KAAK8J,QAAQ4E,KAAK,2BAA4B1O,KAAK2B,KAAKgE,KAAKwqB,qBAC7DnwB,KAAK8J,QAAQ4E,KAAK,cAAe1O,KAAK2B,KAAKgE,KAAKK,QAChDhG,KAAK8J,QAAQ4E,KAAK,eAAgB1O,KAAK2B,KAAKgE,KAAKI,SACjD/F,KAAK8J,QAAQ4E,KAAK,kBAAmB1O,KAAK2B,KAAKgE,KAAKyqB,YACpDpwB,KAAK8J,QAAQ4E,KAAK,4BAA6B1O,KAAK2B,KAAKgE,KAAK0qB,sBAC9DrwB,KAAK8J,QAAQ4E,KAAK,gBAAiB1O,KAAK2B,KAAKgE,KAAKsC,UASlD,IANA,IAAMqoB,EAAW,CACf,OAAQ,SAAU,YAAa,gBAAiB,cAAe,YAC/D,cAAe,gBAAiB,eAAgB,cAChD,cAAe,eAAgB,aAGxBniB,EAAM,EAAGG,EAAMgiB,EAASlvB,OAAQ+M,EAAMG,EAAKH,IAClDnO,KAAKswB,EAASniB,IAAS,SAACoiB,GACtB,OAAO,SAAC3xB,GACN,EAAK4xB,gBACLzmB,SAAS0mB,YAAYF,GAAM,EAAO3xB,GAClC,EAAK8xB,cAAa,IAJC,CAMpBJ,EAASniB,IACZnO,KAAK8J,QAAQ4E,KAAK,QAAU4hB,EAASniB,GAAMnO,KAAK2B,KAAKgE,KAAK2qB,EAASniB,KAGrEnO,KAAKiI,SAAWjI,KAAK2wB,aAAY,SAAC/xB,GAChC,OAAO,EAAKgyB,YAAY,cAAe7f,EAAI/I,cAAcpJ,OAG3DoB,KAAKomB,SAAWpmB,KAAK2wB,aAAY,SAAC/xB,GAChC,IAAMiyB,EAAO,EAAKC,eAAe,kBACjC,OAAO,EAAKF,YAAY,YAAahyB,EAAQiyB,MAG/C7wB,KAAK+wB,aAAe/wB,KAAK2wB,aAAY,SAAC/xB,GACpC,IAAM0D,EAAO,EAAKwuB,eAAe,aACjC,OAAO,EAAKF,YAAY,YAAatuB,EAAO1D,MAG9C,IAAK,IAAIuP,EAAM,EAAGA,GAAO,EAAGA,IAC1BnO,KAAK,UAAYmO,GAAQ,SAACA,GACxB,OAAO,WACL,EAAK6iB,YAAY,IAAM7iB,IAFF,CAItBA,GACHnO,KAAK8J,QAAQ4E,KAAK,eAAiBP,EAAKnO,KAAK2B,KAAKgE,KAAK,UAAYwI,IAGrEnO,KAAKiwB,gBAAkBjwB,KAAK2wB,aAAY,WACtC,EAAKb,OAAOG,gBAAgB,EAAKtU,aAGnC3b,KAAKkwB,kBAAoBlwB,KAAK2wB,aAAY,WACxC,EAAK7H,OAAOoH,kBAAkB,EAAKvU,aAGrC3b,KAAKmwB,oBAAsBnwB,KAAK2wB,aAAY,WAC1C,EAAK7H,OAAOqH,oBAAoB,EAAKxU,aAGvC3b,KAAKgG,OAAShG,KAAK2wB,aAAY,WAC7B,EAAK7H,OAAO9iB,OAAO,EAAK2V,aAG1B3b,KAAK+F,QAAU/F,KAAK2wB,aAAY,WAC9B,EAAK7H,OAAO/iB,QAAQ,EAAK4V,aAQ3B3b,KAAKkiB,WAAaliB,KAAK2wB,aAAY,SAACjhB,GAC9B,EAAKuhB,UAAU9wB,IAAEuP,GAAM6I,OAAOnX,UAGtB,EAAK8vB,eACbhP,WAAWxS,GACf,EAAKyhB,aAAa7L,GAAM3B,oBAAoBjU,GAAM/H,cAOpD3H,KAAKoxB,WAAapxB,KAAK2wB,aAAY,SAACpY,GAClC,IAAI,EAAK0Y,UAAU1Y,EAAKnX,QAAxB,CAGA,IACMiwB,EADM,EAAKH,eACIhP,WAAWpH,GAAIxC,WAAWC,IAC/C,EAAK4Y,aAAa7L,GAAMrmB,OAAOoyB,EAAUvW,GAAI9J,WAAWqgB,IAAW1pB,cAOrE3H,KAAKsxB,UAAYtxB,KAAK2wB,aAAY,SAAC/wB,GACjC,IAAI,EAAKqxB,UAAUrxB,EAAOwB,QAA1B,CAGAxB,EAAS,EAAKkK,QAAQ2B,OAAO,kBAAmB7L,GAChD,IAAMQ,EAAW,EAAK8wB,eAAeI,UAAU1xB,GAC/C,EAAKuxB,aAAa7L,GAAM3B,oBAAoBne,EAAMqI,KAAKzN,IAAWuH,cAQpE3H,KAAKgxB,YAAchxB,KAAK2wB,aAAY,SAACzD,EAAS5Q,GAC5C,IAAMiV,EAAqB,EAAKzxB,QAAQ+b,UAAU0V,mBAC9CA,EACFA,EAAmBzzB,KAAK,EAAMwe,EAAS,EAAKxS,QAAS,EAAK0nB,eAE1D,EAAKA,cAActE,EAAS5Q,MAOhCtc,KAAKqwB,qBAAuBrwB,KAAK2wB,aAAY,WAC3C,IAAMc,EAAS,EAAKP,eAAehP,WAAWpH,GAAI7b,OAAO,OACrDwyB,EAAO7f,aACT,EAAKuf,aAAa7L,GAAMrmB,OAAOwyB,EAAO7f,YAAa,GAAG8P,YAAY/Z,aAQtE3H,KAAKgnB,WAAahnB,KAAK2wB,aAAY,SAAC/xB,GAClC,EAAKmG,MAAM2sB,UAAU,EAAKR,eAAgB,CACxClK,WAAYpoB,OAShBoB,KAAK2xB,WAAa3xB,KAAK2wB,aAAY,SAACiB,GAClC,IAAIC,EAAUD,EAASluB,IACjBouB,EAAWF,EAASrZ,KACpBwZ,EAAcH,EAASG,YACvBC,EAAgBJ,EAASI,cAC3BxQ,EAAMoQ,EAAStM,OAAS,EAAK4L,eAC3Be,EAAuBH,EAAS1wB,OAASogB,EAAIW,WAAW/gB,OAC9D,KAAI6wB,EAAuB,GAAK,EAAKhB,UAAUgB,IAA/C,CAGA,IAAMC,EAAgB1Q,EAAIW,aAAe2P,EAGlB,iBAAZD,IACTA,EAAUA,EAAQ5Y,QAGhB,EAAKnZ,QAAQqyB,aACfN,EAAU,EAAK/xB,QAAQqyB,aAAaN,GAC3BG,IAETH,EAAU,oCAAoCrpB,KAAKqpB,GAC/CA,EAAU,EAAK/xB,QAAQsyB,gBAAkBP,GAG/C,IAAIQ,EAAU,GACd,GAAIH,EAAe,CAEjB,IAAMhL,GADN1F,EAAMA,EAAIO,kBACSG,WAAW/hB,IAAE,MAAQ2xB,EAAW,QAAQ,IAC3DO,EAAQljB,KAAK+X,QAEbmL,EAAU,EAAKttB,MAAMutB,WAAW9Q,EAAK,CACnC3R,SAAU,IACVyW,sBAAsB,EACtBC,qBAAqB,IAIzBpmB,IAAEM,KAAK4xB,GAAS,SAAClkB,EAAK+Y,GACpB/mB,IAAE+mB,GAAQtmB,KAAK,OAAQixB,GACnBE,EACF5xB,IAAE+mB,GAAQtmB,KAAK,SAAU,UAEzBT,IAAE+mB,GAAQ+G,WAAW,aAIzB,EAAKkD,aACH,EAAKoB,oBAAoBF,GAAS1qB,cAWtC3H,KAAKqG,MAAQrG,KAAK2wB,aAAY,SAAC6B,GAC7B,IAAMC,EAAYD,EAAUC,UACtBC,EAAYF,EAAUE,UAExBD,GAAa1oB,SAAS0mB,YAAY,aAAa,EAAOgC,GACtDC,GAAa3oB,SAAS0mB,YAAY,aAAa,EAAOiC,MAQ5D1yB,KAAKyyB,UAAYzyB,KAAK2wB,aAAY,SAAC6B,GACjCzoB,SAAS0mB,YAAY,aAAa,EAAO+B,MAQ3CxyB,KAAK2yB,YAAc3yB,KAAK2wB,aAAY,SAACiC,GACnC,IAAMC,EAAYD,EAAIjmB,MAAM,KAEhB,EAAKukB,eAAenP,iBAC5BG,WAAW,EAAK5d,MAAMwuB,YAAYD,EAAU,GAAIA,EAAU,GAAI,EAAK/yB,aAMzEE,KAAK+yB,YAAc/yB,KAAK2wB,aAAY,WAClC,IAAIrU,EAAUnc,IAAE,EAAK6yB,iBAAiBjhB,SAClCuK,EAAQE,QAAQ,UAAUpb,OAC5Bkb,EAAQE,QAAQ,UAAU7Y,SAE1B2Y,EAAUnc,IAAE,EAAK6yB,iBAAiBC,SAEpC,EAAKnpB,QAAQyR,aAAa,eAAgBe,EAAS,EAAK+I,cAQ1DrlB,KAAKkzB,QAAUlzB,KAAK2wB,aAAY,SAAC/xB,GAC/B,IAAM0d,EAAUnc,IAAE,EAAK6yB,iBACvB1W,EAAQ6W,YAAY,kBAA6B,SAAVv0B,GACvC0d,EAAQ6W,YAAY,mBAA8B,UAAVv0B,GACxC0d,EAAQ2J,IAAI,QAAoB,SAAVrnB,EAAmB,GAAKA,MAOhDoB,KAAKozB,OAASpzB,KAAK2wB,aAAY,SAAC/xB,GAC9B,IAAM0d,EAAUnc,IAAE,EAAK6yB,iBAET,KADdp0B,EAAQ+J,WAAW/J,IAEjB0d,EAAQ2J,IAAI,QAAS,IAErB3J,EAAQ2J,IAAI,CACV5b,MAAe,IAARzL,EAAc,IACrBsD,OAAQ,Q,4DAMH,WAEXlC,KAAKqlB,UAAUvkB,GAAG,WAAW,SAACqb,GAgB5B,GAfIA,EAAM8H,UAAY/kB,GAAI2b,KAAKuJ,OAC7B,EAAKta,QAAQyR,aAAa,QAASY,GAErC,EAAKrS,QAAQyR,aAAa,UAAWY,GAGrC,EAAKoJ,SAAW,EAAKhe,QAAQme,eAC7B,EAAK2N,gBAAiB,EACjBlX,EAAMmX,uBACL,EAAKxzB,QAAQkH,UACf,EAAKqsB,eAAiB,EAAKE,aAAapX,GAExC,EAAKqX,gCAAgCrX,IAGrC,EAAK8U,UAAU,EAAG9U,GAAQ,CAC5B,IAAM0T,EAAY,EAAKqB,eACvB,GAAIrB,EAAUzQ,GAAKyQ,EAAU3Q,IAAO,EAClC,OAAO,EAGX,EAAKiS,eAGD,EAAKrxB,QAAQ2zB,uBACa,IAAxB,EAAKJ,gBACP,EAAK9rB,QAAQie,gBAGhB1kB,GAAG,SAAS,SAACqb,GACd,EAAKgV,eACL,EAAKrnB,QAAQyR,aAAa,QAASY,MAClCrb,GAAG,SAAS,SAACqb,GACd,EAAKgV,eACL,EAAKrnB,QAAQyR,aAAa,QAASY,MAClCrb,GAAG,QAAQ,SAACqb,GACb,EAAKrS,QAAQyR,aAAa,OAAQY,MACjCrb,GAAG,aAAa,SAACqb,GAClB,EAAKrS,QAAQyR,aAAa,YAAaY,MACtCrb,GAAG,WAAW,SAACqb,GAChB,EAAKgV,eACL,EAAK5pB,QAAQie,aACb,EAAK1b,QAAQyR,aAAa,UAAWY,MACpCrb,GAAG,UAAU,SAACqb,GACf,EAAKrS,QAAQyR,aAAa,SAAUY,MACnCrb,GAAG,SAAS,SAACqb,GACd,EAAKgV,eACL,EAAKrnB,QAAQyR,aAAa,QAASY,MAClCrb,GAAG,SAAS,WAET,EAAKmwB,UAAU,IAAM,EAAK1L,UAC5B,EAAKhe,QAAQke,cAAc,EAAKF,aAIpCvlB,KAAKqlB,UAAUzkB,KAAK,aAAcZ,KAAKF,QAAQ4zB,YAE/C1zB,KAAKqlB,UAAUzkB,KAAK,cAAeZ,KAAKF,QAAQ4zB,YAE5C1zB,KAAKF,QAAQ6zB,gBACf3zB,KAAKqlB,UAAUzkB,KAAK,cAAc,GAIpCZ,KAAKqlB,UAAUhlB,KAAKya,GAAIza,KAAKL,KAAK+Z,QAAUe,GAAIpG,WAEhD1U,KAAKqlB,UAAUvkB,GAAGiQ,EAAI/H,eAAgBiE,EAAKD,UAAS,WAClD,EAAKlD,QAAQyR,aAAa,SAAU,EAAK8J,UAAUhlB,OAAQ,EAAKglB,aAC/D,KAEHrlB,KAAKqlB,UAAUvkB,GAAG,WAAW,SAACqb,GAC5B,EAAKrS,QAAQyR,aAAa,UAAWY,MACpCrb,GAAG,YAAY,SAACqb,GACjB,EAAKrS,QAAQyR,aAAa,WAAYY,MAGpCnc,KAAKF,QAAQ8zB,QACX5zB,KAAKF,QAAQ+zB,qBACf7zB,KAAK4vB,QAAQ9uB,GAAG,eAAe,SAACqb,GAE9B,OADA,EAAKrS,QAAQyR,aAAa,cAAeY,IAClC,MAIPnc,KAAKF,QAAQuK,OACfrK,KAAK4vB,QAAQkE,WAAW9zB,KAAKF,QAAQuK,OAEnCrK,KAAKF,QAAQoC,QACflC,KAAKqlB,UAAU/L,YAAYtZ,KAAKF,QAAQoC,QAEtClC,KAAKF,QAAQi0B,WACf/zB,KAAKqlB,UAAUY,IAAI,aAAcjmB,KAAKF,QAAQi0B,WAE5C/zB,KAAKF,QAAQk0B,WACfh0B,KAAKqlB,UAAUY,IAAI,aAAcjmB,KAAKF,QAAQk0B,YAIlDh0B,KAAKuH,QAAQie,aACbxlB,KAAKmxB,iB,gCAILnxB,KAAKqlB,UAAU1L,Q,mCAGJwC,GACX,IAAM8X,EAASj0B,KAAKF,QAAQm0B,OAAOljB,EAAI9H,MAAQ,MAAQ,MACjDwQ,EAAO,GAET0C,EAAM+X,SAAWza,EAAKtK,KAAK,OAC3BgN,EAAMgY,UAAYhY,EAAMiY,QAAU3a,EAAKtK,KAAK,QAC5CgN,EAAMkY,UAAY5a,EAAKtK,KAAK,SAEhC,IAAMmlB,EAAUp1B,GAAI+lB,aAAa9I,EAAM8H,SACnCqQ,GACF7a,EAAKtK,KAAKmlB,GAGZ,IAAMC,EAAYN,EAAOxa,EAAK1M,KAAK,MAEnC,GAAgB,QAAZunB,GAAsBt0B,KAAKF,QAAQ00B,WAEhC,GAAID,GACT,IAAuC,IAAnCv0B,KAAK8J,QAAQ2B,OAAO8oB,GAGtB,OAFApY,EAAME,kBAEC,OAEAnd,GAAI8kB,OAAO7H,EAAM8H,UAC1BjkB,KAAK0wB,oBARL1wB,KAAK0wB,eAUP,OAAO,I,sDAGuBvU,IAEzBA,EAAMgY,SAAWhY,EAAM+X,UAC1B1uB,EAAMwI,SAAS,CAAC,GAAI,GAAI,IAAKmO,EAAM8H,UACnC9H,EAAME,mB,gCAIAoY,EAAKtY,GAGb,OAFAsY,EAAMA,GAAO,QAEQ,IAAVtY,KACLjd,GAAIqlB,OAAOpI,EAAM8H,UACjB/kB,GAAI0lB,aAAazI,EAAM8H,UACtB9H,EAAMgY,SAAWhY,EAAM+X,SACxB1uB,EAAMwI,SAAS,CAAC9O,GAAI2b,KAAKqJ,UAAWhlB,GAAI2b,KAAKyJ,QAASnI,EAAM8H,YAK9DjkB,KAAKF,QAAQ40B,cAAgB,GAC1B10B,KAAKqlB,UAAU9M,OAAOnX,OAASqzB,EAAOz0B,KAAKF,QAAQ40B,gB,oCAc1D,OAFA10B,KAAK+e,QACL/e,KAAKmxB,eACEnxB,KAAKkxB,iB,0CASMyD,GAClB,IACM7d,EADawO,GAAM5B,qBAAqBle,EAAMmI,KAAKgnB,IAC3B5T,gBAExBhK,EADWuO,GAAM3B,oBAAoBne,EAAMqI,KAAK8mB,IAC5B9T,cAE1B,OAAOyE,GAAMrmB,OACX6X,EAAWpH,KACXoH,EAAWxE,OACXyE,EAASrH,KACTqH,EAASzE,U,mCAYAkP,GACPA,EACFxhB,KAAK6vB,UAAYrO,GAEjBxhB,KAAK6vB,UAAYvK,GAAMrmB,OAAOe,KAAK2b,UAE2B,IAA1Dxb,IAAEH,KAAK6vB,UAAU5Q,IAAIzC,QAAQ,kBAAkBpb,SACjDpB,KAAK6vB,UAAYvK,GAAMtC,sBAAsBhjB,KAAK2b,c,qCAiBtD,OAHK3b,KAAK6vB,WACR7vB,KAAKmxB,eAEAnxB,KAAK6vB,Y,gCAUJ+E,GACJA,GACF50B,KAAKkxB,eAAezT,WAAW9V,W,qCAU7B3H,KAAK6vB,YACP7vB,KAAK6vB,UAAUloB,SACf3H,KAAK+e,W,iCAIErP,GACT1P,KAAKqlB,UAAU7kB,KAAK,SAAUkP,K,oCAI9B1P,KAAKqlB,UAAU5K,WAAW,Y,sCAI1B,OAAOza,KAAKqlB,UAAU7kB,KAAK,Y,qCAU3B,IAAIghB,EAAM8D,GAAMrmB,SAIhB,OAHIuiB,IACFA,EAAMA,EAAIE,aAELF,EAAMxhB,KAAK+E,MAAMyS,QAAQgK,GAAOxhB,KAAK+E,MAAM6hB,SAAS5mB,KAAKqlB,a,oCASpDnlB,GACZ,OAAOF,KAAK+E,MAAM6hB,SAAS1mB,K,6BAO3BF,KAAK8J,QAAQyR,aAAa,iBAAkBvb,KAAKqlB,UAAUhlB,QAC3DL,KAAKuH,QAAQC,OACbxH,KAAK8J,QAAQyR,aAAa,SAAUvb,KAAKqlB,UAAUhlB,OAAQL,KAAKqlB,a,+BAOhErlB,KAAK8J,QAAQyR,aAAa,iBAAkBvb,KAAKqlB,UAAUhlB,QAC3DL,KAAKuH,QAAQstB,SACb70B,KAAK8J,QAAQyR,aAAa,SAAUvb,KAAKqlB,UAAUhlB,OAAQL,KAAKqlB,a,6BAOhErlB,KAAK8J,QAAQyR,aAAa,iBAAkBvb,KAAKqlB,UAAUhlB,QAC3DL,KAAKuH,QAAQE,OACbzH,KAAK8J,QAAQyR,aAAa,SAAUvb,KAAKqlB,UAAUhlB,OAAQL,KAAKqlB,a,sCAOhErlB,KAAK8J,QAAQyR,aAAa,iBAAkBvb,KAAKqlB,UAAUhlB,QAG3D0J,SAAS0mB,YAAY,gBAAgB,EAAOzwB,KAAKF,QAAQg1B,cAGzD90B,KAAK+e,U,mCAOMgW,GACX/0B,KAAKg1B,mBACLh1B,KAAKuH,QAAQie,aACRuP,GACH/0B,KAAK8J,QAAQyR,aAAa,SAAUvb,KAAKqlB,UAAUhlB,OAAQL,KAAKqlB,a,4BAQlE,IAAM7D,EAAMxhB,KAAKkxB,eACjB,GAAI1P,EAAIV,eAAiBU,EAAI/B,WAC3Bzf,KAAKsE,MAAM0kB,IAAIxH,OACV,CACL,GAA6B,IAAzBxhB,KAAKF,QAAQm1B,QACf,OAAO,EAGJj1B,KAAKixB,UAAUjxB,KAAKF,QAAQm1B,WAC/Bj1B,KAAKwwB,gBACLxwB,KAAK8vB,OAAOoF,UAAU1T,EAAKxhB,KAAKF,QAAQm1B,SACxCj1B,KAAK0wB,mB,8BAST,IAAMlP,EAAMxhB,KAAKkxB,eACjB,GAAI1P,EAAIV,eAAiBU,EAAI/B,WAC3Bzf,KAAKsE,MAAM0kB,IAAIxH,GAAK,QAEpB,GAA6B,IAAzBxhB,KAAKF,QAAQm1B,QACf,OAAO,I,kCAQDvrB,GACV,OAAO,WACL1J,KAAKwwB,gBACL9mB,EAAG0B,MAAMpL,KAAMsB,WACftB,KAAK0wB,kB,kCAWGyE,EAAKC,GAAO,IChrBE1xB,EDgrBF,OACtB,OCjrBwBA,EDirBLyxB,EChrBdh1B,IAAEk1B,UAAS,SAACC,GACjB,IAAMC,EAAOp1B,IAAE,SAEfo1B,EAAKC,IAAI,QAAQ,WACfD,EAAK5b,IAAI,eACT2b,EAASG,QAAQF,MAChBC,IAAI,eAAe,WACpBD,EAAK5b,IAAI,QAAQsZ,SACjBqC,EAASI,OAAOH,MACftP,IAAI,CACL0P,QAAS,SACRC,SAAS7rB,SAASoT,MAAMvc,KAAK,MAAO8C,MACtCmyB,WDoqB8BC,MAAK,SAACC,GACnC,EAAKvF,gBAEgB,mBAAV4E,EACTA,EAAMW,IAEe,iBAAVX,GACTW,EAAOn1B,KAAK,gBAAiBw0B,GAE/BW,EAAO9P,IAAI,QAAS3F,KAAK0V,IAAI,EAAK3Q,UAAUhb,QAAS0rB,EAAO1rB,WAG9D0rB,EAAOE,OACP,EAAK/E,eAAehP,WAAW6T,EAAO,IACtC,EAAK5E,aAAa7L,GAAM3B,oBAAoBoS,EAAO,IAAIpuB,UACvD,EAAK+oB,kBACJ1lB,MAAK,SAACyX,GACP,EAAK3Y,QAAQyR,aAAa,qBAAsBkH,Q,4CAQ9ByT,GAAO,WAC3B/1B,IAAEM,KAAKy1B,GAAO,SAAC/nB,EAAKgoB,GAClB,IAAMC,EAAWD,EAAKj4B,KAClB,EAAK4B,QAAQu2B,sBAAwB,EAAKv2B,QAAQu2B,qBAAuBF,EAAK7zB,KAChF,EAAKwH,QAAQyR,aAAa,qBAAsB,EAAK5Z,KAAKa,MAAMiB,sBCpuBjE,SAA2B0yB,GAChC,OAAOh2B,IAAEk1B,UAAS,SAACC,GACjBn1B,IAAEyB,OAAO,IAAI00B,WAAc,CACzBC,OAAQ,SAAC9T,GACP,IAAM+T,EAAU/T,EAAElG,OAAO1N,OACzBymB,EAASG,QAAQe,IAEnBC,QAAS,SAACC,GACRpB,EAASI,OAAOgB,MAEjBC,cAAcR,MAChBN,UD2tBGe,CAAkBT,GAAML,MAAK,SAACU,GAC5B,OAAO,EAAKK,YAAYL,EAASJ,MAChCprB,MAAK,WACN,EAAKlB,QAAQyR,aAAa,8B,6CAUX2a,GACHl2B,KAAKF,QAAQ+b,UAEjBib,cACZ92B,KAAK8J,QAAQyR,aAAa,eAAgB2a,GAG1Cl2B,KAAK+2B,sBAAsBb,K,wCAS7B,IAAI1U,EAAMxhB,KAAKkxB,eAOf,OAJI1P,EAAIhC,eACNgC,EAAM8D,GAAMrC,eAAenI,GAAIzJ,SAASmQ,EAAIvC,GAAInE,GAAIlK,YAG/C4Q,EAAIW,a,oCAGC+K,EAAS5Q,GAKrB,GAHAvS,SAAS0mB,YAAY,eAAe,EAAO1f,EAAIxI,OAAS,IAAM2kB,EAAU,IAAMA,GAG1E5Q,GAAWA,EAAQlb,SAEjBkb,EAAQ,GAAG4Q,QAAQpgB,gBAAkBogB,EAAQpgB,gBAC/CwP,EAAUA,EAAQtb,KAAKksB,IAGrB5Q,GAAWA,EAAQlb,QAAQ,CAC7B,IAAMd,EAAYgc,EAAQ,GAAGhc,WAAa,GAC1C,GAAIA,EAAW,CACb,IAAM02B,EAAeh3B,KAAKuK,cAEVpK,IAAE,CAAC62B,EAAa/X,GAAI+X,EAAa7X,KAAK3C,QAAQ0Q,GACtD3sB,SAASD,O,mCAOvBN,KAAKgxB,YAAY,O,kCAGPzU,EAAQ3d,GAClB,IAAM4iB,EAAMxhB,KAAKkxB,eAEjB,GAAY,KAAR1P,EAAY,CACd,IAAMyV,EAAQj3B,KAAK+E,MAAMutB,WAAW9Q,GAMpC,GALAxhB,KAAK4vB,QAAQ5uB,KAAK,uBAAuBX,KAAK,IAC9CF,IAAE82B,GAAOhR,IAAI1J,EAAQ3d,GAIjB4iB,EAAIV,cAAe,CACrB,IAAMoW,EAAY1xB,EAAMmI,KAAKspB,GACzBC,IAAcpc,GAAI9J,WAAWkmB,KAC/BA,EAAU/lB,UAAY2J,GAAItG,qBAC1B8Q,GAAMrC,eAAeiU,EAAUxZ,YAAY/V,SAC3C3H,KAAKmxB,eACLnxB,KAAKqlB,UAAU7kB,KApzBP,QAozBuB02B,SAGjCl3B,KAAKmxB,aACHnxB,KAAKuyB,oBAAoB0E,GAAOtvB,cAG/B,CACL,IAAMwvB,EAAmBh3B,IAAE6a,MAC3Bhb,KAAK4vB,QAAQ5uB,KAAK,uBAAuBX,KAAK,+BAAiC82B,EAAmB,8BAAgCn3B,KAAK2B,KAAKiG,OAAOC,YAAc,UACjK4F,YAAW,WAAatN,IAAE,uBAAyBg3B,GAAkBxzB,WAAa,Q,+BAUpF,IAAI6d,EAAMxhB,KAAKkxB,eACf,GAAI1P,EAAIhC,aAAc,CACpB,IAAM0H,EAASpM,GAAIzJ,SAASmQ,EAAIvC,GAAInE,GAAIlK,WACxC4Q,EAAM8D,GAAMrC,eAAeiE,IACvBvf,SACJ3H,KAAKmxB,eAELnxB,KAAKwwB,gBACLzmB,SAAS0mB,YAAY,UACrBzwB,KAAK0wB,kB,oCAcP,IAAMlP,EAAMxhB,KAAKkxB,eAAekG,OAAOtc,GAAIlK,UAErCymB,EAAUl3B,IAAEqF,EAAMmI,KAAK6T,EAAI7P,MAAMmJ,GAAIlK,YACrCghB,EAAW,CACftM,MAAO9D,EACPjJ,KAAMiJ,EAAIW,WACVze,IAAK2zB,EAAQj2B,OAASi2B,EAAQz2B,KAAK,QAAU,IAS/C,OALIy2B,EAAQj2B,SAEVwwB,EAASG,YAAyC,WAA3BsF,EAAQz2B,KAAK,WAG/BgxB,I,6BAGFlf,GACL,IAAM8O,EAAMxhB,KAAKkxB,aAAalxB,KAAKqlB,WAC/B7D,EAAIV,eAAiBU,EAAI/B,aAC3Bzf,KAAKwwB,gBACLxwB,KAAKsE,MAAMgzB,OAAO9V,EAAK9O,GACvB1S,KAAK0wB,kB,6BAIFhe,GACL,IAAM8O,EAAMxhB,KAAKkxB,aAAalxB,KAAKqlB,WAC/B7D,EAAIV,eAAiBU,EAAI/B,aAC3Bzf,KAAKwwB,gBACLxwB,KAAKsE,MAAMizB,OAAO/V,EAAK9O,GACvB1S,KAAK0wB,kB,kCAKP,IAAMlP,EAAMxhB,KAAKkxB,aAAalxB,KAAKqlB,WAC/B7D,EAAIV,eAAiBU,EAAI/B,aAC3Bzf,KAAKwwB,gBACLxwB,KAAKsE,MAAMkzB,UAAUhW,GACrBxhB,KAAK0wB,kB,kCAKP,IAAMlP,EAAMxhB,KAAKkxB,aAAalxB,KAAKqlB,WAC/B7D,EAAIV,eAAiBU,EAAI/B,aAC3Bzf,KAAKwwB,gBACLxwB,KAAKsE,MAAMmzB,UAAUjW,GACrBxhB,KAAK0wB,kB,oCAKP,IAAMlP,EAAMxhB,KAAKkxB,aAAalxB,KAAKqlB,WAC/B7D,EAAIV,eAAiBU,EAAI/B,aAC3Bzf,KAAKwwB,gBACLxwB,KAAKsE,MAAMozB,YAAYlW,GACvBxhB,KAAK0wB,kB,+BASArX,EAAKiD,EAASqb,GACrB,IAAIC,EACJ,GAAID,EAAY,CACd,IAAME,EAAWxe,EAAIye,EAAIze,EAAI0e,EACvBC,EAAQ1b,EAAQ9b,KAAK,SAC3Bo3B,EAAY,CACVvtB,MAAO2tB,EAAQH,EAAWxe,EAAI0e,EAAI1e,EAAIye,EAAIE,EAC1C91B,OAAQ81B,EAAQH,EAAWxe,EAAI0e,EAAIC,EAAQ3e,EAAIye,QAGjDF,EAAY,CACVvtB,MAAOgP,EAAI0e,EACX71B,OAAQmX,EAAIye,GAIhBxb,EAAQ2J,IAAI2R,K,iCAOZ,OAAO53B,KAAKqlB,UAAU4S,GAAG,Y,8BASpBj4B,KAAKk4B,YACRl4B,KAAKqlB,UAAUtG,U,gCASjB,OAAOjE,GAAIhM,QAAQ9O,KAAKqlB,UAAU,KAAOvK,GAAIpG,YAAc1U,KAAKqlB,UAAUhlB,S,8BAO1EL,KAAK8J,QAAQ2B,OAAO,OAAQqP,GAAIpG,a,yCAOhC1U,KAAKqlB,UAAU,GAAG3D,iB,6MEv+BDyW,G,WACnB,WAAYruB,I,4FAAS,SACnB9J,KAAK8J,QAAUA,EACf9J,KAAKqlB,UAAYvb,EAAQmQ,WAAW0B,S,4DAIpC3b,KAAKqlB,UAAUvkB,GAAG,QAASd,KAAKo4B,aAAaj5B,KAAKa,S,mCAQvCmc,GAAO,WACZkc,EAAgBlc,EAAMmc,cAAcD,cAE1C,GAAIA,GAAiBA,EAAcE,OAASF,EAAcE,MAAMn3B,OAAQ,CACtE,IAAMoK,EAAO6sB,EAAcE,MAAMn3B,OAAS,EAAIi3B,EAAcE,MAAM,GAAK/yB,EAAMmI,KAAK0qB,EAAcE,OAC9E,SAAd/sB,EAAKgtB,OAAoD,IAAjChtB,EAAK+S,KAAKpV,QAAQ,WAE5CnJ,KAAK8J,QAAQ2B,OAAO,gCAAiC,CAACD,EAAKitB,cAC3Dtc,EAAME,kBACiB,WAAd7Q,EAAKgtB,MAEVx4B,KAAK8J,QAAQ2B,OAAO,mBAAoB4sB,EAAcK,QAAQ,QAAQt3B,SACxE+a,EAAME,sBAGL,GAAI9e,OAAO86B,cAAe,CAE/B,IAAI9f,EAAOhb,OAAO86B,cAAcK,QAAQ,QACpC14B,KAAK8J,QAAQ2B,OAAO,mBAAoB8M,EAAKnX,SAC/C+a,EAAME,iBAIV5O,YAAW,WACT,EAAK3D,QAAQ2B,OAAO,yBACnB,S,6MCxCcktB,G,WACnB,WAAY7uB,I,4FAAS,SACnB9J,KAAK8J,QAAUA,EACf9J,KAAK44B,eAAiBz4B,IAAE4J,UACxB/J,KAAK4vB,QAAU9lB,EAAQmQ,WAAWgB,OAClCjb,KAAKqlB,UAAYvb,EAAQmQ,WAAW0B,SACpC3b,KAAKF,QAAUgK,EAAQhK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ4e,SACzB1e,KAAK64B,sBAAwB,GAE7B74B,KAAK84B,UAAY34B,IAAE,CACjB,8BACE,4CACF,UACA4M,KAAK,KAAKgsB,UAAU/4B,KAAK4vB,S,4DAOvB5vB,KAAKF,QAAQk5B,oBAEfh5B,KAAK64B,sBAAsBI,OAAS,SAACxW,GACnCA,EAAEpG,kBAGJrc,KAAK44B,eAAiB54B,KAAK84B,UAC3B94B,KAAK44B,eAAe93B,GAAG,OAAQd,KAAK64B,sBAAsBI,SAE1Dj5B,KAAKk5B,2B,+CAOgB,WACnBtqB,EAAazO,MACXg5B,EAAmBn5B,KAAK84B,UAAU93B,KAAK,0BAE7ChB,KAAK64B,sBAAsBO,YAAc,SAAC3W,GACxC,IAAM4W,EAAa,EAAKvvB,QAAQ2B,OAAO,wBACjC6tB,EAAgB,EAAK1J,QAAQvlB,QAAU,GAAK,EAAKulB,QAAQ1tB,SAAW,EACrEm3B,GAAezqB,EAAWxN,SAAUk4B,IACvC,EAAK1J,QAAQrvB,SAAS,YACtB,EAAKu4B,UAAUzuB,MAAM,EAAKulB,QAAQvlB,SAClC,EAAKyuB,UAAU52B,OAAO,EAAK0tB,QAAQ1tB,UACnCi3B,EAAiB5gB,KAAK,EAAK5W,KAAKa,MAAMa,gBAExCuL,EAAaA,EAAW2qB,IAAI9W,EAAElG,SAGhCvc,KAAK64B,sBAAsBW,YAAc,SAAC/W,IACxC7T,EAAaA,EAAW1D,IAAIuX,EAAElG,SAGdnb,QAAgC,SAAtBqhB,EAAElG,OAAO1M,WACjCjB,EAAazO,MACb,EAAKyvB,QAAQ6J,YAAY,cAI7Bz5B,KAAK64B,sBAAsBI,OAAS,WAClCrqB,EAAazO,MACb,EAAKyvB,QAAQ6J,YAAY,aAK3Bz5B,KAAK44B,eAAe93B,GAAG,YAAad,KAAK64B,sBAAsBO,aAC5Dt4B,GAAG,YAAad,KAAK64B,sBAAsBW,aAC3C14B,GAAG,OAAQd,KAAK64B,sBAAsBI,QAGzCj5B,KAAK84B,UAAUh4B,GAAG,aAAa,WAC7B,EAAKg4B,UAAUv4B,SAAS,SACxB44B,EAAiB5gB,KAAK,EAAK5W,KAAKa,MAAMc,cACrCxC,GAAG,aAAa,WACjB,EAAKg4B,UAAUW,YAAY,SAC3BN,EAAiB5gB,KAAK,EAAK5W,KAAKa,MAAMa,kBAIxCrD,KAAK84B,UAAUh4B,GAAG,QAAQ,SAACqb,GACzB,IAAMud,EAAevd,EAAMmc,cAAcoB,aAGzCvd,EAAME,iBAEFqd,GAAgBA,EAAaxD,OAASwD,EAAaxD,MAAM90B,QAC3D,EAAKikB,UAAUtG,QACf,EAAKjV,QAAQ2B,OAAO,gCAAiCiuB,EAAaxD,QAElE/1B,IAAEM,KAAKi5B,EAAaC,OAAO,SAACxrB,EAAKoQ,GAE/B,KAAIA,EAAKpW,cAAcgB,QAAQ,UAAY,GAA3C,CAGA,IAAMywB,EAAUF,EAAahB,QAAQna,GAEjCA,EAAKpW,cAAcgB,QAAQ,SAAW,EACxC,EAAKW,QAAQ2B,OAAO,mBAAoBmuB,GAExCz5B,IAAEy5B,GAASn5B,MAAK,SAAC0N,EAAK3C,GACpB,EAAK1B,QAAQ2B,OAAO,oBAAqBD,aAKhD1K,GAAG,YAAY,K,gCAGV,WACRzC,OAAOob,KAAKzZ,KAAK64B,uBAAuB53B,SAAQ,SAAC/B,GAC/C,EAAK05B,eAAejf,IAAIza,EAAI26B,OAAO,GAAG1xB,cAAe,EAAK0wB,sBAAsB35B,OAElFc,KAAK64B,sBAAwB,Q,krCCjHZiB,G,WACnB,WAAYhwB,I,4FAAS,SACnB9J,KAAK8J,QAAUA,EACf9J,KAAK4vB,QAAU9lB,EAAQmQ,WAAWgB,OAClCjb,KAAKqlB,UAAYvb,EAAQmQ,WAAW0B,SACpC3b,KAAK+5B,SAAWjwB,EAAQmQ,WAAWyB,QACnC1b,KAAKF,QAAUgK,EAAQhK,QACvBE,KAAKg6B,sBAAwBz8B,OAAO08B,WAEhCj6B,KAAKF,QAAQo6B,WAAWF,wBAC1Bh6B,KAAKg6B,sBAAwBh6B,KAAKF,QAAQo6B,WAAWF,uB,oDAIpD35B,GACH,IAAMg5B,EAAar5B,KAAKwb,cAClBye,EAAaj6B,KAAKg6B,sBAEpBX,IACEh5B,EACE45B,EACFj6B,KAAK+5B,SAASv5B,KAAK,YAAY25B,SAASC,SAAS/5B,GAEjDL,KAAK+5B,SAASzlB,IAAIjU,GAGhB45B,GACFj6B,KAAK+5B,SAASv5B,KAAK,YAAY65B,U,mCAM1B,WACXr6B,KAAK+5B,SAASj5B,GAAG,SAAS,SAACqb,GACrBA,EAAM8H,UAAY/kB,GAAI2b,KAAKyf,QAC7B,EAAKC,kB,oCAST,OAAOv6B,KAAK4vB,QAAQjgB,SAAS,c,+BAOzB3P,KAAKwb,cACPxb,KAAKu6B,aAELv6B,KAAKw6B,WAEPx6B,KAAK8J,QAAQyR,aAAa,sB,6BAQrB3c,GACL,GAAIoB,KAAKF,QAAQ26B,iBAEf77B,EAAQA,EAAM2V,QAAQvU,KAAKF,QAAQ46B,oBAAqB,IAEpD16B,KAAKF,QAAQ66B,sBAAsB,CACrC,IAAMC,EAAY56B,KAAKF,QAAQ+6B,2BAA2BjZ,OAAO5hB,KAAKF,QAAQg7B,gCAC9El8B,EAAQA,EAAM2V,QAAQ,qCAAqC,SAASwmB,GAElE,GAAI,uDAAuDvyB,KAAKuyB,GAC9D,MAAO,GAH8D,WAKrDH,GALqD,IAKvE,2BAA6B,KAAlBzF,EAAkB,QAE3B,GAAK,IAAI6F,OAAO,oBAAwB7F,EAAI5gB,QAAQ,yBAA0B,QAAU,UAAY/L,KAAKuyB,GACvG,OAAOA,GAR4D,8BAWvE,MAAO,MAIb,OAAOn8B,I,iCAME,WACHq7B,EAAaj6B,KAAKg6B,sBAWxB,GAVAh6B,KAAK+5B,SAASzlB,IAAIwG,GAAIza,KAAKL,KAAKqlB,UAAWrlB,KAAKF,QAAQm7B,eACxDj7B,KAAK+5B,SAAS73B,OAAOlC,KAAKqlB,UAAUnjB,UAEpClC,KAAK8J,QAAQ2B,OAAO,0BAA0B,GAC9CzL,KAAK8J,QAAQ2B,OAAO,6BAA6B,GAEjDzL,KAAK4vB,QAAQrvB,SAAS,YACtBP,KAAK+5B,SAAShb,QAGVkb,EAAY,CACd,IAAMiB,EAAWjB,EAAWkB,aAAan7B,KAAK+5B,SAAS,GAAI/5B,KAAKF,QAAQo6B,YAGxE,GAAIl6B,KAAKF,QAAQo6B,WAAWkB,KAAM,CAChC,IAAMC,EAAS,IAAIpB,EAAWqB,WAAWt7B,KAAKF,QAAQo6B,WAAWkB,MACjEF,EAASK,WAAaF,EACtBH,EAASp6B,GAAG,kBAAkB,SAAC06B,GAC7BH,EAAOI,eAAeD,MAI1BN,EAASp6B,GAAG,QAAQ,SAACqb,GACnB,EAAKrS,QAAQyR,aAAa,gBAAiB2f,EAASQ,WAAYvf,MAElE+e,EAASp6B,GAAG,UAAU,WACpB,EAAKgJ,QAAQyR,aAAa,kBAAmB2f,EAASQ,WAAYR,MAIpEA,EAASS,QAAQ,KAAM37B,KAAKqlB,UAAU/L,eACtCtZ,KAAK+5B,SAASv5B,KAAK,WAAY06B,QAE/Bl7B,KAAK+5B,SAASj5B,GAAG,QAAQ,SAACqb,GACxB,EAAKrS,QAAQyR,aAAa,gBAAiB,EAAKwe,SAASzlB,MAAO6H,MAElEnc,KAAK+5B,SAASj5B,GAAG,SAAS,WACxB,EAAKgJ,QAAQyR,aAAa,kBAAmB,EAAKwe,SAASzlB,MAAO,EAAKylB,e,mCAW3E,GAFmB/5B,KAAKg6B,sBAER,CACd,IAAMkB,EAAWl7B,KAAK+5B,SAASv5B,KAAK,YACpCR,KAAK+5B,SAASzlB,IAAI4mB,EAASQ,YAC3BR,EAASU,aAGX,IAAMh9B,EAAQoB,KAAK67B,OAAO/gB,GAAIlc,MAAMoB,KAAK+5B,SAAU/5B,KAAKF,QAAQm7B,eAAiBngB,GAAIpG,WAC/EonB,EAAW97B,KAAKqlB,UAAUhlB,SAAWzB,EAE3CoB,KAAKqlB,UAAUhlB,KAAKzB,GACpBoB,KAAKqlB,UAAUnjB,OAAOlC,KAAKF,QAAQoC,OAASlC,KAAK+5B,SAAS73B,SAAW,QACrElC,KAAK4vB,QAAQ6J,YAAY,YAErBqC,GACF97B,KAAK8J,QAAQyR,aAAa,SAAUvb,KAAKqlB,UAAUhlB,OAAQL,KAAKqlB,WAGlErlB,KAAKqlB,UAAUtG,QAEf/e,KAAK8J,QAAQ2B,OAAO,0BAA0B,GAC9CzL,KAAK8J,QAAQ2B,OAAO,6BAA6B,K,gCAI7CzL,KAAKwb,eACPxb,KAAKu6B,kB,yMC7KX,IAEqBwB,G,WACnB,WAAYjyB,I,4FAAS,SACnB9J,KAAKkM,UAAY/L,IAAE4J,UACnB/J,KAAKg8B,WAAalyB,EAAQmQ,WAAWgiB,UACrCj8B,KAAKqlB,UAAYvb,EAAQmQ,WAAW0B,SACpC3b,KAAKF,QAAUgK,EAAQhK,Q,4DAGZ,WACPE,KAAKF,QAAQ8zB,SAAW5zB,KAAKF,QAAQo8B,oBACvCl8B,KAAKkc,UAIPlc,KAAKg8B,WAAWl7B,GAAG,aAAa,SAACqb,GAC/BA,EAAME,iBACNF,EAAMggB,kBAEN,IAAMC,EAAc,EAAK/W,UAAU/S,SAASnG,IAAM,EAAKD,UAAUE,YAC3DiwB,EAAc,SAAClgB,GACnB,IAAIja,EAASia,EAAMmgB,SAAWF,EAtBb,IAwBjBl6B,EAAU,EAAKpC,QAAQy8B,UAAY,EAAKjc,KAAKkc,IAAIt6B,EAAQ,EAAKpC,QAAQy8B,WAAar6B,EACnFA,EAAU,EAAKpC,QAAQi0B,UAAY,EAAKzT,KAAK0V,IAAI9zB,EAAQ,EAAKpC,QAAQi0B,WAAa7xB,EAEnF,EAAKmjB,UAAUnjB,OAAOA,IAGxB,EAAKgK,UAAUpL,GAAG,YAAau7B,GAAa7G,IAAI,WAAW,WACzD,EAAKtpB,UAAUyN,IAAI,YAAa0iB,W,gCAMpCr8B,KAAKg8B,WAAWriB,MAChB3Z,KAAKg8B,WAAWz7B,SAAS,e,6MCrCRk8B,G,WACnB,WAAY3yB,GAAS,Y,4FAAA,SACnB9J,KAAK8J,QAAUA,EAEf9J,KAAK4vB,QAAU9lB,EAAQmQ,WAAWgB,OAClCjb,KAAK08B,SAAW5yB,EAAQmQ,WAAW0iB,QACnC38B,KAAKqlB,UAAYvb,EAAQmQ,WAAW0B,SACpC3b,KAAK+5B,SAAWjwB,EAAQmQ,WAAWyB,QAEnC1b,KAAK48B,QAAUz8B,IAAE5C,QACjByC,KAAK68B,WAAa18B,IAAE,cAEpBH,KAAK88B,SAAW,WACd,EAAKC,SAAS,CACZC,EAAG,EAAKJ,QAAQ16B,SAAW,EAAKw6B,SAASpjB,iB,wDAKtChX,GACPtC,KAAKqlB,UAAUY,IAAI,SAAU3jB,EAAK06B,GAClCh9B,KAAK+5B,SAAS9T,IAAI,SAAU3jB,EAAK06B,GAC7Bh9B,KAAK+5B,SAASv5B,KAAK,aACrBR,KAAK+5B,SAASv5B,KAAK,YAAYy8B,QAAQ,KAAM36B,EAAK06B,K,+BAQpDh9B,KAAK4vB,QAAQuD,YAAY,cACrBnzB,KAAKk9B,gBACPl9B,KAAKqlB,UAAU7kB,KAAK,YAAaR,KAAKqlB,UAAUY,IAAI,WACpDjmB,KAAKqlB,UAAU7kB,KAAK,eAAgBR,KAAKqlB,UAAUY,IAAI,cACvDjmB,KAAKqlB,UAAUY,IAAI,YAAa,IAChCjmB,KAAK48B,QAAQ97B,GAAG,SAAUd,KAAK88B,UAAUhhB,QAAQ,UACjD9b,KAAK68B,WAAW5W,IAAI,WAAY,YAEhCjmB,KAAK48B,QAAQjjB,IAAI,SAAU3Z,KAAK88B,UAChC98B,KAAK+8B,SAAS,CAAEC,EAAGh9B,KAAKqlB,UAAU7kB,KAAK,eACvCR,KAAKqlB,UAAUY,IAAI,YAAajmB,KAAKqlB,UAAUY,IAAI,iBACnDjmB,KAAK68B,WAAW5W,IAAI,WAAY,YAGlCjmB,KAAK8J,QAAQ2B,OAAO,2BAA4BzL,KAAKk9B,kB,qCAIrD,OAAOl9B,KAAK4vB,QAAQjgB,SAAS,mB,6MChDZwtB,G,WACnB,WAAYrzB,GAAS,Y,4FAAA,SACnB9J,KAAK8J,QAAUA,EACf9J,KAAKkM,UAAY/L,IAAE4J,UACnB/J,KAAKo9B,aAAetzB,EAAQmQ,WAAWojB,YACvCr9B,KAAKF,QAAUgK,EAAQhK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ4e,SAEzB1e,KAAKwZ,OAAS,CACZ,uBAAwB,SAAC8jB,EAAI7a,GACvB,EAAK8a,OAAO9a,EAAElG,OAAQkG,IACxBA,EAAEpG,kBAGN,+EAAgF,WAC9E,EAAKkhB,UAEP,qCAAsC,WACpC,EAAKhjB,QAEP,8BAA+B,WAC7B,EAAKgjB,W,4DAKE,WACXv9B,KAAKw9B,QAAUr9B,IAAE,CACf,4BACE,uCACE,gDACA,0DACA,0DACA,0DACA,eACGH,KAAKF,QAAQ29B,mBAAqB,sBAAwB,sBAC7D,2BACCz9B,KAAKF,QAAQ29B,mBAAqB,GAAK,kDAC1C,SACF,UACA1wB,KAAK,KAAKgsB,UAAU/4B,KAAKo9B,cAE3Bp9B,KAAKw9B,QAAQ18B,GAAG,aAAa,SAACqb,GAC5B,GAAIrB,GAAInG,gBAAgBwH,EAAMI,QAAS,CACrCJ,EAAME,iBACNF,EAAMggB,kBAEN,IAAM7f,EAAU,EAAKkhB,QAAQx8B,KAAK,2BAA2BR,KAAK,UAC5Dk9B,EAAWphB,EAAQhK,SACnBlG,EAAY,EAAKF,UAAUE,YAE3BiwB,EAAc,SAAClgB,GACnB,EAAKrS,QAAQ2B,OAAO,kBAAmB,CACrCssB,EAAG5b,EAAMwhB,QAAUD,EAASz3B,KAC5B6xB,EAAG3b,EAAMmgB,SAAWoB,EAASvxB,IAAMC,IAClCkQ,GAAUH,EAAMkY,UAEnB,EAAKkJ,OAAOjhB,EAAQ,GAAIH,IAG1B,EAAKjQ,UACFpL,GAAG,YAAau7B,GAChB7G,IAAI,WAAW,SAAC/S,GACfA,EAAEpG,iBACF,EAAKnQ,UAAUyN,IAAI,YAAa0iB,GAChC,EAAKvyB,QAAQ2B,OAAO,0BAGnB6Q,EAAQ9b,KAAK,UAChB8b,EAAQ9b,KAAK,QAAS8b,EAAQpa,SAAWoa,EAAQjS,aAMvDrK,KAAKw9B,QAAQ18B,GAAG,SAAS,SAAC2hB,GACxBA,EAAEpG,iBACF,EAAKkhB,c,gCAKPv9B,KAAKw9B,QAAQ75B,W,6BAGR4Y,EAAQJ,GACb,GAAInc,KAAK8J,QAAQ8Q,aACf,OAAO,EAGT,IAAMgjB,EAAU9iB,GAAIrF,MAAM8G,GACpBshB,EAAa79B,KAAKw9B,QAAQx8B,KAAK,2BAIrC,GAFAhB,KAAK8J,QAAQ2B,OAAO,sBAAuB8Q,EAAQJ,GAE/CyhB,EAAS,CACX,IAAM7H,EAAS51B,IAAEoc,GACX7J,EAAWqjB,EAAOrjB,WAClB2G,EAAM,CACVpT,KAAMyM,EAASzM,KAAOogB,SAAS0P,EAAO9P,IAAI,cAAe,IACzD9Z,IAAKuG,EAASvG,IAAMka,SAAS0P,EAAO9P,IAAI,aAAc,KAIlD2R,EAAY,CAChBkG,EAAG/H,EAAOjC,YAAW,GACrBkJ,EAAGjH,EAAOzc,aAAY,IAGxBukB,EAAW5X,IAAI,CACb0P,QAAS,QACT1vB,KAAMoT,EAAIpT,KACVkG,IAAKkN,EAAIlN,IACT9B,MAAOutB,EAAUkG,EACjB57B,OAAQ01B,EAAUoF,IACjBx8B,KAAK,SAAUu1B,GAElB,IAAMgI,EAAe,IAAIC,MACzBD,EAAa5I,IAAMY,EAAOn1B,KAAK,OAE/B,IAAMq9B,EAAarG,EAAUkG,EAAI,IAAMlG,EAAUoF,EAAI,KAAOh9B,KAAK2B,KAAKa,MAAMoB,SAAW,KAAOm6B,EAAa1zB,MAAQ,IAAM0zB,EAAa77B,OAAS,IAC/I27B,EAAW78B,KAAK,gCAAgCuX,KAAK0lB,GACrDj+B,KAAK8J,QAAQ2B,OAAO,oBAAqB8Q,QAEzCvc,KAAKua,OAGP,OAAOqjB,I,6BASP59B,KAAK8J,QAAQ2B,OAAO,sBACpBzL,KAAKw9B,QAAQ39B,WAAW0a,Y,yMCxI5B,IACM2jB,GAAc,iFAECC,G,WACnB,WAAYr0B,GAAS,Y,4FAAA,SACnB9J,KAAK8J,QAAUA,EACf9J,KAAKF,QAAUgK,EAAQhK,QACvBE,KAAKwZ,OAAS,CACZ,mBAAoB,SAAC8jB,EAAI7a,GAClBA,EAAE6Q,sBACL,EAAK8K,YAAY3b,IAGrB,qBAAsB,SAAC6a,EAAI7a,GACzB,EAAK4b,cAAc5b,K,4DAMvBziB,KAAKs+B,cAAgB,O,gCAIrBt+B,KAAKs+B,cAAgB,O,gCAIrB,GAAKt+B,KAAKs+B,cAAV,CAIA,IAAMC,EAAUv+B,KAAKs+B,cAAcnc,WAC7BtJ,EAAQ0lB,EAAQ1lB,MAAMqlB,IAE5B,GAAIrlB,IAAUA,EAAM,IAAMA,EAAM,IAAK,CACnC,IAAM7U,EAAO6U,EAAM,GAAK0lB,EApCR,UAoCkCA,EAC5CC,EAAUx+B,KAAKF,QAAQ2+B,0BAC3BF,EAAQhqB,QAAQ,wDAAyD,IAAI5H,MAAM,KAAK,GACtF4xB,EACE7uB,EAAOvP,IAAE,SAASE,KAAKm+B,GAAS59B,KAAK,OAAQoD,GAAM,GACrDhE,KAAK8J,QAAQhK,QAAQ4+B,iBACvBv+B,IAAEuP,GAAM9O,KAAK,SAAU,UAGzBZ,KAAKs+B,cAAcpc,WAAWxS,GAC9B1P,KAAKs+B,cAAgB,KACrBt+B,KAAK8J,QAAQ2B,OAAO,oB,oCAIVgX,GACZ,GAAIjd,EAAMwI,SAAS,CAAC9O,GAAI2b,KAAKuJ,MAAOllB,GAAI2b,KAAKwJ,OAAQ5B,EAAEwB,SAAU,CAC/D,IAAM0a,EAAY3+B,KAAK8J,QAAQ2B,OAAO,sBAAsBmzB,eAC5D5+B,KAAKs+B,cAAgBK,K,kCAIblc,GACNjd,EAAMwI,SAAS,CAAC9O,GAAI2b,KAAKuJ,MAAOllB,GAAI2b,KAAKwJ,OAAQ5B,EAAEwB,UACrDjkB,KAAKuU,e,6MC3DUsqB,G,WACnB,WAAY/0B,GAAS,Y,4FAAA,SACnB9J,KAAK+Z,MAAQjQ,EAAQmQ,WAAW4E,KAChC7e,KAAKwZ,OAAS,CACZ,oBAAqB,WACnB,EAAKO,MAAMzF,IAAIxK,EAAQ2B,OAAO,W,kEAMlC,OAAOqP,GAAI1G,WAAWpU,KAAK+Z,MAAM,S,6MCZhB+kB,G,WACnB,WAAYh1B,GAAS,Y,4FAAA,SACnB9J,KAAK8J,QAAUA,EACf9J,KAAKF,QAAUgK,EAAQhK,QAAQyU,SAAW,GAE1CvU,KAAKyZ,KAAO,CAACva,GAAI2b,KAAKuJ,MAAOllB,GAAI2b,KAAKwJ,MAAOnlB,GAAI2b,KAAKkkB,OAAQ7/B,GAAI2b,KAAKmkB,MAAO9/B,GAAI2b,KAAKokB,UAAW//B,GAAI2b,KAAKqkB,OAC3Gl/B,KAAKm/B,oBAAsB,KAE3Bn/B,KAAKwZ,OAAS,CACZ,mBAAoB,SAAC8jB,EAAI7a,GAClBA,EAAE6Q,sBACL,EAAK8K,YAAY3b,IAGrB,qBAAsB,SAAC6a,EAAI7a,GACzB,EAAK4b,cAAc5b,K,kEAMvB,QAASziB,KAAKF,QAAQ+Y,Q,mCAItB7Y,KAAKo/B,SAAW,O,gCAIhBp/B,KAAKo/B,SAAW,O,gCAIhB,GAAKp/B,KAAKo/B,SAAV,CAIA,IAAMn0B,EAAOjL,KACPu+B,EAAUv+B,KAAKo/B,SAASjd,WAC9BniB,KAAKF,QAAQ+Y,MAAM0lB,GAAS,SAAS1lB,GACnC,GAAIA,EAAO,CACT,IAAInJ,EAAO,GAUX,GARqB,iBAAVmJ,EACTnJ,EAAOoL,GAAIxC,WAAWO,GACbA,aAAiBwmB,OAC1B3vB,EAAOmJ,EAAM,GACJA,aAAiBymB,OAC1B5vB,EAAOmJ,IAGJnJ,EAAM,OACXzE,EAAKm0B,SAASld,WAAWxS,GACzBzE,EAAKm0B,SAAW,KAChBn0B,EAAKnB,QAAQ2B,OAAO,uB,oCAKZgX,GAGZ,GAAIziB,KAAKm/B,qBAAuB35B,EAAMwI,SAAShO,KAAKyZ,KAAMzZ,KAAKm/B,qBAC7Dn/B,KAAKm/B,oBAAsB1c,EAAEwB,YAD/B,CAKA,GAAIze,EAAMwI,SAAShO,KAAKyZ,KAAMgJ,EAAEwB,SAAU,CACxC,IAAM0a,EAAY3+B,KAAK8J,QAAQ2B,OAAO,sBAAsBmzB,eAC5D5+B,KAAKo/B,SAAWT,EAElB3+B,KAAKm/B,oBAAsB1c,EAAEwB,W,kCAGnBxB,GACNjd,EAAMwI,SAAShO,KAAKyZ,KAAMgJ,EAAEwB,UAC9BjkB,KAAKuU,e,6MC/EUgrB,G,WACnB,WAAYz1B,GAAS,Y,4FAAA,SACnB9J,KAAK8J,QAAUA,EAEf9J,KAAKo9B,aAAetzB,EAAQmQ,WAAWojB,YACvCr9B,KAAKF,QAAUgK,EAAQhK,SAEiB,IAApCE,KAAKF,QAAQ0/B,qBAEfx/B,KAAKF,QAAQqZ,YAAcnZ,KAAK8J,QAAQiQ,MAAMnZ,KAAK,gBAAkBZ,KAAKF,QAAQqZ,aAGpFnZ,KAAKwZ,OAAS,CACZ,oCAAqC,WACnC,EAAK+jB,UAEP,8BAA+B,WAC7B,EAAKA,W,kEAMT,QAASv9B,KAAKF,QAAQqZ,c,mCAGX,WACXnZ,KAAKoZ,aAAejZ,IAAE,kCACtBH,KAAKoZ,aAAatY,GAAG,SAAS,WAC5B,EAAKgJ,QAAQ2B,OAAO,YACnBpL,KAAKL,KAAKF,QAAQqZ,aAAa4f,UAAU/4B,KAAKo9B,cAEjDp9B,KAAKu9B,W,gCAILv9B,KAAKoZ,aAAazV,W,+BAIlB,IAAM87B,GAAUz/B,KAAK8J,QAAQ2B,OAAO,yBAA2BzL,KAAK8J,QAAQ2B,OAAO,kBACnFzL,KAAKoZ,aAAasmB,OAAOD,Q,6MCrCRE,G,WACnB,WAAY71B,I,4FAAS,SACnB9J,KAAKka,GAAK/Z,IAAEuB,WAAWwY,GACvBla,KAAK8J,QAAUA,EACf9J,KAAK08B,SAAW5yB,EAAQmQ,WAAW0iB,QACnC38B,KAAKF,QAAUgK,EAAQhK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ4e,SACzB1e,KAAK4/B,eAAiB3yB,EAAKV,aACzBvM,KAAKF,QAAQm0B,OAAOljB,EAAI9H,MAAQ,MAAQ,O,iEAI1B42B,GAChB,IAAI94B,EAAW/G,KAAK4/B,eAAeC,GACnC,OAAK7/B,KAAKF,QAAQkH,WAAcD,GAI5BgK,EAAI9H,QACNlC,EAAWA,EAASwN,QAAQ,MAAO,KAAKA,QAAQ,QAAS,MAQpD,MALPxN,EAAWA,EAASwN,QAAQ,YAAa,MACtCA,QAAQ,QAAS,KACjBA,QAAQ,cAAe,KACvBA,QAAQ,eAAgB,MAEF,KAZhB,K,6BAeJnW,GAKL,OAJK4B,KAAKF,QAAQ8e,SAAWxgB,EAAEwgB,gBACtBxgB,EAAEwgB,QAEXxgB,EAAE+Z,UAAYnY,KAAKF,QAAQqY,UACpBnY,KAAKka,GAAG4lB,OAAO1hC,K,mCAItB4B,KAAK+/B,oBACL//B,KAAKggC,yBACLhgC,KAAKigC,wBACLjgC,KAAKkgC,yBACLlgC,KAAKmgC,iBAAmB,K,uCAIjBngC,KAAKmgC,mB,sCAGEjiC,GAKd,OAJKG,OAAOkB,UAAUC,eAAe1B,KAAKkC,KAAKmgC,iBAAkBjiC,KAC/D8B,KAAKmgC,iBAAiBjiC,GAAQ6S,EAAInH,gBAAgB1L,IAChDsH,EAAMwI,SAAShO,KAAKF,QAAQsgC,qBAAsBliC,IAE/C8B,KAAKmgC,iBAAiBjiC,K,0CAGXA,GAElB,MAAiB,MADjBA,EAAOA,EAAKiK,gBACWnI,KAAK4J,gBAAgB1L,KAAoD,IAA3C6S,EAAIhJ,oBAAoBoB,QAAQjL,K,mCAG1EoC,EAAWse,EAAS8T,EAAWD,GAAW,WACrD,OAAOzyB,KAAKka,GAAGmmB,YAAY,CACzB//B,UAAW,cAAgBA,EAC3BT,SAAU,CACRG,KAAK8/B,OAAO,CACVx/B,UAAW,4BACXF,SAAUJ,KAAKka,GAAGomB,KAAKtgC,KAAKF,QAAQ6e,MAAM9c,KAAO,sBACjD+c,QAASA,EACT/d,MAAO,SAAC4hB,GACN,IAAM8d,EAAUpgC,IAAEsiB,EAAE+d,eAChB9N,GAAaD,EACf,EAAK3oB,QAAQ2B,OAAO,eAAgB,CAClCinB,UAAW6N,EAAQ3/B,KAAK,kBACxB6xB,UAAW8N,EAAQ3/B,KAAK,oBAEjB8xB,EACT,EAAK5oB,QAAQ2B,OAAO,eAAgB,CAClCinB,UAAW6N,EAAQ3/B,KAAK,oBAEjB6xB,GACT,EAAK3oB,QAAQ2B,OAAO,eAAgB,CAClCgnB,UAAW8N,EAAQ3/B,KAAK,qBAI9Bb,SAAU,SAACwgC,GACT,IAAME,EAAeF,EAAQv/B,KAAK,sBAC9B0xB,IACF+N,EAAaxa,IAAI,mBAAoB,EAAKnmB,QAAQ4gC,YAAYhO,WAC9D6N,EAAQ3/B,KAAK,iBAAkB,EAAKd,QAAQ4gC,YAAYhO,YAEtDD,GACFgO,EAAaxa,IAAI,QAAS,EAAKnmB,QAAQ4gC,YAAYjO,WACnD8N,EAAQ3/B,KAAK,iBAAkB,EAAKd,QAAQ4gC,YAAYjO,YAExDgO,EAAaxa,IAAI,QAAS,kBAIhCjmB,KAAK8/B,OAAO,CACVx/B,UAAW,kBACXF,SAAUJ,KAAKka,GAAGymB,uBAAuB,GAAI3gC,KAAKF,SAClD8e,QAAS5e,KAAK2B,KAAK0E,MAAME,KACzB/F,KAAM,CACJk/B,OAAQ,cAGZ1/B,KAAKka,GAAG0mB,SAAS,CACfrI,OAAQ7F,EAAY,CAClB,6BACE,mCAAqC1yB,KAAK2B,KAAK0E,MAAMG,WAAa,SAClE,QACE,4HACExG,KAAK2B,KAAK0E,MAAMK,YAClB,YACF,SACA,mFACA,QACE,mIACE1G,KAAK2B,KAAK0E,MAAMS,SAClB,YACA,0FAA4F9G,KAAKF,QAAQ4gC,YAAYhO,UAAY,mCACnI,SACA,sFACF,UACA3lB,KAAK,IAAM,KACZ0lB,EAAY,CACX,6BACE,mCAAqCzyB,KAAK2B,KAAK0E,MAAMI,WAAa,SAClE,QACE,6HACEzG,KAAK2B,KAAK0E,MAAMQ,eAClB,YACF,SACA,mFACA,QACE,mIACE7G,KAAK2B,KAAK0E,MAAMS,SAClB,YACA,0FAA4F9G,KAAKF,QAAQ4gC,YAAYjO,UAAY,mCACnI,SACA,sFACF,UACA1lB,KAAK,IAAM,IACbhN,SAAU,SAAC8gC,GACTA,EAAU7/B,KAAK,gBAAgBP,MAAK,SAAC0N,EAAK3C,GACxC,IAAMs1B,EAAU3gC,IAAEqL,GAClBs1B,EAAQz/B,OAAO,EAAK6Y,GAAG6mB,QAAQ,CAC7BC,OAAQ,EAAKlhC,QAAQkhC,OACrBC,WAAY,EAAKnhC,QAAQmhC,WACzB1M,UAAWuM,EAAQtgC,KAAK,SACxB2X,UAAW,EAAKrY,QAAQqY,UACxByG,QAAS,EAAK9e,QAAQ8e,UACrBzd,aAGL,IAAI+/B,EAAe,CACjB,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,YAEhFL,EAAU7/B,KAAK,uBAAuBP,MAAK,SAAC0N,EAAK3C,GAC/C,IAAMs1B,EAAU3gC,IAAEqL,GAClBs1B,EAAQz/B,OAAO,EAAK6Y,GAAG6mB,QAAQ,CAC7BC,OAAQE,EACRD,WAAYC,EACZ3M,UAAWuM,EAAQtgC,KAAK,SACxB2X,UAAW,EAAKrY,QAAQqY,UACxByG,QAAS,EAAK9e,QAAQ8e,UACrBzd,aAEL0/B,EAAU7/B,KAAK,qBAAqBP,MAAK,SAAC0N,EAAK3C,GAC7CrL,IAAEqL,GAAM21B,QAAO,WACb,IAAMC,EAAQP,EAAU7/B,KAAK,IAAMb,IAAEH,MAAMQ,KAAK,UAAUQ,KAAK,mBAAmB8d,QAC5EzY,EAAQrG,KAAKpB,MAAMkO,cACzBs0B,EAAMnb,IAAI,mBAAoB5f,GAC3BzF,KAAK,aAAcyF,GACnBzF,KAAK,aAAcyF,GACnBzF,KAAK,sBAAuByF,GAC/B+6B,EAAMvgC,eAIZA,MAAO,SAACsb,GACNA,EAAMggB,kBAEN,IAAMl8B,EAAUE,IAAE,IAAMG,GAAWU,KAAK,uBAClCu/B,EAAUpgC,IAAEgc,EAAMI,QAClBgY,EAAYgM,EAAQ//B,KAAK,SACzB5B,EAAQ2hC,EAAQ3/B,KAAK,cAE3B,GAAkB,gBAAd2zB,EAA6B,CAC/B,IAAM8M,EAAUphC,EAAQe,KAAK,IAAMpC,GAC7B0iC,EAAWnhC,IAAEF,EAAQe,KAAK,IAAMqgC,EAAQ7gC,KAAK,UAAUQ,KAAK,mBAAmB,IAG/EogC,EAAQE,EAAStgC,KAAK,mBAAmB6M,OAAOolB,SAGhD5sB,EAAQg7B,EAAQ/sB,MACtB8sB,EAAMnb,IAAI,mBAAoB5f,GAC3BzF,KAAK,aAAcyF,GACnBzF,KAAK,aAAcyF,GACnBzF,KAAK,sBAAuByF,GAC/Bi7B,EAASC,QAAQH,GACjBC,EAAQxgC,YACH,CACL,GAAI2E,EAAMwI,SAAS,CAAC,YAAa,aAAcumB,GAAY,CACzD,IAAMr1B,EAAoB,cAAdq1B,EAA4B,mBAAqB,QACvDiN,EAASjB,EAAQ/jB,QAAQ,eAAexb,KAAK,sBAC7CygC,EAAiBlB,EAAQ/jB,QAAQ,eAAexb,KAAK,8BAE3DwgC,EAAOvb,IAAI/mB,EAAKN,GAChB6iC,EAAe7gC,KAAK,QAAU2zB,EAAW31B,GAE3C,EAAKkL,QAAQ2B,OAAO,UAAY8oB,EAAW31B,UAKlDuC,W,0CAGe,WAClBnB,KAAK8J,QAAQ4E,KAAK,gBAAgB,WAChC,OAAO,EAAKwL,GAAGmmB,YAAY,CACzB,EAAKP,OAAO,CACVx/B,UAAW,kBACXF,SAAU,EAAK8Z,GAAGymB,uBAChB,EAAKzmB,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM+iB,OAAQ,EAAK5hC,SAE/C8e,QAAS,EAAKjd,KAAKoD,MAAMA,MACzBvE,KAAM,CACJk/B,OAAQ,cAGZ,EAAKxlB,GAAG0mB,SAAS,CACftgC,UAAW,iBACXi4B,MAAO,EAAKz4B,QAAQ6hC,UACpBC,MAAO,EAAKjgC,KAAKoD,MAAMA,MACvB88B,SAAU,SAACr2B,GAEW,iBAATA,IACTA,EAAO,CACLuvB,IAAKvvB,EACLo2B,MAAQvjC,OAAOkB,UAAUC,eAAe1B,KAAK,EAAK6D,KAAKoD,MAAOyG,GAAQ,EAAK7J,KAAKoD,MAAMyG,GAAQA,IAIlG,IAAMuvB,EAAMvvB,EAAKuvB,IACX6G,EAAQp2B,EAAKo2B,MAInB,MAAO,IAAM7G,GAHCvvB,EAAKzG,MAAQ,WAAayG,EAAKzG,MAAQ,KAAO,KAC1CyG,EAAKlL,UAAY,WAAakL,EAAKlL,UAAY,IAAM,IAEhC,IAAMshC,EAAQ,KAAO7G,EAAM,KAEpEl6B,MAAO,EAAKiJ,QAAQsS,oBAAoB,0BAEzCjb,YAGL,IAtCkB,eAsCT2gC,EAAcC,GACrB,IAAMv2B,EAAO,EAAK1L,QAAQ6hC,UAAUG,GAEpC,EAAKh4B,QAAQ4E,KAAK,gBAAkBlD,GAAM,WACxC,OAAO,EAAKs0B,OAAO,CACjBx/B,UAAW,kBAAoBkL,EAC/BpL,SAAU,oBAAsBoL,EAAO,KAAOA,EAAKsB,cAAgB,SACnE8R,QAAS,EAAKjd,KAAKoD,MAAMyG,GACzB3K,MAAO,EAAKiJ,QAAQsS,oBAAoB,wBACvCjb,aATE2gC,EAAW,EAAGC,EAAW/hC,KAAKF,QAAQ6hC,UAAUvgC,OAAQ0gC,EAAWC,EAAUD,IAAY,EAAzFA,GAaT9hC,KAAK8J,QAAQ4E,KAAK,eAAe,WAC/B,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,gBACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM7c,MAC1C8c,QAAS,EAAKjd,KAAKE,KAAKC,KAAO,EAAKkgC,kBAAkB,QACtDnhC,MAAO,EAAKiJ,QAAQm4B,kCAAkC,iBACrD9gC,YAGLnB,KAAK8J,QAAQ4E,KAAK,iBAAiB,WACjC,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,kBACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM5c,QAC1C6c,QAAS,EAAKjd,KAAKE,KAAKE,OAAS,EAAKigC,kBAAkB,UACxDnhC,MAAO,EAAKiJ,QAAQm4B,kCAAkC,mBACrD9gC,YAGLnB,KAAK8J,QAAQ4E,KAAK,oBAAoB,WACpC,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,qBACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM3c,WAC1C4c,QAAS,EAAKjd,KAAKE,KAAKG,UAAY,EAAKggC,kBAAkB,aAC3DnhC,MAAO,EAAKiJ,QAAQm4B,kCAAkC,sBACrD9gC,YAGLnB,KAAK8J,QAAQ4E,KAAK,gBAAgB,WAChC,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMujB,QAC1CtjB,QAAS,EAAKjd,KAAKE,KAAKI,MAAQ,EAAK+/B,kBAAkB,gBACvDnhC,MAAO,EAAKiJ,QAAQsS,oBAAoB,yBACvCjb,YAGLnB,KAAK8J,QAAQ4E,KAAK,wBAAwB,WACxC,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,yBACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMxc,eAC1Cyc,QAAS,EAAKjd,KAAKE,KAAKM,cAAgB,EAAK6/B,kBAAkB,iBAC/DnhC,MAAO,EAAKiJ,QAAQm4B,kCAAkC,0BACrD9gC,YAGLnB,KAAK8J,QAAQ4E,KAAK,sBAAsB,WACtC,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,uBACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMtc,aAC1Cuc,QAAS,EAAKjd,KAAKE,KAAKQ,YACxBxB,MAAO,EAAKiJ,QAAQm4B,kCAAkC,wBACrD9gC,YAGLnB,KAAK8J,QAAQ4E,KAAK,oBAAoB,WACpC,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,qBACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMvc,WAC1Cwc,QAAS,EAAKjd,KAAKE,KAAKO,UACxBvB,MAAO,EAAKiJ,QAAQm4B,kCAAkC,sBACrD9gC,YAGLnB,KAAK8J,QAAQ4E,KAAK,mBAAmB,WACnC,IAAMwX,EAAY,EAAKpc,QAAQ2B,OAAO,uBActC,OAZI,EAAK3L,QAAQqiC,iBAEfhiC,IAAEM,KAAKylB,EAAU,eAAevZ,MAAM,MAAM,SAACwB,EAAKi0B,GAChDA,EAAWA,EAASnpB,OAAO1E,QAAQ,SAAU,IACzC,EAAK8tB,oBAAoBD,KACuB,IAA9C,EAAKtiC,QAAQwiC,UAAUn5B,QAAQi5B,IACjC,EAAKtiC,QAAQwiC,UAAUnzB,KAAKizB,MAM7B,EAAKloB,GAAGmmB,YAAY,CACzB,EAAKP,OAAO,CACVx/B,UAAW,kBACXF,SAAU,EAAK8Z,GAAGymB,uBAChB,8CAA+C,EAAK7gC,SAEtD8e,QAAS,EAAKjd,KAAKE,KAAK3D,KACxBsC,KAAM,CACJk/B,OAAQ,cAGZ,EAAKxlB,GAAGqoB,cAAc,CACpBjiC,UAAW,oBACXkiC,eAAgB,EAAK1iC,QAAQ6e,MAAM8jB,UACnClK,MAAO,EAAKz4B,QAAQwiC,UAAUnrB,OAAO,EAAKvN,gBAAgBzK,KAAK,IAC/DyiC,MAAO,EAAKjgC,KAAKE,KAAK3D,KACtB2jC,SAAU,SAACr2B,GACT,MAAO,6BAA+BuF,EAAI/I,cAAcwD,GAAQ,KAAOA,EAAO,WAEhF3K,MAAO,EAAKiJ,QAAQm4B,kCAAkC,uBAEvD9gC,YAGLnB,KAAK8J,QAAQ4E,KAAK,mBAAmB,WACnC,OAAO,EAAKwL,GAAGmmB,YAAY,CACzB,EAAKP,OAAO,CACVx/B,UAAW,kBACXF,SAAU,EAAK8Z,GAAGymB,uBAAuB,8CAA+C,EAAK7gC,SAC7F8e,QAAS,EAAKjd,KAAKE,KAAKS,KACxB9B,KAAM,CACJk/B,OAAQ,cAGZ,EAAKxlB,GAAGqoB,cAAc,CACpBjiC,UAAW,oBACXkiC,eAAgB,EAAK1iC,QAAQ6e,MAAM8jB,UACnClK,MAAO,EAAKz4B,QAAQ4iC,UACpBd,MAAO,EAAKjgC,KAAKE,KAAKS,KACtBzB,MAAO,EAAKiJ,QAAQm4B,kCAAkC,uBAEvD9gC,YAGLnB,KAAK8J,QAAQ4E,KAAK,uBAAuB,WACvC,OAAO,EAAKwL,GAAGmmB,YAAY,CACzB,EAAKP,OAAO,CACVx/B,UAAW,kBACXF,SAAU,EAAK8Z,GAAGymB,uBAAuB,kDAAmD,EAAK7gC,SACjG8e,QAAS,EAAKjd,KAAKE,KAAKU,SACxB/B,KAAM,CACJk/B,OAAQ,cAGZ,EAAKxlB,GAAGqoB,cAAc,CACpBjiC,UAAW,wBACXkiC,eAAgB,EAAK1iC,QAAQ6e,MAAM8jB,UACnClK,MAAO,EAAKz4B,QAAQ6iC,cACpBf,MAAO,EAAKjgC,KAAKE,KAAKU,SACtB1B,MAAO,EAAKiJ,QAAQm4B,kCAAkC,2BAEvD9gC,YAGLnB,KAAK8J,QAAQ4E,KAAK,gBAAgB,WAChC,OAAO,EAAKk0B,aAAa,iBAAkB,EAAKjhC,KAAK0E,MAAMC,QAAQ,GAAM,MAG3EtG,KAAK8J,QAAQ4E,KAAK,oBAAoB,WACpC,OAAO,EAAKk0B,aAAa,kBAAmB,EAAKjhC,KAAK0E,MAAMI,YAAY,GAAO,MAGjFzG,KAAK8J,QAAQ4E,KAAK,oBAAoB,WACpC,OAAO,EAAKk0B,aAAa,kBAAmB,EAAKjhC,KAAK0E,MAAMG,YAAY,GAAM,MAGhFxG,KAAK8J,QAAQ4E,KAAK,aAAa,WAC7B,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMkkB,eAC1CjkB,QAAS,EAAKjd,KAAK6D,MAAMC,UAAY,EAAKu8B,kBAAkB,uBAC5DnhC,MAAO,EAAKiJ,QAAQsS,oBAAoB,gCACvCjb,YAGLnB,KAAK8J,QAAQ4E,KAAK,aAAa,WAC7B,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMmkB,aAC1ClkB,QAAS,EAAKjd,KAAK6D,MAAME,QAAU,EAAKs8B,kBAAkB,qBAC1DnhC,MAAO,EAAKiJ,QAAQsS,oBAAoB,8BACvCjb,YAGL,IAAM4hC,EAAc/iC,KAAK8/B,OAAO,CAC9B1/B,SAAUJ,KAAKka,GAAGomB,KAAKtgC,KAAKF,QAAQ6e,MAAMqkB,WAC1CpkB,QAAS5e,KAAK2B,KAAKmE,UAAUG,KAAOjG,KAAKgiC,kBAAkB,eAC3DnhC,MAAOb,KAAK8J,QAAQsS,oBAAoB,wBAGpC6mB,EAAgBjjC,KAAK8/B,OAAO,CAChC1/B,SAAUJ,KAAKka,GAAGomB,KAAKtgC,KAAKF,QAAQ6e,MAAMukB,aAC1CtkB,QAAS5e,KAAK2B,KAAKmE,UAAUI,OAASlG,KAAKgiC,kBAAkB,iBAC7DnhC,MAAOb,KAAK8J,QAAQsS,oBAAoB,0BAGpC+mB,EAAenjC,KAAK8/B,OAAO,CAC/B1/B,SAAUJ,KAAKka,GAAGomB,KAAKtgC,KAAKF,QAAQ6e,MAAMykB,YAC1CxkB,QAAS5e,KAAK2B,KAAKmE,UAAUK,MAAQnG,KAAKgiC,kBAAkB,gBAC5DnhC,MAAOb,KAAK8J,QAAQsS,oBAAoB,yBAGpCinB,EAAcrjC,KAAK8/B,OAAO,CAC9B1/B,SAAUJ,KAAKka,GAAGomB,KAAKtgC,KAAKF,QAAQ6e,MAAM2kB,cAC1C1kB,QAAS5e,KAAK2B,KAAKmE,UAAUM,QAAUpG,KAAKgiC,kBAAkB,eAC9DnhC,MAAOb,KAAK8J,QAAQsS,oBAAoB,wBAGpCrW,EAAU/F,KAAK8/B,OAAO,CAC1B1/B,SAAUJ,KAAKka,GAAGomB,KAAKtgC,KAAKF,QAAQ6e,MAAM5Y,SAC1C6Y,QAAS5e,KAAK2B,KAAKmE,UAAUC,QAAU/F,KAAKgiC,kBAAkB,WAC9DnhC,MAAOb,KAAK8J,QAAQsS,oBAAoB,oBAGpCpW,EAAShG,KAAK8/B,OAAO,CACzB1/B,SAAUJ,KAAKka,GAAGomB,KAAKtgC,KAAKF,QAAQ6e,MAAM3Y,QAC1C4Y,QAAS5e,KAAK2B,KAAKmE,UAAUE,OAAShG,KAAKgiC,kBAAkB,UAC7DnhC,MAAOb,KAAK8J,QAAQsS,oBAAoB,mBAG1Cpc,KAAK8J,QAAQ4E,KAAK,qBAAsBzB,EAAKxB,OAAOs3B,EAAa,WACjE/iC,KAAK8J,QAAQ4E,KAAK,uBAAwBzB,EAAKxB,OAAOw3B,EAAe,WACrEjjC,KAAK8J,QAAQ4E,KAAK,sBAAuBzB,EAAKxB,OAAO03B,EAAc,WACnEnjC,KAAK8J,QAAQ4E,KAAK,qBAAsBzB,EAAKxB,OAAO43B,EAAa,WACjErjC,KAAK8J,QAAQ4E,KAAK,iBAAkBzB,EAAKxB,OAAO1F,EAAS,WACzD/F,KAAK8J,QAAQ4E,KAAK,gBAAiBzB,EAAKxB,OAAOzF,EAAQ,WAEvDhG,KAAK8J,QAAQ4E,KAAK,oBAAoB,WACpC,OAAO,EAAKwL,GAAGmmB,YAAY,CACzB,EAAKP,OAAO,CACVx/B,UAAW,kBACXF,SAAU,EAAK8Z,GAAGymB,uBAAuB,EAAKzmB,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMqkB,WAAY,EAAKljC,SAC1F8e,QAAS,EAAKjd,KAAKmE,UAAUA,UAC7BtF,KAAM,CACJk/B,OAAQ,cAGZ,EAAKxlB,GAAG0mB,SAAS,CACf,EAAK1mB,GAAGmmB,YAAY,CAClB//B,UAAW,aACXT,SAAU,CAACkjC,EAAaE,EAAeE,EAAcE,KAEvD,EAAKnpB,GAAGmmB,YAAY,CAClB//B,UAAW,YACXT,SAAU,CAACkG,EAASC,SAGvB7E,YAGLnB,KAAK8J,QAAQ4E,KAAK,iBAAiB,WACjC,OAAO,EAAKwL,GAAGmmB,YAAY,CACzB,EAAKP,OAAO,CACVx/B,UAAW,kBACXF,SAAU,EAAK8Z,GAAGymB,uBAAuB,EAAKzmB,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM4kB,YAAa,EAAKzjC,SAC3F8e,QAAS,EAAKjd,KAAKE,KAAKK,OACxB1B,KAAM,CACJk/B,OAAQ,cAGZ,EAAKxlB,GAAGqoB,cAAc,CACpBhK,MAAO,EAAKz4B,QAAQ0jC,YACpBhB,eAAgB,EAAK1iC,QAAQ6e,MAAM8jB,UACnCniC,UAAW,uBACXshC,MAAO,EAAKjgC,KAAKE,KAAKK,OACtBrB,MAAO,EAAKiJ,QAAQsS,oBAAoB,yBAEzCjb,YAGLnB,KAAK8J,QAAQ4E,KAAK,gBAAgB,WAChC,OAAO,EAAKwL,GAAGmmB,YAAY,CACzB,EAAKP,OAAO,CACVx/B,UAAW,kBACXF,SAAU,EAAK8Z,GAAGymB,uBAAuB,EAAKzmB,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMra,OAAQ,EAAKxE,SACtF8e,QAAS,EAAKjd,KAAK2C,MAAMA,MACzB9D,KAAM,CACJk/B,OAAQ,cAGZ,EAAKxlB,GAAG0mB,SAAS,CACfgB,MAAO,EAAKjgC,KAAK2C,MAAMA,MACvBhE,UAAW,aACXi4B,MAAO,CACL,sCACE,mGACA,wDACA,0DACF,SACA,mDACAxrB,KAAK,OAER,CACDhN,SAAU,SAACG,GACQA,EAAMc,KAAK,uCACnBilB,IAAI,CACX5b,MAAO,EAAKvK,QAAQ2jC,mBAAmBC,IAAM,KAC7CxhC,OAAQ,EAAKpC,QAAQ2jC,mBAAmB7Y,IAAM,OAC7C+Y,UAAU,EAAK75B,QAAQsS,oBAAoB,uBAC3Ctb,GAAG,YAAa,EAAK8iC,iBAAiBzkC,KAAK,OAE/CgC,YAGLnB,KAAK8J,QAAQ4E,KAAK,eAAe,WAC/B,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM3a,MAC1C4a,QAAS,EAAKjd,KAAKqC,KAAKA,KAAO,EAAKg+B,kBAAkB,mBACtDnhC,MAAO,EAAKiJ,QAAQsS,oBAAoB,qBACvCjb,YAGLnB,KAAK8J,QAAQ4E,KAAK,kBAAkB,WAClC,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMklB,SAC1CjlB,QAAS,EAAKjd,KAAKa,MAAMA,MACzB3B,MAAO,EAAKiJ,QAAQsS,oBAAoB,sBACvCjb,YAGLnB,KAAK8J,QAAQ4E,KAAK,gBAAgB,WAChC,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM9a,OAC1C+a,QAAS,EAAKjd,KAAKkC,MAAMA,MACzBhD,MAAO,EAAKiJ,QAAQsS,oBAAoB,sBACvCjb,YAGLnB,KAAK8J,QAAQ4E,KAAK,aAAa,WAC7B,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMmlB,OAC1CllB,QAAS,EAAKjd,KAAKmD,GAAGrC,OAAS,EAAKu/B,kBAAkB,wBACtDnhC,MAAO,EAAKiJ,QAAQsS,oBAAoB,iCACvCjb,YAGLnB,KAAK8J,QAAQ4E,KAAK,qBAAqB,WACrC,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,oCACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMolB,WAC1CnlB,QAAS,EAAKjd,KAAK7B,QAAQ8F,WAC3B/E,MAAO,EAAKiJ,QAAQsS,oBAAoB,uBACvCjb,YAGLnB,KAAK8J,QAAQ4E,KAAK,mBAAmB,WACnC,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,kCACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM9D,MAC1C+D,QAAS,EAAKjd,KAAK7B,QAAQ+F,SAC3BhF,MAAO,EAAKiJ,QAAQsS,oBAAoB,qBACvCjb,YAGLnB,KAAK8J,QAAQ4E,KAAK,eAAe,WAC/B,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMlX,MAC1CmX,QAAS,EAAKjd,KAAK4F,QAAQE,KAAO,EAAKu6B,kBAAkB,QACzDnhC,MAAO,EAAKiJ,QAAQsS,oBAAoB,iBACvCjb,YAGLnB,KAAK8J,QAAQ4E,KAAK,eAAe,WAC/B,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMnX,MAC1CoX,QAAS,EAAKjd,KAAK4F,QAAQC,KAAO,EAAKw6B,kBAAkB,QACzDnhC,MAAO,EAAKiJ,QAAQsS,oBAAoB,iBACvCjb,YAGLnB,KAAK8J,QAAQ4E,KAAK,eAAe,WAC/B,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMqlB,UAC1CplB,QAAS,EAAKjd,KAAK7B,QAAQ6F,KAC3B9E,MAAO,EAAKiJ,QAAQsS,oBAAoB,qBACvCjb,c,+CAWkB,WAEvBnB,KAAK8J,QAAQ4E,KAAK,qBAAqB,WACrC,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,6CACVwe,QAAS,EAAKjd,KAAKa,MAAME,WACzB7B,MAAO,EAAKiJ,QAAQsS,oBAAoB,gBAAiB,OACxDjb,YAELnB,KAAK8J,QAAQ4E,KAAK,qBAAqB,WACrC,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,4CACVwe,QAAS,EAAKjd,KAAKa,MAAMG,WACzB9B,MAAO,EAAKiJ,QAAQsS,oBAAoB,gBAAiB,SACxDjb,YAELnB,KAAK8J,QAAQ4E,KAAK,wBAAwB,WACxC,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,4CACVwe,QAAS,EAAKjd,KAAKa,MAAMI,cACzB/B,MAAO,EAAKiJ,QAAQsS,oBAAoB,gBAAiB,UACxDjb,YAELnB,KAAK8J,QAAQ4E,KAAK,qBAAqB,WACrC,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMslB,UAC1CrlB,QAAS,EAAKjd,KAAKa,MAAMK,WACzBhC,MAAO,EAAKiJ,QAAQsS,oBAAoB,gBAAiB,OACxDjb,YAILnB,KAAK8J,QAAQ4E,KAAK,oBAAoB,WACpC,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM7b,WAC1C8b,QAAS,EAAKjd,KAAKa,MAAMM,UACzBjC,MAAO,EAAKiJ,QAAQsS,oBAAoB,iBAAkB,UACzDjb,YAGLnB,KAAK8J,QAAQ4E,KAAK,qBAAqB,WACrC,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM5b,YAC1C6b,QAAS,EAAKjd,KAAKa,MAAMO,WACzBlC,MAAO,EAAKiJ,QAAQsS,oBAAoB,iBAAkB,WACzDjb,YAGLnB,KAAK8J,QAAQ4E,KAAK,oBAAoB,WACpC,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMslB,UAC1CrlB,QAAS,EAAKjd,KAAKa,MAAMQ,UACzBnC,MAAO,EAAKiJ,QAAQsS,oBAAoB,iBAAkB,UACzDjb,YAILnB,KAAK8J,QAAQ4E,KAAK,sBAAsB,WACtC,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMulB,OAC1CtlB,QAAS,EAAKjd,KAAKa,MAAMmB,OACzB9C,MAAO,EAAKiJ,QAAQsS,oBAAoB,wBACvCjb,c,8CAIiB,WACtBnB,KAAK8J,QAAQ4E,KAAK,yBAAyB,WACzC,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM3a,MAC1C4a,QAAS,EAAKjd,KAAKqC,KAAKE,KACxBrD,MAAO,EAAKiJ,QAAQsS,oBAAoB,qBACvCjb,YAGLnB,KAAK8J,QAAQ4E,KAAK,iBAAiB,WACjC,OAAO,EAAKoxB,OAAO,CACjB1/B,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM1a,QAC1C2a,QAAS,EAAKjd,KAAKqC,KAAKC,OACxBpD,MAAO,EAAKiJ,QAAQsS,oBAAoB,mBACvCjb,c,+CAUkB,WACvBnB,KAAK8J,QAAQ4E,KAAK,mBAAmB,WACnC,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,SACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMwlB,UAC1CvlB,QAAS,EAAKjd,KAAK2C,MAAMC,YACzB1D,MAAO,EAAKiJ,QAAQsS,oBAAoB,gBAAiB,SACxDjb,YAELnB,KAAK8J,QAAQ4E,KAAK,qBAAqB,WACrC,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,SACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMylB,UAC1CxlB,QAAS,EAAKjd,KAAK2C,MAAME,YACzB3D,MAAO,EAAKiJ,QAAQsS,oBAAoB,gBAAiB,YACxDjb,YAELnB,KAAK8J,QAAQ4E,KAAK,qBAAqB,WACrC,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,SACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM0lB,WAC1CzlB,QAAS,EAAKjd,KAAK2C,MAAMG,WACzB5D,MAAO,EAAKiJ,QAAQsS,oBAAoB,gBAAiB,UACxDjb,YAELnB,KAAK8J,QAAQ4E,KAAK,sBAAsB,WACtC,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,SACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM2lB,UAC1C1lB,QAAS,EAAKjd,KAAK2C,MAAMI,YACzB7D,MAAO,EAAKiJ,QAAQsS,oBAAoB,gBAAiB,WACxDjb,YAELnB,KAAK8J,QAAQ4E,KAAK,oBAAoB,WACpC,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,SACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM4lB,WAC1C3lB,QAAS,EAAKjd,KAAK2C,MAAMK,OACzB9D,MAAO,EAAKiJ,QAAQsS,oBAAoB,sBACvCjb,YAELnB,KAAK8J,QAAQ4E,KAAK,oBAAoB,WACpC,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,SACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAM6lB,WAC1C5lB,QAAS,EAAKjd,KAAK2C,MAAMM,OACzB/D,MAAO,EAAKiJ,QAAQsS,oBAAoB,sBACvCjb,YAELnB,KAAK8J,QAAQ4E,KAAK,sBAAsB,WACtC,OAAO,EAAKoxB,OAAO,CACjBx/B,UAAW,SACXF,SAAU,EAAK8Z,GAAGomB,KAAK,EAAKxgC,QAAQ6e,MAAMulB,OAC1CtlB,QAAS,EAAKjd,KAAK2C,MAAMO,SACzBhE,MAAO,EAAKiJ,QAAQsS,oBAAoB,wBACvCjb,c,4BAIDJ,EAAY0jC,GAChB,IAAK,IAAIC,EAAW,EAAGC,EAAWF,EAAOrjC,OAAQsjC,EAAWC,EAAUD,IAAY,CAShF,IARA,IAAME,EAAQH,EAAOC,GACfG,EAAYtjC,MAAMC,QAAQojC,GAASA,EAAM,GAAKA,EAC9C1pB,EAAU3Z,MAAMC,QAAQojC,GAA4B,IAAjBA,EAAMxjC,OAAgB,CAACwjC,EAAM,IAAMA,EAAM,GAAM,CAACA,GAEnFE,EAAS9kC,KAAKka,GAAGmmB,YAAY,CACjC//B,UAAW,QAAUukC,IACpB1jC,SAEMgN,EAAM,EAAGG,EAAM4M,EAAQ9Z,OAAQ+M,EAAMG,EAAKH,IAAO,CACxD,IAAM42B,EAAM/kC,KAAK8J,QAAQ4E,KAAK,UAAYwM,EAAQ/M,IAC9C42B,GACFD,EAAOzjC,OAAsB,mBAAR0jC,EAAqBA,EAAI/kC,KAAK8J,SAAWi7B,GAGlED,EAAOlP,SAAS70B,M,yCAODA,GAAY,WACvB4lB,EAAQ5lB,GAAcf,KAAK08B,SAE3BxW,EAAYlmB,KAAK8J,QAAQ2B,OAAO,uBAsBtC,GArBAzL,KAAKglC,gBAAgBre,EAAO,CAC1B,iBAAkB,WAChB,MAAkC,SAA3BT,EAAU,cAEnB,mBAAoB,WAClB,MAAoC,WAA7BA,EAAU,gBAEnB,sBAAuB,WACrB,MAAuC,cAAhCA,EAAU,mBAEnB,sBAAuB,WACrB,MAAuC,cAAhCA,EAAU,mBAEnB,wBAAyB,WACvB,MAAyC,gBAAlCA,EAAU,qBAEnB,0BAA2B,WACzB,MAA2C,kBAApCA,EAAU,yBAIjBA,EAAU,eAAgB,CAC5B,IAAMoc,EAAYpc,EAAU,eAAevZ,MAAM,KAAKC,KAAI,SAAC1O,GACzD,OAAOA,EAAKqW,QAAQ,UAAW,IAC5BA,QAAQ,OAAQ,IAChBA,QAAQ,OAAQ,OAEftM,EAAWzC,EAAMxE,KAAKshC,EAAWtiC,KAAK4J,gBAAgBzK,KAAKa,OAEjE2mB,EAAM3lB,KAAK,wBAAwBP,MAAK,SAAC0N,EAAK3C,GAC5C,IAAMy5B,EAAQ9kC,IAAEqL,GAEV05B,EAAaD,EAAMzkC,KAAK,SAAW,IAASyH,EAAW,GAC7Dg9B,EAAM9R,YAAY,UAAW+R,MAE/Bve,EAAM3lB,KAAK,0BAA0BuX,KAAKtQ,GAAUge,IAAI,cAAehe,GAGzE,GAAIie,EAAU,aAAc,CAC1B,IAAME,EAAWF,EAAU,aAC3BS,EAAM3lB,KAAK,wBAAwBP,MAAK,SAAC0N,EAAK3C,GAC5C,IAAMy5B,EAAQ9kC,IAAEqL,GAEV05B,EAAaD,EAAMzkC,KAAK,SAAW,IAAS4lB,EAAW,GAC7D6e,EAAM9R,YAAY,UAAW+R,MAE/Bve,EAAM3lB,KAAK,0BAA0BuX,KAAK6N,GAE1C,IAAM2K,EAAe7K,EAAU,kBAC/BS,EAAM3lB,KAAK,4BAA4BP,MAAK,SAAC0N,EAAK3C,GAChD,IAAMy5B,EAAQ9kC,IAAEqL,GACV05B,EAAaD,EAAMzkC,KAAK,SAAW,IAASuwB,EAAe,GACjEkU,EAAM9R,YAAY,UAAW+R,MAE/Bve,EAAM3lB,KAAK,8BAA8BuX,KAAKwY,GAGhD,GAAI7K,EAAU,eAAgB,CAC5B,IAAMc,EAAad,EAAU,eAC7BS,EAAM3lB,KAAK,8BAA8BP,MAAK,SAAC0N,EAAK3C,GAElD,IAAM05B,EAAa/kC,IAAEqL,GAAMhL,KAAK,SAAW,IAASwmB,EAAa,GACjE,EAAK1mB,UAAY4kC,EAAY,UAAY,S,sCAK/BnkC,EAAYokC,GAAO,WACjChlC,IAAEM,KAAK0kC,GAAO,SAACC,EAAU/2B,GACvB,EAAK6L,GAAGmrB,gBAAgBtkC,EAAWC,KAAKokC,GAAW/2B,U,uCAItC8N,GACf,IAOImpB,EANEjE,EAAUlhC,IAAEgc,EAAMI,OAAOjL,YACzBi0B,EAAoBlE,EAAQjzB,OAC5Bo3B,EAAWnE,EAAQrgC,KAAK,uCACxBykC,EAAepE,EAAQrgC,KAAK,sCAC5B0kC,EAAiBrE,EAAQrgC,KAAK,wCAIpC,QAAsBya,IAAlBU,EAAMwpB,QAAuB,CAC/B,IAAMC,EAAazlC,IAAEgc,EAAMI,QAAQjK,SACnCgzB,EAAY,CACVvN,EAAG5b,EAAM0pB,MAAQD,EAAW3/B,KAC5B6xB,EAAG3b,EAAM2pB,MAAQF,EAAWz5B,UAG9Bm5B,EAAY,CACVvN,EAAG5b,EAAMwpB,QACT7N,EAAG3b,EAAM4pB,SAIb,IAAMnT,EACDtS,KAAK0lB,KAAKV,EAAUvN,EAvBP,KAuByB,EADrCnF,EAEDtS,KAAK0lB,KAAKV,EAAUxN,EAxBP,KAwByB,EAG3C2N,EAAaxf,IAAI,CAAE5b,MAAOuoB,EAAQ,KAAM1wB,OAAQ0wB,EAAQ,OACxD4S,EAAShlC,KAAK,QAASoyB,EAAQ,IAAMA,GAEjCA,EAAQ,GAAKA,EAAQ5yB,KAAKF,QAAQ2jC,mBAAmBC,KACvDgC,EAAezf,IAAI,CAAE5b,MAAOuoB,EAAQ,EAAI,OAGtCA,EAAQ,GAAKA,EAAQ5yB,KAAKF,QAAQ2jC,mBAAmB7Y,KACvD8a,EAAezf,IAAI,CAAE/jB,OAAQ0wB,EAAQ,EAAI,OAG3C2S,EAAkBllC,KAAKuyB,EAAQ,MAAQA,Q,6MC16BtBqT,G,WACnB,WAAYn8B,I,4FAAS,SACnB9J,KAAK8J,QAAUA,EAEf9J,KAAK48B,QAAUz8B,IAAE5C,QACjByC,KAAKkM,UAAY/L,IAAE4J,UAEnB/J,KAAKka,GAAK/Z,IAAEuB,WAAWwY,GACvBla,KAAK+Z,MAAQjQ,EAAQmQ,WAAW4E,KAChC7e,KAAK4vB,QAAU9lB,EAAQmQ,WAAWgB,OAClCjb,KAAK08B,SAAW5yB,EAAQmQ,WAAW0iB,QACnC38B,KAAKqlB,UAAYvb,EAAQmQ,WAAW0B,SACpC3b,KAAKg8B,WAAalyB,EAAQmQ,WAAWgiB,UACrCj8B,KAAKF,QAAUgK,EAAQhK,QAEvBE,KAAKkmC,aAAc,EACnBlmC,KAAKmmC,aAAenmC,KAAKmmC,aAAahnC,KAAKa,M,kEAI3C,OAAQA,KAAKF,QAAQ8zB,U,mCAGV,WACX5zB,KAAKF,QAAQ68B,QAAU38B,KAAKF,QAAQ68B,SAAW,GAE1C38B,KAAKF,QAAQ68B,QAAQv7B,OAGxBpB,KAAK8J,QAAQ2B,OAAO,gBAAiBzL,KAAK08B,SAAU18B,KAAKF,QAAQ68B,SAFjE38B,KAAK08B,SAASniB,OAKZva,KAAKF,QAAQsmC,kBACfpmC,KAAK08B,SAAS9G,SAAS51B,KAAKF,QAAQsmC,kBAGtCpmC,KAAKqmC,iBAAgB,GAErBrmC,KAAK+Z,MAAMjZ,GAAG,yDAAyD,WACrE,EAAKgJ,QAAQ2B,OAAO,iCAGtBzL,KAAK8J,QAAQ2B,OAAO,8BAChBzL,KAAKF,QAAQwmC,kBACftmC,KAAK48B,QAAQ97B,GAAG,gBAAiBd,KAAKmmC,gB,gCAKxCnmC,KAAK08B,SAAS78B,WAAW8D,SAErB3D,KAAKF,QAAQwmC,kBACftmC,KAAK48B,QAAQjjB,IAAI,gBAAiB3Z,KAAKmmC,gB,qCAKzC,GAAInmC,KAAK4vB,QAAQjgB,SAAS,cACxB,OAAO,EAGT,IAAM42B,EAAevmC,KAAK4vB,QAAQtW,cAC5BktB,EAAcxmC,KAAK4vB,QAAQvlB,QAC3Bo8B,EAAgBzmC,KAAK08B,SAASx6B,SAC9BwkC,EAAkB1mC,KAAKg8B,WAAW95B,SAGpCykC,EAAiB,EACjB3mC,KAAKF,QAAQ8mC,iBACfD,EAAiBxmC,IAAEH,KAAKF,QAAQ8mC,gBAAgBttB,eAGlD,IAAMutB,EAAgB7mC,KAAKkM,UAAUE,YAC/B06B,EAAkB9mC,KAAK4vB,QAAQtd,SAASnG,IAExC46B,EAAiBD,EAAkBH,EACnCK,EAFqBF,EAAkBP,EAEOI,EAAiBF,EAAgBC,GAEhF1mC,KAAKkmC,aACPW,EAAgBE,GAAoBF,EAAgBG,EAAyBP,GAC9EzmC,KAAKkmC,aAAc,EACnBlmC,KAAKqlB,UAAUY,IAAI,CACjBghB,UAAWjnC,KAAK08B,SAASpjB,gBAE3BtZ,KAAK08B,SAASzW,IAAI,CAChBvT,SAAU,QACVvG,IAAKw6B,EACLt8B,MAAOm8B,EACPU,OAAQ,OAEDlnC,KAAKkmC,cACZW,EAAgBE,GAAoBF,EAAgBG,KACtDhnC,KAAKkmC,aAAc,EACnBlmC,KAAK08B,SAASzW,IAAI,CAChBvT,SAAU,WACVvG,IAAK,EACL9B,MAAO,OACP68B,OAAQ,SAEVlnC,KAAKqlB,UAAUY,IAAI,CACjBghB,UAAW,Q,sCAKD/J,GACVA,EACFl9B,KAAK08B,SAAS3D,UAAU/4B,KAAK4vB,SAEzB5vB,KAAKF,QAAQsmC,kBACfpmC,KAAK08B,SAAS9G,SAAS51B,KAAKF,QAAQsmC,kBAGpCpmC,KAAKF,QAAQwmC,kBACftmC,KAAKmmC,iB,uCAIQjJ,GACfl9B,KAAKka,GAAGmrB,gBAAgBrlC,KAAK08B,SAAS17B,KAAK,mBAAoBk8B,GAE/Dl9B,KAAKqmC,gBAAgBnJ,K,qCAGR7D,GACbr5B,KAAKka,GAAGmrB,gBAAgBrlC,KAAK08B,SAAS17B,KAAK,iBAAkBq4B,GACzDA,EACFr5B,KAAKu6B,aAELv6B,KAAKw6B,a,+BAIA2M,GACP,IAAIC,EAAOpnC,KAAK08B,SAAS17B,KAAK,UACzBmmC,IACHC,EAAOA,EAAKl8B,IAAI,wBAElBlL,KAAKka,GAAGmtB,UAAUD,GAAM,K,iCAGfD,GACT,IAAIC,EAAOpnC,KAAK08B,SAAS17B,KAAK,UACzBmmC,IACHC,EAAOA,EAAKl8B,IAAI,wBAElBlL,KAAKka,GAAGmtB,UAAUD,GAAM,Q,6MC9IPE,G,WACnB,WAAYx9B,I,4FAAS,SACnB9J,KAAK8J,QAAUA,EAEf9J,KAAKka,GAAK/Z,IAAEuB,WAAWwY,GACvBla,KAAKunC,MAAQpnC,IAAE4J,SAASoT,MACxBnd,KAAK4vB,QAAU9lB,EAAQmQ,WAAWgB,OAClCjb,KAAKF,QAAUgK,EAAQhK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ4e,SAEzB5U,EAAQ4E,KAAK,uBAAwB1O,KAAKF,QAAQ4e,SAAS/Y,KAAK,oB,4DAIhE,IAAM5E,EAAaf,KAAKF,QAAQ0nC,cAAgBxnC,KAAKunC,MAAQvnC,KAAKF,QAAQqY,UACpEgF,EAAO,CACX,2CADW,2CAE2Bnd,KAAKF,QAAQiM,GAFxC,qCAEuE/L,KAAK2B,KAAKqC,KAAKG,cAFtF,sDAG0BnE,KAAKF,QAAQiM,GAHvC,oFAIX,SACA,2CALW,2CAM2B/L,KAAKF,QAAQiM,GANxC,qCAMuE/L,KAAK2B,KAAKqC,KAAKN,IANtF,sDAO0B1D,KAAKF,QAAQiM,GAPvC,mGAQX,SACC/L,KAAKF,QAAQ2nC,kBAMV,GALAtnC,IAAE,UAAUkB,OAAOrB,KAAKka,GAAGwtB,SAAS,CACpCpnC,UAAW,iCACXiY,KAAMvY,KAAK2B,KAAKqC,KAAKI,gBACrBujC,SAAS,IACRxmC,UAAUd,OAEfF,IAAE,UAAUkB,OAAOrB,KAAKka,GAAGwtB,SAAS,CAClCpnC,UAAW,2BACXiY,KAAMvY,KAAK2B,KAAKqC,KAAKK,YACrBsjC,SAAS,IACRxmC,UAAUd,QACb0M,KAAK,IAGD66B,EAAS,wCAAH,OADQ,0DACR,oBAAkE5nC,KAAK2B,KAAKqC,KAAKvB,OAAjF,eAEZzC,KAAK6nC,QAAU7nC,KAAKka,GAAG4tB,OAAO,CAC5BxnC,UAAW,cACXshC,MAAO5hC,KAAK2B,KAAKqC,KAAKvB,OACtBslC,KAAM/nC,KAAKF,QAAQkoC,YACnB7qB,KAAMA,EACNyqB,OAAQA,IACPzmC,SAASy0B,SAAS70B,K,gCAIrBf,KAAKka,GAAG+tB,WAAWjoC,KAAK6nC,SACxB7nC,KAAK6nC,QAAQlkC,W,mCAGFukC,EAAQd,GACnBc,EAAOpnC,GAAG,YAAY,SAACqb,GACjBA,EAAM8H,UAAY/kB,GAAI2b,KAAKuJ,QAC7BjI,EAAME,iBACN+qB,EAAKtrB,QAAQ,e,oCAQLqsB,EAAUC,EAAWC,GACjCroC,KAAKka,GAAGmtB,UAAUc,EAAUC,EAAU9zB,OAAS+zB,EAAS/zB,S,qCAS3Csd,GAAU,WACvB,OAAOzxB,IAAEk1B,UAAS,SAACC,GACjB,IAAM8S,EAAY,EAAKP,QAAQ7mC,KAAK,mBAC9BqnC,EAAW,EAAKR,QAAQ7mC,KAAK,kBAC7BmnC,EAAW,EAAKN,QAAQ7mC,KAAK,kBAC7BsnC,EAAmB,EAAKT,QAC3B7mC,KAAK,wDACFunC,EAAe,EAAKV,QACvB7mC,KAAK,kDAER,EAAKkZ,GAAGsuB,cAAc,EAAKX,SAAS,WAClC,EAAK/9B,QAAQyR,aAAa,iBAGrBqW,EAASluB,KAAOuJ,EAAKS,WAAWkkB,EAASrZ,QAC5CqZ,EAASluB,IAAMkuB,EAASrZ,MAG1B6vB,EAAUtnC,GAAG,8BAA8B,WAGzC8wB,EAASrZ,KAAO6vB,EAAU9zB,MAC1B,EAAKm0B,cAAcN,EAAUC,EAAWC,MACvC/zB,IAAIsd,EAASrZ,MAEhB8vB,EAASvnC,GAAG,8BAA8B,WAGnC8wB,EAASrZ,MACZ6vB,EAAU9zB,IAAI+zB,EAAS/zB,OAEzB,EAAKm0B,cAAcN,EAAUC,EAAWC,MACvC/zB,IAAIsd,EAASluB,KAEXqN,EAAIlI,gBACPw/B,EAASvsB,QAAQ,SAGnB,EAAK2sB,cAAcN,EAAUC,EAAWC,GACxC,EAAKK,aAAaL,EAAUF,GAC5B,EAAKO,aAAaN,EAAWD,GAE7B,IAAMQ,OAA8CltB,IAAzBmW,EAASG,YAChCH,EAASG,YAAc,EAAKjoB,QAAQhK,QAAQ4+B,gBAEhD4J,EAAiBM,KAAK,UAAWD,GAEjC,IAAME,GAAqBjX,EAASluB,KACxB,EAAKoG,QAAQhK,QAAQuE,YAEjCkkC,EAAaK,KAAK,UAAWC,GAE7BV,EAAS3S,IAAI,SAAS,SAACrZ,GACrBA,EAAME,iBAENiZ,EAASG,QAAQ,CACfnQ,MAAOsM,EAAStM,MAChB5hB,IAAK2kC,EAAS/zB,MACdiE,KAAM6vB,EAAU9zB,MAChByd,YAAauW,EAAiBrQ,GAAG,YACjCjG,cAAeuW,EAAatQ,GAAG,cAEjC,EAAK/d,GAAG+tB,WAAW,EAAKJ,eAI5B,EAAK3tB,GAAG4uB,eAAe,EAAKjB,SAAS,WAEnCO,EAAUzuB,MACV0uB,EAAS1uB,MACTwuB,EAASxuB,MAEgB,YAArB2b,EAASyT,SACXzT,EAASI,YAIb,EAAKxb,GAAG8uB,WAAW,EAAKnB,YACvBhS,Y,6BAME,WACCjE,EAAW5xB,KAAK8J,QAAQ2B,OAAO,sBAErCzL,KAAK8J,QAAQ2B,OAAO,oBACpBzL,KAAKipC,eAAerX,GAAUkE,MAAK,SAAClE,GAClC,EAAK9nB,QAAQ2B,OAAO,uBACpB,EAAK3B,QAAQ2B,OAAO,oBAAqBmmB,MACxC5mB,MAAK,WACN,EAAKlB,QAAQ2B,OAAO,+B,6MC1KLy9B,G,WACnB,WAAYp/B,GAAS,Y,4FAAA,SACnB9J,KAAK8J,QAAUA,EAEf9J,KAAKka,GAAK/Z,IAAEuB,WAAWwY,GACvBla,KAAKF,QAAUgK,EAAQhK,QACvBE,KAAKwZ,OAAS,CACZ,0EAA2E,WACzE,EAAK+jB,UAEP,6DAA8D,WAC5D,EAAKhjB,S,kEAMT,OAAQ/U,EAAMsJ,QAAQ9O,KAAKF,QAAQqpC,QAAQnlC,Q,mCAI3ChE,KAAKopC,SAAWppC,KAAKka,GAAGivB,QAAQ,CAC9B7oC,UAAW,oBACXP,SAAU,SAACG,GACQA,EAAMc,KAAK,0CACnBugC,QAAQ,iDAElBpgC,SAASy0B,SAAS51B,KAAKF,QAAQqY,WAClC,IAAMkxB,EAAWrpC,KAAKopC,SAASpoC,KAAK,0CAEpChB,KAAK8J,QAAQ2B,OAAO,gBAAiB49B,EAAUrpC,KAAKF,QAAQqpC,QAAQnlC,MAEpEhE,KAAKopC,SAAStoC,GAAG,aAAa,SAAC2hB,GAAQA,EAAEpG,sB,gCAIzCrc,KAAKopC,SAASzlC,W,+BAKd,GAAK3D,KAAK8J,QAAQ2B,OAAO,mBAAzB,CAKA,IAAM+V,EAAMxhB,KAAK8J,QAAQ2B,OAAO,uBAChC,GAAI+V,EAAIV,eAAiBU,EAAIhC,aAAc,CACzC,IAAM0H,EAASpM,GAAIzJ,SAASmQ,EAAIvC,GAAInE,GAAIlK,UAClC04B,EAAOnpC,IAAE+mB,GAAQtmB,KAAK,QAC5BZ,KAAKopC,SAASpoC,KAAK,KAAKJ,KAAK,OAAQ0oC,GAAM/wB,KAAK+wB,GAEhD,IAAMjwB,EAAMyB,GAAI5B,mBAAmBgO,GAC7BqiB,EAAkBppC,IAAEH,KAAKF,QAAQqY,WAAW7F,SAClD+G,EAAIlN,KAAOo9B,EAAgBp9B,IAC3BkN,EAAIpT,MAAQsjC,EAAgBtjC,KAE5BjG,KAAKopC,SAASnjB,IAAI,CAChB0P,QAAS,QACT1vB,KAAMoT,EAAIpT,KACVkG,IAAKkN,EAAIlN,WAGXnM,KAAKua,YArBLva,KAAKua,S,6BA0BPva,KAAKopC,SAAS7uB,Y,6MCpEGivB,G,WACnB,WAAY1/B,I,4FAAS,SACnB9J,KAAK8J,QAAUA,EACf9J,KAAKka,GAAK/Z,IAAEuB,WAAWwY,GACvBla,KAAKunC,MAAQpnC,IAAE4J,SAASoT,MACxBnd,KAAK4vB,QAAU9lB,EAAQmQ,WAAWgB,OAClCjb,KAAKF,QAAUgK,EAAQhK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ4e,S,4DAIzB,IAAI+qB,EAAkB,GACtB,GAAIzpC,KAAKF,QAAQu2B,qBAAsB,CACrC,IAAMxF,EAAOvQ,KAAKopB,MAAMppB,KAAKqpB,IAAI3pC,KAAKF,QAAQu2B,sBAAwB/V,KAAKqpB,IAAI,OACzEC,EAAuF,GAAvE5pC,KAAKF,QAAQu2B,qBAAuB/V,KAAKupB,IAAI,KAAMhZ,IAAO5J,QAAQ,GACrE,IAAM,SAAS4J,GAAQ,IAC1C4Y,EAAkB,UAAH,OAAazpC,KAAK2B,KAAKa,MAAMgB,gBAAkB,MAAQomC,EAAvD,YAGjB,IAAM7oC,EAAaf,KAAKF,QAAQ0nC,cAAgBxnC,KAAKunC,MAAQvnC,KAAKF,QAAQqY,UACpEgF,EAAO,CACX,wEACE,sCAAwCnd,KAAKF,QAAQiM,GAAK,6BAA+B/L,KAAK2B,KAAKa,MAAMe,gBAAkB,WAC3H,qCAAuCvD,KAAKF,QAAQiM,GAAK,6EACzD,mEACA09B,EACF,SACA,gDACE,qCAAuCzpC,KAAKF,QAAQiM,GAAK,6BAA+B/L,KAAK2B,KAAKa,MAAMkB,IAAM,WAC9G,oCAAsC1D,KAAKF,QAAQiM,GAAK,mFAC1D,UACAgB,KAAK,IAED66B,EAAS,wCAAH,OADQ,2DACR,oBAAkE5nC,KAAK2B,KAAKa,MAAMC,OAAlF,eAEZzC,KAAK6nC,QAAU7nC,KAAKka,GAAG4tB,OAAO,CAC5BlG,MAAO5hC,KAAK2B,KAAKa,MAAMC,OACvBslC,KAAM/nC,KAAKF,QAAQkoC,YACnB7qB,KAAMA,EACNyqB,OAAQA,IACPzmC,SAASy0B,SAAS70B,K,gCAIrBf,KAAKka,GAAG+tB,WAAWjoC,KAAK6nC,SACxB7nC,KAAK6nC,QAAQlkC,W,mCAGFukC,EAAQd,GACnBc,EAAOpnC,GAAG,YAAY,SAACqb,GACjBA,EAAM8H,UAAY/kB,GAAI2b,KAAKuJ,QAC7BjI,EAAME,iBACN+qB,EAAKtrB,QAAQ,e,6BAKZ,WACL9b,KAAK8J,QAAQ2B,OAAO,oBACpBzL,KAAK8pC,kBAAkBhU,MAAK,SAACt1B,GAE3B,EAAK0Z,GAAG+tB,WAAW,EAAKJ,SACxB,EAAK/9B,QAAQ2B,OAAO,uBAEA,iBAATjL,EAEL,EAAKV,QAAQ+b,UAAUkuB,kBACzB,EAAKjgC,QAAQyR,aAAa,oBAAqB/a,GAE/C,EAAKsJ,QAAQ2B,OAAO,qBAAsBjL,GAG5C,EAAKsJ,QAAQ2B,OAAO,gCAAiCjL,MAEtDwK,MAAK,WACN,EAAKlB,QAAQ2B,OAAO,4B,wCAUN,WAChB,OAAOtL,IAAEk1B,UAAS,SAACC,GACjB,IAAM0U,EAAc,EAAKnC,QAAQ7mC,KAAK,qBAChCipC,EAAY,EAAKpC,QAAQ7mC,KAAK,mBAC9BkpC,EAAY,EAAKrC,QAAQ7mC,KAAK,mBAEpC,EAAKkZ,GAAGsuB,cAAc,EAAKX,SAAS,WAClC,EAAK/9B,QAAQyR,aAAa,gBAG1ByuB,EAAYG,YAAYH,EAAYl2B,QAAQhT,GAAG,UAAU,SAACqb,GACxDmZ,EAASG,QAAQtZ,EAAMI,OAAO2Z,OAAS/Z,EAAMI,OAAO3d,UACnD0V,IAAI,KAEP21B,EAAUnpC,GAAG,8BAA8B,WACzC,EAAKoZ,GAAGmtB,UAAU6C,EAAWD,EAAU31B,UACtCA,IAAI,IAEFvD,EAAIlI,gBACPohC,EAAUnuB,QAAQ,SAGpBouB,EAAUrpC,OAAM,SAACsb,GACfA,EAAME,iBACNiZ,EAASG,QAAQwU,EAAU31B,UAG7B,EAAKo0B,aAAauB,EAAWC,MAG/B,EAAKhwB,GAAG4uB,eAAe,EAAKjB,SAAS,WACnCmC,EAAYrwB,MACZswB,EAAUtwB,MACVuwB,EAAUvwB,MAEe,YAArB2b,EAASyT,SACXzT,EAASI,YAIb,EAAKxb,GAAG8uB,WAAW,EAAKnB,iB,6MCxHTuC,G,WACnB,WAAYtgC,GAAS,Y,4FAAA,SACnB9J,KAAK8J,QAAUA,EACf9J,KAAKka,GAAK/Z,IAAEuB,WAAWwY,GAEvBla,KAAK2b,SAAW7R,EAAQmQ,WAAW0B,SAAS,GAC5C3b,KAAKF,QAAUgK,EAAQhK,QAEvBE,KAAKwZ,OAAS,CACZ,qCAAsC,WACpC,EAAKe,S,kEAMT,OAAQ/U,EAAMsJ,QAAQ9O,KAAKF,QAAQqpC,QAAQ3mC,S,mCAI3CxC,KAAKopC,SAAWppC,KAAKka,GAAGivB,QAAQ,CAC9B7oC,UAAW,uBACVa,SAASy0B,SAAS51B,KAAKF,QAAQqY,WAClC,IAAMkxB,EAAWrpC,KAAKopC,SAASpoC,KAAK,0CACpChB,KAAK8J,QAAQ2B,OAAO,gBAAiB49B,EAAUrpC,KAAKF,QAAQqpC,QAAQ3mC,OAEpExC,KAAKopC,SAAStoC,GAAG,aAAa,SAAC2hB,GAAQA,EAAEpG,sB,gCAIzCrc,KAAKopC,SAASzlC,W,6BAGT4Y,EAAQJ,GACb,GAAIrB,GAAIrF,MAAM8G,GAAS,CACrB,IAAM7J,EAAWvS,IAAEoc,GAAQjK,SACrBi3B,EAAkBppC,IAAEH,KAAKF,QAAQqY,WAAW7F,SAC9C+G,EAAM,GACNrZ,KAAKF,QAAQuqC,YACfhxB,EAAIpT,KAAOkW,EAAM0pB,MAAQ,GACzBxsB,EAAIlN,IAAMgQ,EAAM2pB,OAEhBzsB,EAAM3G,EAER2G,EAAIlN,KAAOo9B,EAAgBp9B,IAC3BkN,EAAIpT,MAAQsjC,EAAgBtjC,KAE5BjG,KAAKopC,SAASnjB,IAAI,CAChB0P,QAAS,QACT1vB,KAAMoT,EAAIpT,KACVkG,IAAKkN,EAAIlN,WAGXnM,KAAKua,S,6BAKPva,KAAKopC,SAAS7uB,Y,6MC9DG+vB,G,WACnB,WAAYxgC,GAAS,Y,4FAAA,SACnB9J,KAAK8J,QAAUA,EAEf9J,KAAKka,GAAK/Z,IAAEuB,WAAWwY,GACvBla,KAAKF,QAAUgK,EAAQhK,QACvBE,KAAKwZ,OAAS,CACZ,uBAAwB,SAAC8jB,EAAI7a,GAC3B,EAAK8a,OAAO9a,EAAElG,SAEhB,uDAAwD,WACtD,EAAKghB,UAEP,qCAAsC,WACpC,EAAKhjB,S,kEAMT,OAAQ/U,EAAMsJ,QAAQ9O,KAAKF,QAAQqpC,QAAQ7kC,S,mCAI3CtE,KAAKopC,SAAWppC,KAAKka,GAAGivB,QAAQ,CAC9B7oC,UAAW,uBACVa,SAASy0B,SAAS51B,KAAKF,QAAQqY,WAClC,IAAMkxB,EAAWrpC,KAAKopC,SAASpoC,KAAK,0CAEpChB,KAAK8J,QAAQ2B,OAAO,gBAAiB49B,EAAUrpC,KAAKF,QAAQqpC,QAAQ7kC,OAGhEyM,EAAI3H,MACNW,SAAS0mB,YAAY,4BAA4B,GAAO,GAG1DzwB,KAAKopC,SAAStoC,GAAG,aAAa,SAAC2hB,GAAQA,EAAEpG,sB,gCAIzCrc,KAAKopC,SAASzlC,W,6BAGT4Y,GACL,GAAIvc,KAAK8J,QAAQ8Q,aACf,OAAO,EAGT,IAAMjK,EAASmK,GAAInK,OAAO4L,GAE1B,GAAI5L,EAAQ,CACV,IAAM0I,EAAMyB,GAAI5B,mBAAmBqD,GAC7BgtB,EAAkBppC,IAAEH,KAAKF,QAAQqY,WAAW7F,SAClD+G,EAAIlN,KAAOo9B,EAAgBp9B,IAC3BkN,EAAIpT,MAAQsjC,EAAgBtjC,KAE5BjG,KAAKopC,SAASnjB,IAAI,CAChB0P,QAAS,QACT1vB,KAAMoT,EAAIpT,KACVkG,IAAKkN,EAAIlN,WAGXnM,KAAKua,OAGP,OAAO5J,I,6BAIP3Q,KAAKopC,SAAS7uB,Y,6MCtEGgwB,G,WACnB,WAAYzgC,I,4FAAS,SACnB9J,KAAK8J,QAAUA,EAEf9J,KAAKka,GAAK/Z,IAAEuB,WAAWwY,GACvBla,KAAKunC,MAAQpnC,IAAE4J,SAASoT,MACxBnd,KAAK4vB,QAAU9lB,EAAQmQ,WAAWgB,OAClCjb,KAAKF,QAAUgK,EAAQhK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ4e,S,4DAIzB,IAAM3d,EAAaf,KAAKF,QAAQ0nC,cAAgBxnC,KAAKunC,MAAQvnC,KAAKF,QAAQqY,UACpEgF,EAAO,CACX,qDADW,4CAE4Bnd,KAAKF,QAAQiM,GAFzC,qCAEwE/L,KAAK2B,KAAKkC,MAAMH,IAFxF,sCAEyH1D,KAAK2B,KAAKkC,MAAME,UAFzI,+DAG2B/D,KAAKF,QAAQiM,GAHxC,oFAIX,UACAgB,KAAK,IAED66B,EAAS,wCAAH,OADQ,2DACR,oBAAkE5nC,KAAK2B,KAAKkC,MAAMpB,OAAlF,eAEZzC,KAAK6nC,QAAU7nC,KAAKka,GAAG4tB,OAAO,CAC5BlG,MAAO5hC,KAAK2B,KAAKkC,MAAMpB,OACvBslC,KAAM/nC,KAAKF,QAAQkoC,YACnB7qB,KAAMA,EACNyqB,OAAQA,IACPzmC,SAASy0B,SAAS70B,K,gCAIrBf,KAAKka,GAAG+tB,WAAWjoC,KAAK6nC,SACxB7nC,KAAK6nC,QAAQlkC,W,mCAGFukC,EAAQd,GACnBc,EAAOpnC,GAAG,YAAY,SAACqb,GACjBA,EAAM8H,UAAY/kB,GAAI2b,KAAKuJ,QAC7BjI,EAAME,iBACN+qB,EAAKtrB,QAAQ,e,sCAKHpY,GAEd,IAqCI8mC,EAnCEC,EAAU/mC,EAAImV,MAFH,8HAKX6xB,EAAUhnC,EAAImV,MADH,sDAIX8xB,EAASjnC,EAAImV,MADH,mCAIV+xB,EAAWlnC,EAAImV,MADH,qDAIZgyB,EAAUnnC,EAAImV,MADH,kEAIXiyB,EAAapnC,EAAImV,MADH,+CAIdkyB,EAAUrnC,EAAImV,MADH,6BAIXmyB,EAAWtnC,EAAImV,MADH,6DAIZoyB,EAAWvnC,EAAImV,MADH,kBAIZqyB,EAAWxnC,EAAImV,MADH,kBAIZsyB,EAAYznC,EAAImV,MADH,eAIbuyB,EAAU1nC,EAAImV,MADH,2DAIjB,GAAI4xB,GAAiC,KAAtBA,EAAQ,GAAGrpC,OAAe,CACvC,IAAMiqC,EAAYZ,EAAQ,GACtBa,EAAQ,EACZ,QAA0B,IAAfb,EAAQ,GAAoB,CACrC,IAAMc,EAAkBd,EAAQ,GAAG5xB,MAzCd,uCA0CrB,GAAI0yB,EACF,IAAK,IAAInsC,EAAI,CAAC,KAAM,GAAI,GAAI9B,EAAI,EAAGmB,EAAIW,EAAEgC,OAAQ9D,EAAImB,EAAGnB,IACtDguC,QAA4C,IAA3BC,EAAgBjuC,EAAI,GAAqB8B,EAAE9B,GAAK+oB,SAASklB,EAAgBjuC,EAAI,GAAI,IAAM,EAI9GktC,EAASrqC,IAAE,YACRS,KAAK,cAAe,GACpBA,KAAK,MAAO,2BAA6ByqC,GAAaC,EAAQ,EAAI,UAAYA,EAAQ,KACtF1qC,KAAK,QAAS,OAAOA,KAAK,SAAU,YAClC,GAAI8pC,GAAWA,EAAQ,GAAGtpC,OAC/BopC,EAASrqC,IAAE,YACRS,KAAK,cAAe,GACpBA,KAAK,MAAO,2BAA6B8pC,EAAQ,GAAK,WACtD9pC,KAAK,QAAS,OAAOA,KAAK,SAAU,OACpCA,KAAK,YAAa,MAClBA,KAAK,oBAAqB,aACxB,GAAI+pC,GAAUA,EAAO,GAAGvpC,OAC7BopC,EAASrqC,IAAE,YACRS,KAAK,cAAe,GACpBA,KAAK,MAAO+pC,EAAO,GAAK,iBACxB/pC,KAAK,QAAS,OAAOA,KAAK,SAAU,OACpCA,KAAK,QAAS,mBACZ,GAAIgqC,GAAYA,EAAS,GAAGxpC,OACjCopC,EAASrqC,IAAE,qEACRS,KAAK,cAAe,GACpBA,KAAK,MAAO,4BAA8BgqC,EAAS,IACnDhqC,KAAK,QAAS,OAAOA,KAAK,SAAU,YAClC,GAAIiqC,GAAWA,EAAQ,GAAGzpC,OAC/BopC,EAASrqC,IAAE,YACRS,KAAK,cAAe,GACpBA,KAAK,MAAO,qCAAuCiqC,EAAQ,IAC3DjqC,KAAK,QAAS,OAAOA,KAAK,SAAU,YAClC,GAAIkqC,GAAcA,EAAW,GAAG1pC,OACrCopC,EAASrqC,IAAE,qEACRS,KAAK,cAAe,GACpBA,KAAK,SAAU,OACfA,KAAK,QAAS,OACdA,KAAK,MAAO,4BAA8BkqC,EAAW,SACnD,GAAKC,GAAWA,EAAQ,GAAG3pC,QAAY4pC,GAAYA,EAAS,GAAG5pC,OAAS,CAC7E,IAAMoqC,EAAQT,GAAWA,EAAQ,GAAG3pC,OAAU2pC,EAAQ,GAAKC,EAAS,GACpER,EAASrqC,IAAE,qEACRS,KAAK,cAAe,GACpBA,KAAK,SAAU,OACfA,KAAK,QAAS,OACdA,KAAK,MAAO,+CAAiD4qC,EAAM,oBACjE,GAAIP,GAAYC,GAAYC,EACjCX,EAASrqC,IAAE,oBACRS,KAAK,MAAO8C,GACZ9C,KAAK,QAAS,OAAOA,KAAK,SAAU,WAClC,KAAIwqC,IAAWA,EAAQ,GAAGhqC,OAS/B,OAAO,EARPopC,EAASrqC,IAAE,YACRS,KAAK,cAAe,GACpBA,KAAK,MAAO,mDAAqD6qC,mBAAmBL,EAAQ,IAAM,0BAClGxqC,KAAK,QAAS,OAAOA,KAAK,SAAU,OACpCA,KAAK,YAAa,MAClBA,KAAK,oBAAqB,QAQ/B,OAFA4pC,EAAOjqC,SAAS,mBAETiqC,EAAO,K,6BAGT,WACCjyB,EAAOvY,KAAK8J,QAAQ2B,OAAO,0BACjCzL,KAAK8J,QAAQ2B,OAAO,oBACpBzL,KAAK0rC,gBAAgBnzB,GAAMud,MAAK,SAACpyB,GAE/B,EAAKwW,GAAG+tB,WAAW,EAAKJ,SACxB,EAAK/9B,QAAQ2B,OAAO,uBAGpB,IAAMvL,EAAQ,EAAKyrC,gBAAgBjoC,GAE/BxD,GAEF,EAAK4J,QAAQ2B,OAAO,oBAAqBvL,MAE1C8K,MAAK,WACN,EAAKlB,QAAQ2B,OAAO,4B,wCAUI,WAC1B,OAAOtL,IAAEk1B,UAAS,SAACC,GACjB,IAAMsW,EAAY,EAAK/D,QAAQ7mC,KAAK,mBAC9B6qC,EAAY,EAAKhE,QAAQ7mC,KAAK,mBAEpC,EAAKkZ,GAAGsuB,cAAc,EAAKX,SAAS,WAClC,EAAK/9B,QAAQyR,aAAa,gBAE1BqwB,EAAU9qC,GAAG,8BAA8B,WACzC,EAAKoZ,GAAGmtB,UAAUwE,EAAWD,EAAUt3B,UAGpCvD,EAAIlI,gBACP+iC,EAAU9vB,QAAQ,SAGpB+vB,EAAUhrC,OAAM,SAACsb,GACfA,EAAME,iBACNiZ,EAASG,QAAQmW,EAAUt3B,UAG7B,EAAKo0B,aAAakD,EAAWC,MAG/B,EAAK3xB,GAAG4uB,eAAe,EAAKjB,SAAS,WACnC+D,EAAUjyB,MACVkyB,EAAUlyB,MAEe,YAArB2b,EAASyT,SACXzT,EAASI,YAIb,EAAKxb,GAAG8uB,WAAW,EAAKnB,iB,6MCxNTiE,G,WACnB,WAAYhiC,I,4FAAS,SACnB9J,KAAK8J,QAAUA,EAEf9J,KAAKka,GAAK/Z,IAAEuB,WAAWwY,GACvBla,KAAKunC,MAAQpnC,IAAE4J,SAASoT,MACxBnd,KAAK4vB,QAAU9lB,EAAQmQ,WAAWgB,OAClCjb,KAAKF,QAAUgK,EAAQhK,QACvBE,KAAK2B,KAAO3B,KAAKF,QAAQ4e,S,4DAIzB,IAAM3d,EAAaf,KAAKF,QAAQ0nC,cAAgBxnC,KAAKunC,MAAQvnC,KAAKF,QAAQqY,UACpEgF,EAAO,CACX,0BACE,gKACA,uFACA,QACF,KACApQ,IAEF/M,KAAK6nC,QAAU7nC,KAAKka,GAAG4tB,OAAO,CAC5BlG,MAAO5hC,KAAK2B,KAAK7B,QAAQ6F,KACzBoiC,KAAM/nC,KAAKF,QAAQkoC,YACnB7qB,KAAMnd,KAAK+rC,qBACXnE,OAAQzqB,EACRpd,SAAU,SAACG,GACTA,EAAMc,KAAK,gCAAgCilB,IAAI,CAC7C,aAAc,IACd,SAAY,cAGf9kB,SAASy0B,SAAS70B,K,gCAIrBf,KAAKka,GAAG+tB,WAAWjoC,KAAK6nC,SACxB7nC,KAAK6nC,QAAQlkC,W,2CAGM,WACbswB,EAASj0B,KAAKF,QAAQm0B,OAAOljB,EAAI9H,MAAQ,MAAQ,MACvD,OAAO5K,OAAOob,KAAKwa,GAAQrnB,KAAI,SAAC1N,GAC9B,IAAM8sC,EAAU/X,EAAO/0B,GACjB+sC,EAAO9rC,IAAE,iDAKf,OAJA8rC,EAAK5qC,OAAOlB,IAAE,eAAiBjB,EAAM,kBAAkB+mB,IAAI,CACzD,MAAS,IACT,eAAgB,MACd5kB,OAAOlB,IAAE,WAAWE,KAAK,EAAKyJ,QAAQ4E,KAAK,QAAUs9B,IAAYA,IAC9DC,EAAK5rC,UACX0M,KAAK,M,uCAQO,WACf,OAAO5M,IAAEk1B,UAAS,SAACC,GACjB,EAAKpb,GAAGsuB,cAAc,EAAKX,SAAS,WAClC,EAAK/9B,QAAQyR,aAAa,gBAC1B+Z,EAASG,aAEX,EAAKvb,GAAG8uB,WAAW,EAAKnB,YACvBhS,Y,6BAGE,WACL71B,KAAK8J,QAAQ2B,OAAO,oBACpBzL,KAAKksC,iBAAiBpW,MAAK,WACzB,EAAKhsB,QAAQ2B,OAAO,+B,yMCvE1B,IAGqB0gC,G,WACnB,WAAYriC,GAAS,Y,4FAAA,SACnB9J,KAAK8J,QAAUA,EACf9J,KAAKka,GAAK/Z,IAAEuB,WAAWwY,GACvBla,KAAKF,QAAUgK,EAAQhK,QAEvBE,KAAKosC,SAAU,EACfpsC,KAAKqsC,eAAgB,EACrBrsC,KAAK6lC,MAAQ,KACb7lC,KAAK8lC,MAAQ,KAEb9lC,KAAKwZ,OAAS,CACZ,yBAA0B,SAACiJ,GACrB,EAAK3iB,QAAQ8b,UACf6G,EAAEpG,iBACFoG,EAAE0Z,kBACF,EAAKkQ,eAAgB,EACrB,EAAK9O,QAAO,KAGhB,uBAAwB,SAACD,EAAI7a,GAC3B,EAAKojB,MAAQpjB,EAAEojB,MACf,EAAKC,MAAQrjB,EAAEqjB,OAEjB,wDAAyD,SAACxI,EAAI7a,GACxD,EAAK3iB,QAAQ8b,UAAY,EAAKywB,gBAChC,EAAKxG,MAAQpjB,EAAEojB,MACf,EAAKC,MAAQrjB,EAAEqjB,MACf,EAAKvI,UAEP,EAAK8O,eAAgB,GAEvB,+EAAgF,WAC9E,EAAK9xB,QAEP,sBAAuB,WAChB,EAAK6uB,SAASnR,GAAG,mBACpB,EAAK1d,S,kEAOX,OAAOva,KAAKF,QAAQ8zB,UAAYpuB,EAAMsJ,QAAQ9O,KAAKF,QAAQqpC,QAAQmD,O,mCAGxD,WACXtsC,KAAKopC,SAAWppC,KAAKka,GAAGivB,QAAQ,CAC9B7oC,UAAW,qBACVa,SAASy0B,SAAS51B,KAAKF,QAAQqY,WAClC,IAAMkxB,EAAWrpC,KAAKopC,SAASpoC,KAAK,oBAEpChB,KAAK8J,QAAQ2B,OAAO,gBAAiB49B,EAAUrpC,KAAKF,QAAQqpC,QAAQmD,KAGpEtsC,KAAKopC,SAAStoC,GAAG,aAAa,WAAQ,EAAKsrC,SAAU,KAErDpsC,KAAKopC,SAAStoC,GAAG,WAAW,WAAQ,EAAKsrC,SAAU,O,gCAInDpsC,KAAKopC,SAASzlC,W,6BAGT4oC,GACL,IAAMrmB,EAAYlmB,KAAK8J,QAAQ2B,OAAO,uBACtC,IAAIya,EAAUZ,OAAWY,EAAUZ,MAAMxE,gBAAiByrB,EAiBxDvsC,KAAKua,WAjBiE,CACtE,IAAItO,EAAO,CACThG,KAAMjG,KAAK6lC,MACX15B,IAAKnM,KAAK8lC,OAGNyD,EAAkBppC,IAAEH,KAAKF,QAAQqY,WAAW7F,SAClDrG,EAAKE,KAAOo9B,EAAgBp9B,IAC5BF,EAAKhG,MAAQsjC,EAAgBtjC,KAE7BjG,KAAKopC,SAASnjB,IAAI,CAChB0P,QAAS,QACT1vB,KAAMqa,KAAKkc,IAAIvwB,EAAKhG,KAAM,IAlFD,EAmFzBkG,IAAKF,EAAKE,IAlFe,IAoF3BnM,KAAK8J,QAAQ2B,OAAO,6BAA8BzL,KAAKopC,a,qCAM5C/P,GACbr5B,KAAKka,GAAGmrB,gBAAgBrlC,KAAKopC,SAASpoC,KAAK,iBAAkBq4B,GACzDA,GACFr5B,KAAKua,S,6BAKHva,KAAKosC,SACPpsC,KAAKopC,SAAS7uB,Y,yMChGpB,IAEqBiyB,G,WACnB,WAAY1iC,GAAS,Y,4FAAA,SACnB9J,KAAK8J,QAAUA,EAEf9J,KAAKka,GAAK/Z,IAAEuB,WAAWwY,GACvBla,KAAKqlB,UAAYvb,EAAQmQ,WAAW0B,SACpC3b,KAAKF,QAAUgK,EAAQhK,QACvBE,KAAKysC,KAAOzsC,KAAKF,QAAQ2sC,MAAQ,GACjCzsC,KAAK0sC,UAAY1sC,KAAKF,QAAQ6sC,eAAiB,SAC/C3sC,KAAK4sC,MAAQrrC,MAAMC,QAAQxB,KAAKysC,MAAQzsC,KAAKysC,KAAO,CAACzsC,KAAKysC,MAE1DzsC,KAAKwZ,OAAS,CACZ,mBAAoB,SAAC8jB,EAAI7a,GAClBA,EAAE6Q,sBACL,EAAK8K,YAAY3b,IAGrB,qBAAsB,SAAC6a,EAAI7a,GACzB,EAAK4b,cAAc5b,IAErB,6DAA8D,WAC5D,EAAKlI,S,kEAMT,OAAOva,KAAK4sC,MAAMxrC,OAAS,I,mCAGhB,WACXpB,KAAKs+B,cAAgB,KACrBt+B,KAAK6sC,aAAe,KACpB7sC,KAAKopC,SAAWppC,KAAKka,GAAGivB,QAAQ,CAC9B7oC,UAAW,oBACXwsC,WAAW,EACXJ,UAAW,KACVvrC,SAASy0B,SAAS51B,KAAKF,QAAQqY,WAElCnY,KAAKopC,SAAS7uB,OACdva,KAAKqpC,SAAWrpC,KAAKopC,SAASpoC,KAAK,0CACnChB,KAAKqpC,SAASvoC,GAAG,QAAS,mBAAmB,SAAC2hB,GAC5C,EAAK4mB,SAASroC,KAAK,WAAWy4B,YAAY,UAC1Ct5B,IAAEsiB,EAAE+d,eAAejgC,SAAS,UAC5B,EAAKgU,aAGPvU,KAAKopC,SAAStoC,GAAG,aAAa,SAAC2hB,GAAQA,EAAEpG,sB,gCAIzCrc,KAAKopC,SAASzlC,W,iCAGLshC,GACTjlC,KAAKqpC,SAASroC,KAAK,WAAWy4B,YAAY,UAC1CwL,EAAM1kC,SAAS,UAEfP,KAAKqpC,SAAS,GAAGj9B,UAAY64B,EAAM,GAAG5kB,UAAargB,KAAKqpC,SAAS0D,cAAgB,I,iCAIjF,IAAMC,EAAWhtC,KAAKqpC,SAASroC,KAAK,0BAC9BisC,EAAQD,EAAS5+B,OAEvB,GAAI6+B,EAAM7rC,OACRpB,KAAKktC,WAAWD,OACX,CACL,IAAIE,EAAaH,EAASj7B,SAAS3D,OAE9B++B,EAAW/rC,SACd+rC,EAAantC,KAAKqpC,SAASroC,KAAK,oBAAoB8d,SAGtD9e,KAAKktC,WAAWC,EAAWnsC,KAAK,mBAAmB8d,Y,+BAKrD,IAAMkuB,EAAWhtC,KAAKqpC,SAASroC,KAAK,0BAC9BosC,EAAQJ,EAAS9+B,OAEvB,GAAIk/B,EAAMhsC,OACRpB,KAAKktC,WAAWE,OACX,CACL,IAAIC,EAAaL,EAASj7B,SAAS7D,OAE9Bm/B,EAAWjsC,SACdisC,EAAartC,KAAKqpC,SAASroC,KAAK,oBAAoB6M,QAGtD7N,KAAKktC,WAAWG,EAAWrsC,KAAK,mBAAmB6M,W,gCAKrD,IAAMo3B,EAAQjlC,KAAKqpC,SAASroC,KAAK,0BAEjC,GAAIikC,EAAM7jC,OAAQ,CAChB,IAAIsO,EAAO1P,KAAKstC,aAAarI,GAE7B,GAA0B,OAAtBjlC,KAAK6sC,cAAsD,IAA7B7sC,KAAK6sC,aAAazrC,OAClDpB,KAAKs+B,cAAcpf,GAAKlf,KAAKs+B,cAAclf,QAEtC,GAA0B,OAAtBpf,KAAK6sC,cAAyB7sC,KAAK6sC,aAAazrC,OAAS,IAAMpB,KAAKs+B,cAAcxd,cAAe,CAC1G,IAAIysB,EAAevtC,KAAKs+B,cAAclf,GAAKpf,KAAKs+B,cAAcpf,GAAKlf,KAAK6sC,aAAazrC,OACjFmsC,EAAe,IACjBvtC,KAAKs+B,cAAcpf,IAAMquB,GAK7B,GAFAvtC,KAAKs+B,cAAcpc,WAAWxS,GAEE,SAA5B1P,KAAKF,QAAQ0tC,WAAuB,CACtC,IAAI/4B,EAAQ1K,SAASyO,eAAe,IACpCrY,IAAEuP,GAAM2e,MAAM5Z,GACd6Q,GAAM5B,qBAAqBjP,GAAO9M,cAElC2d,GAAM3B,oBAAoBjU,GAAM/H,SAGlC3H,KAAKs+B,cAAgB,KACrBt+B,KAAKua,OACLva,KAAK8J,QAAQ2B,OAAO,mB,mCAIXw5B,GACX,IAAMwH,EAAOzsC,KAAK4sC,MAAM3H,EAAMzkC,KAAK,UAC7BgL,EAAOy5B,EAAMzkC,KAAK,QACpBkP,EAAO+8B,EAAK7S,QAAU6S,EAAK7S,QAAQpuB,GAAQA,EAI/C,MAHoB,iBAATkE,IACTA,EAAOoL,GAAIxC,WAAW5I,IAEjBA,I,0CAGW+9B,EAASlV,GAC3B,IAAMkU,EAAOzsC,KAAK4sC,MAAMa,GACxB,OAAOlV,EAAM3rB,KAAI,SAACpB,GAChB,IAAMy5B,EAAQ9kC,IAAE,iCAMhB,OALA8kC,EAAM5jC,OAAOorC,EAAK5K,SAAW4K,EAAK5K,SAASr2B,GAAQA,EAAO,IAC1Dy5B,EAAMzkC,KAAK,CACT,MAASitC,EACT,KAAQjiC,IAEHy5B,O,oCAIGxiB,GACPziB,KAAKopC,SAASnR,GAAG,cAIlBxV,EAAEwB,UAAY/kB,GAAI2b,KAAKuJ,OACzB3B,EAAEpG,iBACFrc,KAAKuU,WACIkO,EAAEwB,UAAY/kB,GAAI2b,KAAK4J,IAChChC,EAAEpG,iBACFrc,KAAK0tC,UACIjrB,EAAEwB,UAAY/kB,GAAI2b,KAAK8J,OAChClC,EAAEpG,iBACFrc,KAAK2tC,e,oCAIKprB,EAAOgc,EAASx+B,GAC5B,IAAM0sC,EAAOzsC,KAAK4sC,MAAMrqB,GACxB,GAAIkqB,GAAQA,EAAK5zB,MAAMrQ,KAAK+1B,IAAYkO,EAAKmB,OAAQ,CACnD,IAAMnlC,EAAUgkC,EAAK5zB,MAAMnQ,KAAK61B,GAChCv+B,KAAK6sC,aAAepkC,EAAQ,GAC5BgkC,EAAKmB,OAAOnlC,EAAQ,GAAI1I,QAExBA,M,kCAIQoO,EAAKowB,GAAS,WAClBuG,EAAS3kC,IAAE,+CAAiDgO,EAAM,YASxE,OARAnO,KAAK6tC,cAAc1/B,EAAKowB,GAAS,SAAChG,IAChCA,EAAQA,GAAS,IACPn3B,SACR0jC,EAAOzkC,KAAK,EAAKytC,oBAAoB3/B,EAAKoqB,IAC1C,EAAKtC,WAIF6O,I,kCAGGriB,GAAG,WACb,IAAKjd,EAAMwI,SAAS,CAAC9O,GAAI2b,KAAKuJ,MAAOllB,GAAI2b,KAAK4J,GAAIvlB,GAAI2b,KAAK8J,MAAOlC,EAAEwB,SAAU,CAC5E,IACI0a,EAAWJ,EADXjZ,EAAQtlB,KAAK8J,QAAQ2B,OAAO,uBAEhC,GAA8B,UAA1BzL,KAAKF,QAAQiuC,SAAsB,CAWrC,GAVApP,EAAYrZ,EAAM0oB,cAAc1oB,GAChCiZ,EAAUI,EAAUxc,WAEpBniB,KAAK4sC,MAAM3rC,SAAQ,SAACwrC,GAClB,GAAIA,EAAK5zB,MAAMrQ,KAAK+1B,GAElB,OADAI,EAAYrZ,EAAM2oB,mBAAmBxB,EAAK5zB,QACnC,MAIN8lB,EAEH,YADA3+B,KAAKua,OAIPgkB,EAAUI,EAAUxc,gBAEpBwc,EAAYrZ,EAAMsZ,eAClBL,EAAUI,EAAUxc,WAGtB,GAAIniB,KAAK4sC,MAAMxrC,QAAUm9B,EAAS,CAChCv+B,KAAKqpC,SAAS6E,QAEd,IAAMC,EAAMlhC,EAAKjB,SAASxG,EAAMqI,KAAK8wB,EAAUhc,mBACzC4mB,EAAkBppC,IAAEH,KAAKF,QAAQqY,WAAW7F,SAC9C67B,IACFA,EAAIhiC,KAAOo9B,EAAgBp9B,IAC3BgiC,EAAIloC,MAAQsjC,EAAgBtjC,KAE5BjG,KAAKopC,SAAS7uB,OACdva,KAAKs+B,cAAgBK,EACrB3+B,KAAK4sC,MAAM3rC,SAAQ,SAACwrC,EAAMt+B,GACpBs+B,EAAK5zB,MAAMrQ,KAAK+1B,IAClB,EAAK6P,YAAYjgC,EAAKowB,GAAS3I,SAAS,EAAKyT,aAIjDrpC,KAAKqpC,SAASroC,KAAK,yBAAyBT,SAAS,UAG9B,QAAnBP,KAAK0sC,UACP1sC,KAAKopC,SAASnjB,IAAI,CAChBhgB,KAAMkoC,EAAIloC,KACVkG,IAAKgiC,EAAIhiC,IAAMnM,KAAKopC,SAAS9vB,cAjPtB,IAoPTtZ,KAAKopC,SAASnjB,IAAI,CAChBhgB,KAAMkoC,EAAIloC,KACVkG,IAAKgiC,EAAIhiC,IAAMgiC,EAAIjsC,OAtPZ,UA2PblC,KAAKua,U,6BAMTva,KAAKopC,SAASnT,S,6BAIdj2B,KAAKopC,SAAS7uB,Y,kCC/OlBpa,IAAEuB,WAAavB,IAAEyB,OAAOzB,IAAEuB,WAAY,CACpC2sC,QAAS,SACTlzB,QAAS,GAETL,IAAKA,GACLwK,MAAOA,GACP9f,MAAOA,EAEP1F,QAAS,CACP4e,SAAUve,IAAEuB,WAAWC,KAAK,SAC5Bia,SAAS,EACT/d,QAAS,CACP,OAAU8xB,GACV,UAAawI,GACb,SAAYQ,GACZ,SAAY2V,GACZ,UAAavS,GACb,WAAcU,GACd,OAAUU,GAGV,YAAeqP,GACf,SAAYrO,GACZ,SAAYU,GACZ,YAAeC,GACf,YAAeS,GACf,QAAWI,GACX,QAAWsG,GACX,WAAcqB,GACd,YAAe4B,GACf,YAAeM,GACf,aAAgBY,GAChB,aAAgBE,GAChB,YAAeC,GACf,WAAcuB,GACd,WAAcK,IAGhBjxB,QAAS,GAETvZ,KAAM,QAEN2kC,kBAAkB,EAClBiI,gBAAiB,MACjB3H,eAAgB,GAGhB4H,oBAAoB,EACpB7R,QAAS,CACP,CAAC,QAAS,CAAC,UACX,CAAC,OAAQ,CAAC,OAAQ,YAAa,UAC/B,CAAC,WAAY,CAAC,aACd,CAAC,QAAS,CAAC,UACX,CAAC,OAAQ,CAAC,KAAM,KAAM,cACtB,CAAC,QAAS,CAAC,UACX,CAAC,SAAU,CAAC,OAAQ,UAAW,UAC/B,CAAC,OAAQ,CAAC,aAAc,WAAY,UAItC0N,YAAY,EACZlB,QAAS,CACP3mC,MAAO,CACL,CAAC,SAAU,CAAC,aAAc,aAAc,gBAAiB,eACzD,CAAC,QAAS,CAAC,YAAa,aAAc,cACtC,CAAC,SAAU,CAAC,iBAEdwB,KAAM,CACJ,CAAC,OAAQ,CAAC,iBAAkB,YAE9BM,MAAO,CACL,CAAC,MAAO,CAAC,aAAc,WAAY,aAAc,gBACjD,CAAC,SAAU,CAAC,YAAa,YAAa,iBAExCgoC,IAAK,CACH,CAAC,QAAS,CAAC,UACX,CAAC,OAAQ,CAAC,OAAQ,YAAa,UAC/B,CAAC,OAAQ,CAAC,KAAM,cAChB,CAAC,QAAS,CAAC,UACX,CAAC,SAAU,CAAC,OAAQ,YACpB,CAAC,OAAQ,CAAC,aAAc,eAK5B1Y,SAAS,EACTC,qBAAqB,EAErBxpB,MAAO,KACPnI,OAAQ,KACRw8B,iBAAiB,EACjBr6B,aAAa,EACb+tB,gBAAiB,UAEjBrT,OAAO,EACP0vB,aAAa,EACbxZ,QAAS,EACTH,cAAc,EACd9tB,WAAW,EACX0nC,kBAAkB,EAClB9vB,QAAS,OACTzG,UAAW,KACXuc,cAAe,EACfxL,wBAAyB,EACzBwK,YAAY,EACZC,gBAAgB,EAChBxa,YAAa,KACbqmB,oBAAoB,EAEpB/L,sBAAsB,EACtB9N,aAAc,IAGd8Y,2BAA2B,EAG3BsP,SAAU,OACVP,WAAY,QACZb,cAAe,SAEfhL,UAAW,CAAC,IAAK,aAAc,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,MAEpEW,UAAW,CACT,QAAS,cAAe,gBAAiB,cACzC,iBAAkB,YAAa,SAAU,gBACzC,SAAU,kBAAmB,WAE/BlC,qBAAsB,GACtB+B,iBAAiB,EAEjBO,UAAW,CAAC,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAE1DC,cAAe,CAAC,KAAM,MAGtB3B,OAAQ,CACN,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAC9E,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAC9E,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAC9E,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAC9E,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAC9E,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAC9E,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAC9E,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,YAIhFC,WAAY,CACV,CAAC,QAAS,UAAW,YAAa,YAAa,aAAc,UAAW,YAAa,SACrF,CAAC,MAAO,cAAe,SAAU,QAAS,OAAQ,OAAQ,kBAAmB,WAC7E,CAAC,SAAU,QAAS,YAAa,QAAS,aAAc,gBAAiB,UAAW,YACpF,CAAC,aAAc,eAAgB,eAAgB,SAAU,SAAU,SAAU,cAAe,eAC5F,CAAC,QAAS,QAAS,YAAa,UAAW,cAAe,SAAU,kBAAmB,QACvF,CAAC,gBAAiB,YAAa,eAAgB,mBAAoB,aAAc,cAAe,iBAAkB,YAClH,CAAC,UAAW,UAAW,cAAe,eAAgB,OAAQ,cAAe,YAAa,UAC1F,CAAC,WAAY,WAAY,QAAS,UAAW,QAAS,gBAAiB,YAAa,WAGtFP,YAAa,CACXjO,UAAW,UACXC,UAAW,WAGb8Q,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAE/D9T,eAAgB,uBAEhB+T,mBAAoB,CAClBC,IAAK,GACL9Y,IAAK,IAIP4c,eAAe,EACfQ,aAAa,EAEb3R,qBAAsB,KAEtBxa,UAAW,CACT8yB,gBAAiB,KACjBC,OAAQ,KACRC,eAAgB,KAChBC,SAAU,KACVC,iBAAkB,KAClBvG,cAAe,KACfwG,QAAS,KACTC,QAAS,KACTlF,kBAAmB,KACnBjT,cAAe,KACfoY,mBAAoB,KACpBC,OAAQ,KACRC,UAAW,KACXC,QAAS,KACTC,YAAa,KACbC,UAAW,KACXC,QAAS,KACTC,SAAU,MAGZvV,WAAY,CACVp7B,KAAM,YACN4wC,UAAU,EACVC,aAAa,GAGflV,gBAAgB,EAChBC,oBAAqB,0IACrBC,sBAAsB,EACtBE,2BAA4B,GAC5BC,+BAAgC,CAC9B,kBACA,2BACA,mBACA,UACA,gBACA,mBACA,sBACA,mBACA,YAGF7G,OAAQ,CACN2b,GAAI,CACF,IAAO,SACP,MAAS,kBACT,SAAU,OACV,SAAU,OACV,IAAO,MACP,YAAa,QACb,SAAU,OACV,SAAU,SACV,SAAU,YACV,eAAgB,gBAChB,iBAAkB,eAClB,eAAgB,cAChB,eAAgB,gBAChB,eAAgB,eAChB,eAAgB,cAChB,kBAAmB,sBACnB,kBAAmB,oBACnB,mBAAoB,UACpB,oBAAqB,SACrB,YAAa,aACb,YAAa,WACb,YAAa,WACb,YAAa,WACb,YAAa,WACb,YAAa,WACb,YAAa,WACb,aAAc,uBACd,SAAU,mBAGZC,IAAK,CACH,IAAO,SACP,MAAS,kBACT,QAAS,OACT,cAAe,OACf,IAAO,MACP,YAAa,QACb,QAAS,OACT,QAAS,SACT,QAAS,YACT,cAAe,gBACf,gBAAiB,eACjB,cAAe,cACf,cAAe,gBACf,cAAe,eACf,cAAe,cACf,iBAAkB,sBAClB,iBAAkB,oBAClB,kBAAmB,UACnB,mBAAoB,SACpB,WAAY,aACZ,WAAY,WACZ,WAAY,WACZ,WAAY,WACZ,WAAY,WACZ,WAAY,WACZ,WAAY,WACZ,YAAa,uBACb,QAAS,oBAGblxB,MAAO,CACL,MAAS,kBACT,YAAe,yBACf,aAAgB,0BAChB,UAAa,uBACb,WAAc,wBACd,SAAY,sBACZ,UAAa,uBACb,SAAY,sBACZ,SAAY,sBACZ,UAAa,uBACb,UAAa,uBACb,OAAU,yBACV,QAAW,0BACX,UAAa,uBACb,KAAQ,iBACR,MAAS,kBACT,OAAU,mBACV,MAAS,kBACT,KAAQ,iBACR,OAAU,mBACV,UAAa,uBACb,WAAc,wBACd,KAAQ,iBACR,MAAS,kBACT,OAAU,mBACV,KAAQ,iBACR,OAAU,yBACV,MAAS,kBACT,UAAa,uBACb,MAAS,kBACT,YAAe,wBACf,OAAU,mBACV,QAAW,oBACX,SAAY,qBACZ,KAAQ,iBACR,SAAY,qBACZ,OAAU,mBACV,cAAiB,0BACjB,UAAa,sBACb,YAAe,wBACf,MAAS,kBACT,WAAc,wBACd,MAAS,kBACT,UAAa,sBACb,KAAQ,iBACR,cAAiB,0BACjB,MAAS,uB,2TCrWf,IAAM1D,EAAS60B,IAAS7wC,OAAO,8CACzB09B,EAAUmT,IAAS7wC,OAAO,0DAC1Bo+B,EAAcyS,IAAS7wC,OAAO,oCAC9Byc,EAAUo0B,IAAS7wC,OAAO,0DAC1B0c,EAAWm0B,IAAS7wC,OAAO,uGAC3Bg9B,EAAY6T,IAAS7wC,OAAO,CAChC,gFACA,6CACE,mDACE,oCACA,oCACA,oCACF,SACF,UACA8N,KAAK,KAEDgjC,EAAYD,IAAS7wC,OAAO,4CAC5B+wC,EAAcF,IAAS7wC,OAAO,CAClC,gGACA,iFACA8N,KAAK,KAEDszB,EAAcyP,IAAS7wC,OAAO,0CAE9B2hC,EAAWkP,IAAS7wC,OAAO,8DAA8D,SAASiB,EAAOJ,GAC7G,IAAMF,EAAS2B,MAAMC,QAAQ1B,EAAQy4B,OAASz4B,EAAQy4B,MAAM3rB,KAAI,SAASpB,GACvE,IAAM5M,EAAyB,iBAAT4M,EAAqBA,EAAQA,EAAK5M,OAAS,GAC3Dg7B,EAAU95B,EAAQ+hC,SAAW/hC,EAAQ+hC,SAASr2B,GAAQA,EACtDykC,EAA0B,WAAhB,EAAOzkC,GAAqBA,EAAKykC,YAASx0B,EAI1D,MAAO,sCAFW,eAAiB7c,EAAQ,UACZ6c,IAAXw0B,EAAwB,iBAAmBA,EAAS,IAAM,KACL,gCAAkCrxC,EAAQ,KAAOg7B,EAAU,UACnI7sB,KAAK,IAAMjN,EAAQy4B,MAEtBr4B,EAAMG,KAAKT,GAAQgB,KAAK,CAAE,aAAcd,EAAQ8hC,QAE5C9hC,GAAWA,EAAQ0uC,oBACrBtuC,EAAMK,SAAS,yBAIbogC,EAAyB,SAASvgC,GACtC,OAAOA,GAGHmiC,EAAgBuN,IAAS7wC,OAAO,yEAAyE,SAASiB,EAAOJ,GAC7H,IAAMF,EAAS2B,MAAMC,QAAQ1B,EAAQy4B,OAASz4B,EAAQy4B,MAAM3rB,KAAI,SAASpB,GACvE,IAAM5M,EAAyB,iBAAT4M,EAAqBA,EAAQA,EAAK5M,OAAS,GAC3Dg7B,EAAU95B,EAAQ+hC,SAAW/hC,EAAQ+hC,SAASr2B,GAAQA,EAC5D,MAAO,iDAAmD5M,EAAQ,iCAAmC4M,EAAO,KAAO80B,EAAKxgC,EAAQ0iC,gBAAkB,IAAM5I,EAAU,UACjK7sB,KAAK,IAAMjN,EAAQy4B,MACtBr4B,EAAMG,KAAKT,GAAQgB,KAAK,CAAE,aAAcd,EAAQ8hC,QAE5C9hC,GAAWA,EAAQ0uC,oBACrBtuC,EAAMK,SAAS,yBAIbunC,EAASgI,IAAS7wC,OAAO,mFAAmF,SAASiB,EAAOJ,GAC5HA,EAAQioC,MACV7nC,EAAMK,SAAS,QAEjBL,EAAMU,KAAK,CACT,aAAcd,EAAQ8hC,QAExB1hC,EAAMG,KAAK,CACT,6BACE,8BACGP,EAAQ8hC,MAAQ,qDACc9hC,EAAQ8hC,MADtB,6HAGN,GACX,2BAA6B9hC,EAAQqd,KAAO,SAC3Crd,EAAQ8nC,OAAS,6BAA+B9nC,EAAQ8nC,OAAS,SAAW,GAC/E,SACF,UACA76B,KAAK,QAGHo8B,EAAU2G,IAAS7wC,OAAO,CAC9B,wCACE,4BACA,8DACF,UACA8N,KAAK,KAAK,SAAS7M,EAAOJ,GAC1B,IAAM4sC,OAAyC,IAAtB5sC,EAAQ4sC,UAA4B5sC,EAAQ4sC,UAAY,SAEjFxsC,EAAMK,SAASmsC,GAEX5sC,EAAQgtC,WACV5sC,EAAMc,KAAK,UAAUuZ,UAInBmtB,EAAWoI,IAAS7wC,OAAO,kCAAkC,SAASiB,EAAOJ,GACjFI,EAAMG,KAAK,CACT,mCAAqCP,EAAQiM,GAAK,cAAgBjM,EAAQiM,GAAK,IAAM,IAAM,IACzF,mDAAqDjM,EAAQiM,GAAK,aAAejM,EAAQiM,GAAK,IAAM,IACjGjM,EAAQ6nC,QAAU,WAAa,GAChC,iBAAmB7nC,EAAQyY,KAAOzY,EAAQyY,KAAO,IAAM,IACvD,mBAAqBzY,EAAQ6nC,QAAU,OAAS,SAAW,MAC7D,KAAO7nC,EAAQyY,KAAOzY,EAAQyY,KAAO,IACvC,YACAxL,KAAK,QAGHuzB,EAAO,SAAS4P,EAAehjB,GAEnC,MAAO,KADPA,EAAUA,GAAW,KACE,WAAagjB,EAAgB,OAAShjB,EAAQ,KAqJxDhT,EAlJJ,SAASi2B,GAClB,MAAO,CACLl1B,OAAQA,EACR0hB,QAASA,EACTU,YAAaA,EACb3hB,QAASA,EACTC,SAAUA,EACVsgB,UAAWA,EACX8T,UAAWA,EACXC,YAAaA,EACb3P,YAAaA,EACbO,SAAUA,EACVD,uBAAwBA,EACxB4B,cAAeA,EACfuF,OAAQA,EACRqB,QAASA,EACT7I,KAAMA,EACNoH,SAAUA,EACV5nC,QAASqwC,EAETpP,QAAS,SAAS7gC,EAAOJ,GACvB,OAAOgwC,IAAS7wC,OAAO,qCAAqC,SAASiB,EAAOJ,GAE1E,IADA,IAAMM,EAAW,GACRwqB,EAAM,EAAGwlB,EAAUtwC,EAAQkhC,OAAO5/B,OAAQwpB,EAAMwlB,EAASxlB,IAAO,CAKvE,IAJA,IAAM2J,EAAYz0B,EAAQy0B,UACpByM,EAASlhC,EAAQkhC,OAAOpW,GACxBqW,EAAanhC,EAAQmhC,WAAWrW,GAChC1P,EAAU,GACPwoB,EAAM,EAAG2M,EAAUrP,EAAO5/B,OAAQsiC,EAAM2M,EAAS3M,IAAO,CAC/D,IAAMr9B,EAAQ26B,EAAO0C,GACf4M,EAAYrP,EAAWyC,GAC7BxoB,EAAQ/L,KAAK,CACX,+CACA,2BAA4B9I,EAAO,KACnC,eAAgBkuB,EAAW,KAC3B,eAAgBluB,EAAO,KACvB,UAAWiqC,EAAW,KACtB,eAAgBA,EAAW,KAC3B,gDACAvjC,KAAK,KAET3M,EAAS+O,KAAK,+BAAiC+L,EAAQnO,KAAK,IAAM,UAEpE7M,EAAMG,KAAKD,EAAS2M,KAAK,KAErBjN,EAAQ8e,SACV1e,EAAMc,KAAK,mBAAmB4d,QAAQ,CACpCzG,UAAWrY,EAAQqY,WAAag4B,EAAch4B,UAC9C2D,QAAS,QACTy0B,UAAW,aA5BVT,CA+BJ5vC,EAAOJ,IAGZggC,OAAQ,SAAS5/B,EAAOJ,GACtB,OAAOgwC,IAAS7wC,OAAO,8EAA8E,SAASiB,EAAOJ,GAC/GA,GAAWA,EAAQ8e,SACrB1e,EAAMU,KAAK,CACTghC,MAAO9hC,EAAQ8e,QACf,aAAc9e,EAAQ8e,UACrBA,QAAQ,CACTzG,UAAWrY,EAAQqY,WAAag4B,EAAch4B,UAC9C2D,QAAS,QACTy0B,UAAW,WACVzvC,GAAG,SAAS,SAAC2hB,GACdtiB,IAAEsiB,EAAE+d,eAAe5hB,QAAQ,WAG3B9e,GAAWA,EAAQ0wC,gBACrBtwC,EAAMK,SAAS,wBAdZuvC,CAgBJ5vC,EAAOJ,IAGZunC,UAAW,SAASD,EAAMqJ,GACxBrJ,EAAKjU,YAAY,YAAasd,GAC9BrJ,EAAKxmC,KAAK,YAAa6vC,IAGzBpL,gBAAiB,SAAS+B,EAAMsJ,GAC9BtJ,EAAKjU,YAAY,SAAUud,IAG7BlI,cAAe,SAASX,EAAS7wB,GAC/B6wB,EAAQrS,IAAI,iBAAkBxe,IAGhC8xB,eAAgB,SAASjB,EAAS7wB,GAChC6wB,EAAQrS,IAAI,kBAAmBxe,IAGjCgyB,WAAY,SAASnB,GACnBA,EAAQ8I,MAAM,SAGhB1I,WAAY,SAASJ,GACnBA,EAAQ8I,MAAM,SAGhBt2B,aAAc,SAASN,GACrB,IAAM6V,GAAWugB,EAAcvc,QAAUmc,EAAU,CACjD1S,EAAY,CACV3hB,IACAs0B,QAEoC,WAAlCG,EAAc5B,gBAChBtzB,EAAO,CACPoiB,EAAY,CACV3hB,IACAC,MAEFghB,IACAV,MAEAhhB,EAAO,CACP0hB,IACAU,EAAY,CACV3hB,IACAC,MAEFsgB,OAED96B,SAIH,OAFAyuB,EAAQ/d,YAAYkI,GAEb,CACL8E,KAAM9E,EACNkB,OAAQ2U,EACR+M,QAAS/M,EAAQ5uB,KAAK,iBACtBq8B,YAAazN,EAAQ5uB,KAAK,sBAC1B2a,SAAUiU,EAAQ5uB,KAAK,kBACvB0a,QAASkU,EAAQ5uB,KAAK,iBACtBi7B,UAAWrM,EAAQ5uB,KAAK,qBAI5B0Z,aAAc,SAASX,EAAOE,GAC5BF,EAAM1Z,KAAK4Z,EAAW0B,SAAStb,QAC/B4Z,EAAWgB,OAAOtX,SAClBoW,EAAMkc,U,UC1PZ91B,IAAEuB,WAAavB,IAAEyB,OAAOzB,IAAEuB,WAAY,CACpCyY,YAAaD,EACb02B,UAAW,QAGbzwC,IAAEuB,WAAW5B,QAAQ6hC,UAAY,CAC/B,IACA,CAAEC,MAAO,aAAc7G,IAAK,aAAcz6B,UAAW,aAAc1B,MAAO,cAC1E,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM", "file": "summernote-bs4.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"jquery\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"jquery\"], factory);\n\telse {\n\t\tvar a = typeof exports === 'object' ? factory(require(\"jquery\")) : factory(root[\"jQuery\"]);\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(window, function(__WEBPACK_EXTERNAL_MODULE__0__) {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 53);\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__0__;", "import $ from 'jquery';\n\nclass Renderer {\n  constructor(markup, children, options, callback) {\n    this.markup = markup;\n    this.children = children;\n    this.options = options;\n    this.callback = callback;\n  }\n\n  render($parent) {\n    const $node = $(this.markup);\n\n    if (this.options && this.options.contents) {\n      $node.html(this.options.contents);\n    }\n\n    if (this.options && this.options.className) {\n      $node.addClass(this.options.className);\n    }\n\n    if (this.options && this.options.data) {\n      $.each(this.options.data, (k, v) => {\n        $node.attr('data-' + k, v);\n      });\n    }\n\n    if (this.options && this.options.click) {\n      $node.on('click', this.options.click);\n    }\n\n    if (this.children) {\n      const $container = $node.find('.note-children-container');\n      this.children.forEach((child) => {\n        child.render($container.length ? $container : $node);\n      });\n    }\n\n    if (this.callback) {\n      this.callback($node, this.options);\n    }\n\n    if (this.options && this.options.callback) {\n      this.options.callback($node);\n    }\n\n    if ($parent) {\n      $parent.append($node);\n    }\n\n    return $node;\n  }\n}\n\nexport default {\n  create: (markup, callback) => {\n    return function() {\n      const options = typeof arguments[1] === 'object' ? arguments[1] : arguments[0];\n      let children = Array.isArray(arguments[0]) ? arguments[0] : [];\n      if (options && options.children) {\n        children = options.children;\n      }\n      return new Renderer(markup, children, options, callback);\n    };\n  },\n};\n", "/* globals __webpack_amd_options__ */\nmodule.exports = __webpack_amd_options__;\n", "import $ from 'jquery';\n\n$.summernote = $.summernote || {\n  lang: {},\n};\n\n$.extend($.summernote.lang, {\n  'en-US': {\n    font: {\n      bold: 'Bold',\n      italic: 'Italic',\n      underline: 'Underline',\n      clear: 'Remove Font Style',\n      height: 'Line Height',\n      name: 'Font Family',\n      strikethrough: 'Strikethrough',\n      subscript: 'Subscript',\n      superscript: 'Superscript',\n      size: 'Font Size',\n      sizeunit: 'Font Size Unit',\n    },\n    image: {\n      image: 'Picture',\n      insert: 'Insert Image',\n      resizeFull: 'Resize full',\n      resizeHalf: 'Resize half',\n      resizeQuarter: 'Resize quarter',\n      resizeNone: 'Original size',\n      floatLeft: 'Float Left',\n      floatRight: 'Float Right',\n      floatNone: 'Remove float',\n      shapeRounded: 'Shape: Rounded',\n      shapeCircle: 'Shape: Circle',\n      shapeThumbnail: 'Shape: Thumbnail',\n      shapeNone: 'Shape: None',\n      dragImageHere: 'Drag image or text here',\n      dropImage: 'Drop image or Text',\n      selectFromFiles: 'Select from files',\n      maximumFileSize: 'Maximum file size',\n      maximumFileSizeError: 'Maximum file size exceeded.',\n      url: 'Image URL',\n      remove: 'Remove Image',\n      original: 'Original',\n    },\n    video: {\n      video: 'Video',\n      videoLink: 'Video Link',\n      insert: 'Insert Video',\n      url: 'Video URL',\n      providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n    },\n    link: {\n      link: 'Link',\n      insert: 'Insert Link',\n      unlink: 'Unlink',\n      edit: 'Edit',\n      textToDisplay: 'Text to display',\n      url: 'To what URL should this link go?',\n      openInNewWindow: 'Open in new window',\n      useProtocol: 'Use default protocol',\n    },\n    table: {\n      table: 'Table',\n      addRowAbove: 'Add row above',\n      addRowBelow: 'Add row below',\n      addColLeft: 'Add column left',\n      addColRight: 'Add column right',\n      delRow: 'Delete row',\n      delCol: 'Delete column',\n      delTable: 'Delete table',\n    },\n    hr: {\n      insert: 'Insert Horizontal Rule',\n    },\n    style: {\n      style: 'Style',\n      p: 'Normal',\n      blockquote: 'Quote',\n      pre: 'Code',\n      h1: 'Header 1',\n      h2: 'Header 2',\n      h3: 'Header 3',\n      h4: 'Header 4',\n      h5: 'Header 5',\n      h6: 'Header 6',\n    },\n    lists: {\n      unordered: 'Unordered list',\n      ordered: 'Ordered list',\n    },\n    options: {\n      help: 'Help',\n      fullscreen: 'Full Screen',\n      codeview: 'Code View',\n    },\n    paragraph: {\n      paragraph: 'Paragraph',\n      outdent: 'Outdent',\n      indent: 'Indent',\n      left: 'Align left',\n      center: 'Align center',\n      right: 'Align right',\n      justify: 'Justify full',\n    },\n    color: {\n      recent: 'Recent Color',\n      more: 'More Color',\n      background: 'Background Color',\n      foreground: 'Text Color',\n      transparent: 'Transparent',\n      setTransparent: 'Set transparent',\n      reset: 'Reset',\n      resetToDefault: 'Reset to default',\n      cpSelect: 'Select',\n    },\n    shortcut: {\n      shortcuts: 'Keyboard shortcuts',\n      close: 'Close',\n      textFormatting: 'Text formatting',\n      action: 'Action',\n      paragraphFormatting: 'Paragraph formatting',\n      documentStyle: 'Document Style',\n      extraKeys: 'Extra keys',\n    },\n    help: {\n      'escape': 'Escape',\n      'insertParagraph': 'Insert Paragraph',\n      'undo': 'Undo the last command',\n      'redo': 'Redo the last command',\n      'tab': 'Tab',\n      'untab': 'Untab',\n      'bold': 'Set a bold style',\n      'italic': 'Set a italic style',\n      'underline': 'Set a underline style',\n      'strikethrough': 'Set a strikethrough style',\n      'removeFormat': 'Clean a style',\n      'justifyLeft': 'Set left align',\n      'justifyCenter': 'Set center align',\n      'justifyRight': 'Set right align',\n      'justifyFull': 'Set full align',\n      'insertUnorderedList': 'Toggle unordered list',\n      'insertOrderedList': 'Toggle ordered list',\n      'outdent': 'Outdent on current paragraph',\n      'indent': 'Indent on current paragraph',\n      'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n      'formatH1': 'Change current block\\'s format as H1',\n      'formatH2': 'Change current block\\'s format as H2',\n      'formatH3': 'Change current block\\'s format as H3',\n      'formatH4': 'Change current block\\'s format as H4',\n      'formatH5': 'Change current block\\'s format as H5',\n      'formatH6': 'Change current block\\'s format as H6',\n      'insertHorizontalRule': 'Insert horizontal rule',\n      'linkDialog.show': 'Show Link Dialog',\n    },\n    history: {\n      undo: 'Undo',\n      redo: 'Redo',\n    },\n    specialChar: {\n      specialChar: 'SPECIAL CHARACTERS',\n      select: 'Select Special characters',\n    },\n    output: {\n      noSelection: 'No Selection Made!',\n    },\n  },\n});\n", "import $ from 'jquery';\nconst isSupportAmd = typeof define === 'function' && define.amd; // eslint-disable-line\n\n/**\n * returns whether font is installed or not.\n *\n * @param {String} fontName\n * @return {Boolean}\n */\nconst genericFontFamilies = ['sans-serif', 'serif', 'monospace', 'cursive', 'fantasy'];\n\nfunction validFontName(fontName) {\n  return ($.inArray(fontName.toLowerCase(), genericFontFamilies) === -1) ? `'${fontName}'` : fontName;\n}\n\nfunction isFontInstalled(fontName) {\n  const testFontName = fontName === 'Comic Sans MS' ? 'Courier New' : 'Comic Sans MS';\n  const testText = 'mmmmmmmmmmwwwww';\n  const testSize = '200px';\n\n  var canvas = document.createElement('canvas');\n  var context = canvas.getContext('2d');\n\n  context.font = testSize + \" '\" + testFontName + \"'\";\n  const originalWidth = context.measureText(testText).width;\n\n  context.font = testSize + ' ' + validFontName(fontName) + ', \"' + testFontName + '\"';\n  const width = context.measureText(testText).width;\n\n  return originalWidth !== width;\n}\n\nconst userAgent = navigator.userAgent;\nconst isMSIE = /MSIE|Trident/i.test(userAgent);\nlet browserVersion;\nif (isMSIE) {\n  let matches = /MSIE (\\d+[.]\\d+)/.exec(userAgent);\n  if (matches) {\n    browserVersion = parseFloat(matches[1]);\n  }\n  matches = /Trident\\/.*rv:([0-9]{1,}[.0-9]{0,})/.exec(userAgent);\n  if (matches) {\n    browserVersion = parseFloat(matches[1]);\n  }\n}\n\nconst isEdge = /Edge\\/\\d+/.test(userAgent);\n\nconst isSupportTouch =\n  (('ontouchstart' in window) ||\n   (navigator.MaxTouchPoints > 0) ||\n   (navigator.msMaxTouchPoints > 0));\n\n// [workaround] IE doesn't have input events for contentEditable\n// - see: https://goo.gl/4bfIvA\nconst inputEventName = (isMSIE) ? 'DOMCharacterDataModified DOMSubtreeModified DOMNodeInserted' : 'input';\n\n/**\n * @class core.env\n *\n * Object which check platform and agent\n *\n * @singleton\n * @alternateClassName env\n */\nexport default {\n  isMac: navigator.appVersion.indexOf('Mac') > -1,\n  isMSIE,\n  isEdge,\n  isFF: !isEdge && /firefox/i.test(userAgent),\n  isPhantom: /PhantomJS/i.test(userAgent),\n  isWebkit: !isEdge && /webkit/i.test(userAgent),\n  isChrome: !isEdge && /chrome/i.test(userAgent),\n  isSafari: !isEdge && /safari/i.test(userAgent) && (!/chrome/i.test(userAgent)),\n  browserVersion,\n  jqueryVersion: parseFloat($.fn.jquery),\n  isSupportAmd,\n  isSupportTouch,\n  isFontInstalled,\n  isW3CRangeSupport: !!document.createRange,\n  inputEventName,\n  genericFontFamilies,\n  validFontName,\n};\n", "import $ from 'jquery';\n\n/**\n * @class core.func\n *\n * func utils (for high-order func's arg)\n *\n * @singleton\n * @alternateClassName func\n */\nfunction eq(itemA) {\n  return function(itemB) {\n    return itemA === itemB;\n  };\n}\n\nfunction eq2(itemA, itemB) {\n  return itemA === itemB;\n}\n\nfunction peq2(propName) {\n  return function(itemA, itemB) {\n    return itemA[propName] === itemB[propName];\n  };\n}\n\nfunction ok() {\n  return true;\n}\n\nfunction fail() {\n  return false;\n}\n\nfunction not(f) {\n  return function() {\n    return !f.apply(f, arguments);\n  };\n}\n\nfunction and(fA, fB) {\n  return function(item) {\n    return fA(item) && fB(item);\n  };\n}\n\nfunction self(a) {\n  return a;\n}\n\nfunction invoke(obj, method) {\n  return function() {\n    return obj[method].apply(obj, arguments);\n  };\n}\n\nlet idCounter = 0;\n\n/**\n * reset globally-unique id\n *\n */\nfunction resetUniqueId() {\n  idCounter = 0;\n}\n\n/**\n * generate a globally-unique id\n *\n * @param {String} [prefix]\n */\nfunction uniqueId(prefix) {\n  const id = ++idCounter + '';\n  return prefix ? prefix + id : id;\n}\n\n/**\n * returns bnd (bounds) from rect\n *\n * - IE Compatibility Issue: http://goo.gl/sRLOAo\n * - Scroll Issue: http://goo.gl/sNjUc\n *\n * @param {Rect} rect\n * @return {Object} bounds\n * @return {Number} bounds.top\n * @return {Number} bounds.left\n * @return {Number} bounds.width\n * @return {Number} bounds.height\n */\nfunction rect2bnd(rect) {\n  const $document = $(document);\n  return {\n    top: rect.top + $document.scrollTop(),\n    left: rect.left + $document.scrollLeft(),\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n}\n\n/**\n * returns a copy of the object where the keys have become the values and the values the keys.\n * @param {Object} obj\n * @return {Object}\n */\nfunction invertObject(obj) {\n  const inverted = {};\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      inverted[obj[key]] = key;\n    }\n  }\n  return inverted;\n}\n\n/**\n * @param {String} namespace\n * @param {String} [prefix]\n * @return {String}\n */\nfunction namespaceToCamel(namespace, prefix) {\n  prefix = prefix || '';\n  return prefix + namespace.split('.').map(function(name) {\n    return name.substring(0, 1).toUpperCase() + name.substring(1);\n  }).join('');\n}\n\n/**\n * Returns a function, that, as long as it continues to be invoked, will not\n * be triggered. The function will be called after it stops being called for\n * N milliseconds. If `immediate` is passed, trigger the function on the\n * leading edge, instead of the trailing.\n * @param {Function} func\n * @param {Number} wait\n * @param {Boolean} immediate\n * @return {Function}\n */\nfunction debounce(func, wait, immediate) {\n  let timeout;\n  return function() {\n    const context = this;\n    const args = arguments;\n    const later = () => {\n      timeout = null;\n      if (!immediate) {\n        func.apply(context, args);\n      }\n    };\n    const callNow = immediate && !timeout;\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n    if (callNow) {\n      func.apply(context, args);\n    }\n  };\n}\n\n/**\n *\n * @param {String} url\n * @return {Boolean}\n */\nfunction isValidUrl(url) {\n  const expression = /[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)/gi;\n  return expression.test(url);\n}\n\nexport default {\n  eq,\n  eq2,\n  peq2,\n  ok,\n  fail,\n  self,\n  not,\n  and,\n  invoke,\n  resetUniqueId,\n  uniqueId,\n  rect2bnd,\n  invertObject,\n  namespaceToCamel,\n  debounce,\n  isValidUrl,\n};\n", "import func from './func';\n\n/**\n * returns the first item of an array.\n *\n * @param {Array} array\n */\nfunction head(array) {\n  return array[0];\n}\n\n/**\n * returns the last item of an array.\n *\n * @param {Array} array\n */\nfunction last(array) {\n  return array[array.length - 1];\n}\n\n/**\n * returns everything but the last entry of the array.\n *\n * @param {Array} array\n */\nfunction initial(array) {\n  return array.slice(0, array.length - 1);\n}\n\n/**\n * returns the rest of the items in an array.\n *\n * @param {Array} array\n */\nfunction tail(array) {\n  return array.slice(1);\n}\n\n/**\n * returns item of array\n */\nfunction find(array, pred) {\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    const item = array[idx];\n    if (pred(item)) {\n      return item;\n    }\n  }\n}\n\n/**\n * returns true if all of the values in the array pass the predicate truth test.\n */\nfunction all(array, pred) {\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    if (!pred(array[idx])) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * returns true if the value is present in the list.\n */\nfunction contains(array, item) {\n  if (array && array.length && item) {\n    if (array.indexOf) {\n      return array.indexOf(item) !== -1;\n    } else if (array.contains) {\n      // `DOMTokenList` doesn't implement `.indexOf`, but it implements `.contains`\n      return array.contains(item);\n    }\n  }\n  return false;\n}\n\n/**\n * get sum from a list\n *\n * @param {Array} array - array\n * @param {Function} fn - iterator\n */\nfunction sum(array, fn) {\n  fn = fn || func.self;\n  return array.reduce(function(memo, v) {\n    return memo + fn(v);\n  }, 0);\n}\n\n/**\n * returns a copy of the collection with array type.\n * @param {Collection} collection - collection eg) node.childNodes, ...\n */\nfunction from(collection) {\n  const result = [];\n  const length = collection.length;\n  let idx = -1;\n  while (++idx < length) {\n    result[idx] = collection[idx];\n  }\n  return result;\n}\n\n/**\n * returns whether list is empty or not\n */\nfunction isEmpty(array) {\n  return !array || !array.length;\n}\n\n/**\n * cluster elements by predicate function.\n *\n * @param {Array} array - array\n * @param {Function} fn - predicate function for cluster rule\n * @param {Array[]}\n */\nfunction clusterBy(array, fn) {\n  if (!array.length) { return []; }\n  const aTail = tail(array);\n  return aTail.reduce(function(memo, v) {\n    const aLast = last(memo);\n    if (fn(last(aLast), v)) {\n      aLast[aLast.length] = v;\n    } else {\n      memo[memo.length] = [v];\n    }\n    return memo;\n  }, [[head(array)]]);\n}\n\n/**\n * returns a copy of the array with all false values removed\n *\n * @param {Array} array - array\n * @param {Function} fn - predicate function for cluster rule\n */\nfunction compact(array) {\n  const aResult = [];\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    if (array[idx]) { aResult.push(array[idx]); }\n  }\n  return aResult;\n}\n\n/**\n * produces a duplicate-free version of the array\n *\n * @param {Array} array\n */\nfunction unique(array) {\n  const results = [];\n\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    if (!contains(results, array[idx])) {\n      results.push(array[idx]);\n    }\n  }\n\n  return results;\n}\n\n/**\n * returns next item.\n * @param {Array} array\n */\nfunction next(array, item) {\n  if (array && array.length && item) {\n    const idx = array.indexOf(item);\n    return idx === -1 ? null : array[idx + 1];\n  }\n  return null;\n}\n\n/**\n * returns prev item.\n * @param {Array} array\n */\nfunction prev(array, item) {\n  if (array && array.length && item) {\n    const idx = array.indexOf(item);\n    return idx === -1 ? null : array[idx - 1];\n  }\n  return null;\n}\n\n/**\n * @class core.list\n *\n * list utils\n *\n * @singleton\n * @alternateClassName list\n */\nexport default {\n  head,\n  last,\n  initial,\n  tail,\n  prev,\n  next,\n  find,\n  contains,\n  all,\n  sum,\n  from,\n  isEmpty,\n  clusterBy,\n  compact,\n  unique,\n};\n", "import $ from 'jquery';\nimport func from './func';\nimport lists from './lists';\nimport env from './env';\n\nconst NBSP_CHAR = String.fromCharCode(160);\nconst ZERO_WIDTH_NBSP_CHAR = '\\ufeff';\n\n/**\n * @method isEditable\n *\n * returns whether node is `note-editable` or not.\n *\n * @param {Node} node\n * @return {Boolean}\n */\nfunction isEditable(node) {\n  return node && $(node).hasClass('note-editable');\n}\n\n/**\n * @method isControlSizing\n *\n * returns whether node is `note-control-sizing` or not.\n *\n * @param {Node} node\n * @return {Boolean}\n */\nfunction isControlSizing(node) {\n  return node && $(node).hasClass('note-control-sizing');\n}\n\n/**\n * @method makePredByNodeName\n *\n * returns predicate which judge whether nodeName is same\n *\n * @param {String} nodeName\n * @return {Function}\n */\nfunction makePredByNodeName(nodeName) {\n  nodeName = nodeName.toUpperCase();\n  return function(node) {\n    return node && node.nodeName.toUpperCase() === nodeName;\n  };\n}\n\n/**\n * @method isText\n *\n *\n *\n * @param {Node} node\n * @return {Boolean} true if node's type is text(3)\n */\nfunction isText(node) {\n  return node && node.nodeType === 3;\n}\n\n/**\n * @method isElement\n *\n *\n *\n * @param {Node} node\n * @return {Boolean} true if node's type is element(1)\n */\nfunction isElement(node) {\n  return node && node.nodeType === 1;\n}\n\n/**\n * ex) br, col, embed, hr, img, input, ...\n * @see http://www.w3.org/html/wg/drafts/html/master/syntax.html#void-elements\n */\nfunction isVoid(node) {\n  return node && /^BR|^IMG|^HR|^IFRAME|^BUTTON|^INPUT|^AUDIO|^VIDEO|^EMBED/.test(node.nodeName.toUpperCase());\n}\n\nfunction isPara(node) {\n  if (isEditable(node)) {\n    return false;\n  }\n\n  // Chrome(v31.0), FF(v25.0.1) use DIV for paragraph\n  return node && /^DIV|^P|^LI|^H[1-7]/.test(node.nodeName.toUpperCase());\n}\n\nfunction isHeading(node) {\n  return node && /^H[1-7]/.test(node.nodeName.toUpperCase());\n}\n\nconst isPre = makePredByNodeName('PRE');\n\nconst isLi = makePredByNodeName('LI');\n\nfunction isPurePara(node) {\n  return isPara(node) && !isLi(node);\n}\n\nconst isTable = makePredByNodeName('TABLE');\n\nconst isData = makePredByNodeName('DATA');\n\nfunction isInline(node) {\n  return !isBodyContainer(node) &&\n         !isList(node) &&\n         !isHr(node) &&\n         !isPara(node) &&\n         !isTable(node) &&\n         !isBlockquote(node) &&\n         !isData(node);\n}\n\nfunction isList(node) {\n  return node && /^UL|^OL/.test(node.nodeName.toUpperCase());\n}\n\nconst isHr = makePredByNodeName('HR');\n\nfunction isCell(node) {\n  return node && /^TD|^TH/.test(node.nodeName.toUpperCase());\n}\n\nconst isBlockquote = makePredByNodeName('BLOCKQUOTE');\n\nfunction isBodyContainer(node) {\n  return isCell(node) || isBlockquote(node) || isEditable(node);\n}\n\nconst isAnchor = makePredByNodeName('A');\n\nfunction isParaInline(node) {\n  return isInline(node) && !!ancestor(node, isPara);\n}\n\nfunction isBodyInline(node) {\n  return isInline(node) && !ancestor(node, isPara);\n}\n\nconst isBody = makePredByNodeName('BODY');\n\n/**\n * returns whether nodeB is closest sibling of nodeA\n *\n * @param {Node} nodeA\n * @param {Node} nodeB\n * @return {Boolean}\n */\nfunction isClosestSibling(nodeA, nodeB) {\n  return nodeA.nextSibling === nodeB ||\n         nodeA.previousSibling === nodeB;\n}\n\n/**\n * returns array of closest siblings with node\n *\n * @param {Node} node\n * @param {function} [pred] - predicate function\n * @return {Node[]}\n */\nfunction withClosestSiblings(node, pred) {\n  pred = pred || func.ok;\n\n  const siblings = [];\n  if (node.previousSibling && pred(node.previousSibling)) {\n    siblings.push(node.previousSibling);\n  }\n  siblings.push(node);\n  if (node.nextSibling && pred(node.nextSibling)) {\n    siblings.push(node.nextSibling);\n  }\n  return siblings;\n}\n\n/**\n * blank HTML for cursor position\n * - [workaround] old IE only works with &nbsp;\n * - [workaround] IE11 and other browser works with bogus br\n */\nconst blankHTML = env.isMSIE && env.browserVersion < 11 ? '&nbsp;' : '<br>';\n\n/**\n * @method nodeLength\n *\n * returns #text's text size or element's childNodes size\n *\n * @param {Node} node\n */\nfunction nodeLength(node) {\n  if (isText(node)) {\n    return node.nodeValue.length;\n  }\n\n  if (node) {\n    return node.childNodes.length;\n  }\n\n  return 0;\n}\n\n/**\n * returns whether deepest child node is empty or not.\n *\n * @param {Node} node\n * @return {Boolean}\n */\nfunction deepestChildIsEmpty(node) {\n  do {\n    if (node.firstElementChild === null || node.firstElementChild.innerHTML === '') break;\n  } while ((node = node.firstElementChild));\n\n  return isEmpty(node);\n}\n\n/**\n * returns whether node is empty or not.\n *\n * @param {Node} node\n * @return {Boolean}\n */\nfunction isEmpty(node) {\n  const len = nodeLength(node);\n\n  if (len === 0) {\n    return true;\n  } else if (!isText(node) && len === 1 && node.innerHTML === blankHTML) {\n    // ex) <p><br></p>, <span><br></span>\n    return true;\n  } else if (lists.all(node.childNodes, isText) && node.innerHTML === '') {\n    // ex) <p></p>, <span></span>\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * padding blankHTML if node is empty (for cursor position)\n */\nfunction paddingBlankHTML(node) {\n  if (!isVoid(node) && !nodeLength(node)) {\n    node.innerHTML = blankHTML;\n  }\n}\n\n/**\n * find nearest ancestor predicate hit\n *\n * @param {Node} node\n * @param {Function} pred - predicate function\n */\nfunction ancestor(node, pred) {\n  while (node) {\n    if (pred(node)) { return node; }\n    if (isEditable(node)) { break; }\n\n    node = node.parentNode;\n  }\n  return null;\n}\n\n/**\n * find nearest ancestor only single child blood line and predicate hit\n *\n * @param {Node} node\n * @param {Function} pred - predicate function\n */\nfunction singleChildAncestor(node, pred) {\n  node = node.parentNode;\n\n  while (node) {\n    if (nodeLength(node) !== 1) { break; }\n    if (pred(node)) { return node; }\n    if (isEditable(node)) { break; }\n\n    node = node.parentNode;\n  }\n  return null;\n}\n\n/**\n * returns new array of ancestor nodes (until predicate hit).\n *\n * @param {Node} node\n * @param {Function} [optional] pred - predicate function\n */\nfunction listAncestor(node, pred) {\n  pred = pred || func.fail;\n\n  const ancestors = [];\n  ancestor(node, function(el) {\n    if (!isEditable(el)) {\n      ancestors.push(el);\n    }\n\n    return pred(el);\n  });\n  return ancestors;\n}\n\n/**\n * find farthest ancestor predicate hit\n */\nfunction lastAncestor(node, pred) {\n  const ancestors = listAncestor(node);\n  return lists.last(ancestors.filter(pred));\n}\n\n/**\n * returns common ancestor node between two nodes.\n *\n * @param {Node} nodeA\n * @param {Node} nodeB\n */\nfunction commonAncestor(nodeA, nodeB) {\n  const ancestors = listAncestor(nodeA);\n  for (let n = nodeB; n; n = n.parentNode) {\n    if (ancestors.indexOf(n) > -1) return n;\n  }\n  return null; // difference document area\n}\n\n/**\n * listing all previous siblings (until predicate hit).\n *\n * @param {Node} node\n * @param {Function} [optional] pred - predicate function\n */\nfunction listPrev(node, pred) {\n  pred = pred || func.fail;\n\n  const nodes = [];\n  while (node) {\n    if (pred(node)) { break; }\n    nodes.push(node);\n    node = node.previousSibling;\n  }\n  return nodes;\n}\n\n/**\n * listing next siblings (until predicate hit).\n *\n * @param {Node} node\n * @param {Function} [pred] - predicate function\n */\nfunction listNext(node, pred) {\n  pred = pred || func.fail;\n\n  const nodes = [];\n  while (node) {\n    if (pred(node)) { break; }\n    nodes.push(node);\n    node = node.nextSibling;\n  }\n  return nodes;\n}\n\n/**\n * listing descendant nodes\n *\n * @param {Node} node\n * @param {Function} [pred] - predicate function\n */\nfunction listDescendant(node, pred) {\n  const descendants = [];\n  pred = pred || func.ok;\n\n  // start DFS(depth first search) with node\n  (function fnWalk(current) {\n    if (node !== current && pred(current)) {\n      descendants.push(current);\n    }\n    for (let idx = 0, len = current.childNodes.length; idx < len; idx++) {\n      fnWalk(current.childNodes[idx]);\n    }\n  })(node);\n\n  return descendants;\n}\n\n/**\n * wrap node with new tag.\n *\n * @param {Node} node\n * @param {Node} tagName of wrapper\n * @return {Node} - wrapper\n */\nfunction wrap(node, wrapperName) {\n  const parent = node.parentNode;\n  const wrapper = $('<' + wrapperName + '>')[0];\n\n  parent.insertBefore(wrapper, node);\n  wrapper.appendChild(node);\n\n  return wrapper;\n}\n\n/**\n * insert node after preceding\n *\n * @param {Node} node\n * @param {Node} preceding - predicate function\n */\nfunction insertAfter(node, preceding) {\n  const next = preceding.nextSibling;\n  let parent = preceding.parentNode;\n  if (next) {\n    parent.insertBefore(node, next);\n  } else {\n    parent.appendChild(node);\n  }\n  return node;\n}\n\n/**\n * append elements.\n *\n * @param {Node} node\n * @param {Collection} aChild\n */\nfunction appendChildNodes(node, aChild) {\n  $.each(aChild, function(idx, child) {\n    node.appendChild(child);\n  });\n  return node;\n}\n\n/**\n * returns whether boundaryPoint is left edge or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isLeftEdgePoint(point) {\n  return point.offset === 0;\n}\n\n/**\n * returns whether boundaryPoint is right edge or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isRightEdgePoint(point) {\n  return point.offset === nodeLength(point.node);\n}\n\n/**\n * returns whether boundaryPoint is edge or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isEdgePoint(point) {\n  return isLeftEdgePoint(point) || isRightEdgePoint(point);\n}\n\n/**\n * returns whether node is left edge of ancestor or not.\n *\n * @param {Node} node\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isLeftEdgeOf(node, ancestor) {\n  while (node && node !== ancestor) {\n    if (position(node) !== 0) {\n      return false;\n    }\n    node = node.parentNode;\n  }\n\n  return true;\n}\n\n/**\n * returns whether node is right edge of ancestor or not.\n *\n * @param {Node} node\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isRightEdgeOf(node, ancestor) {\n  if (!ancestor) {\n    return false;\n  }\n  while (node && node !== ancestor) {\n    if (position(node) !== nodeLength(node.parentNode) - 1) {\n      return false;\n    }\n    node = node.parentNode;\n  }\n\n  return true;\n}\n\n/**\n * returns whether point is left edge of ancestor or not.\n * @param {BoundaryPoint} point\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isLeftEdgePointOf(point, ancestor) {\n  return isLeftEdgePoint(point) && isLeftEdgeOf(point.node, ancestor);\n}\n\n/**\n * returns whether point is right edge of ancestor or not.\n * @param {BoundaryPoint} point\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isRightEdgePointOf(point, ancestor) {\n  return isRightEdgePoint(point) && isRightEdgeOf(point.node, ancestor);\n}\n\n/**\n * returns offset from parent.\n *\n * @param {Node} node\n */\nfunction position(node) {\n  let offset = 0;\n  while ((node = node.previousSibling)) {\n    offset += 1;\n  }\n  return offset;\n}\n\nfunction hasChildren(node) {\n  return !!(node && node.childNodes && node.childNodes.length);\n}\n\n/**\n * returns previous boundaryPoint\n *\n * @param {BoundaryPoint} point\n * @param {Boolean} isSkipInnerOffset\n * @return {BoundaryPoint}\n */\nfunction prevPoint(point, isSkipInnerOffset) {\n  let node;\n  let offset;\n\n  if (point.offset === 0) {\n    if (isEditable(point.node)) {\n      return null;\n    }\n\n    node = point.node.parentNode;\n    offset = position(point.node);\n  } else if (hasChildren(point.node)) {\n    node = point.node.childNodes[point.offset - 1];\n    offset = nodeLength(node);\n  } else {\n    node = point.node;\n    offset = isSkipInnerOffset ? 0 : point.offset - 1;\n  }\n\n  return {\n    node: node,\n    offset: offset,\n  };\n}\n\n/**\n * returns next boundaryPoint\n *\n * @param {BoundaryPoint} point\n * @param {Boolean} isSkipInnerOffset\n * @return {BoundaryPoint}\n */\nfunction nextPoint(point, isSkipInnerOffset) {\n  let node, offset;\n\n  if (nodeLength(point.node) === point.offset) {\n    if (isEditable(point.node)) {\n      return null;\n    }\n\n    let nextTextNode = getNextTextNode(point.node);\n    if (nextTextNode) {\n      node = nextTextNode;\n      offset = 0;\n    } else {\n      node = point.node.parentNode;\n      offset = position(point.node) + 1;\n    }\n  } else if (hasChildren(point.node)) {\n    node = point.node.childNodes[point.offset];\n    offset = 0;\n  } else {\n    node = point.node;\n    offset = isSkipInnerOffset ? nodeLength(point.node) : point.offset + 1;\n  }\n\n  return {\n    node: node,\n    offset: offset,\n  };\n}\n\n/**\n * returns next boundaryPoint with empty node\n *\n * @param {BoundaryPoint} point\n * @param {Boolean} isSkipInnerOffset\n * @return {BoundaryPoint}\n */\nfunction nextPointWithEmptyNode(point, isSkipInnerOffset) {\n  let node, offset;\n\n  // if node is empty string node, return current node's sibling.\n  if (isEmpty(point.node)) {\n    node = point.node.nextSibling;\n    offset = 0;\n\n    return {\n      node: node,\n      offset: offset,\n    };\n  }\n\n  if (nodeLength(point.node) === point.offset) {\n    if (isEditable(point.node)) {\n      return null;\n    }\n\n    let nextTextNode = getNextTextNode(point.node);\n    if (nextTextNode) {\n      node = nextTextNode;\n      offset = 0;\n    } else {\n      node = point.node.parentNode;\n      offset = position(point.node) + 1;\n    }\n\n    // if next node is editable, return current node's sibling node.\n    if (isEditable(node)) {\n      node = point.node.nextSibling;\n      offset = 0;\n    }\n  } else if (hasChildren(point.node)) {\n    node = point.node.childNodes[point.offset];\n    offset = 0;\n    if (isEmpty(node)) {\n      return null;\n    }\n  } else {\n    node = point.node;\n    offset = isSkipInnerOffset ? nodeLength(point.node) : point.offset + 1;\n\n    if (isEmpty(node)) {\n      return null;\n    }\n  }\n\n  return {\n    node: node,\n    offset: offset,\n  };\n}\n\n/*\n* returns the next Text node index or 0 if not found.\n*/\nfunction getNextTextNode(actual) {\n  if (!actual.nextSibling) return undefined;\n  if (actual.parent !== actual.nextSibling.parent) return undefined;\n  if (isText(actual.nextSibling)) return actual.nextSibling;\n  return getNextTextNode(actual.nextSibling);\n}\n\n/**\n * returns whether pointA and pointB is same or not.\n *\n * @param {BoundaryPoint} pointA\n * @param {BoundaryPoint} pointB\n * @return {Boolean}\n */\nfunction isSamePoint(pointA, pointB) {\n  return pointA.node === pointB.node && pointA.offset === pointB.offset;\n}\n\n/**\n * returns whether point is visible (can set cursor) or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isVisiblePoint(point) {\n  if (isText(point.node) || !hasChildren(point.node) || isEmpty(point.node)) {\n    return true;\n  }\n\n  const leftNode = point.node.childNodes[point.offset - 1];\n  const rightNode = point.node.childNodes[point.offset];\n  if ((!leftNode || isVoid(leftNode)) && (!rightNode || isVoid(rightNode))) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * @method prevPointUtil\n *\n * @param {BoundaryPoint} point\n * @param {Function} pred\n * @return {BoundaryPoint}\n */\nfunction prevPointUntil(point, pred) {\n  while (point) {\n    if (pred(point)) {\n      return point;\n    }\n\n    point = prevPoint(point);\n  }\n\n  return null;\n}\n\n/**\n * @method nextPointUntil\n *\n * @param {BoundaryPoint} point\n * @param {Function} pred\n * @return {BoundaryPoint}\n */\nfunction nextPointUntil(point, pred) {\n  while (point) {\n    if (pred(point)) {\n      return point;\n    }\n\n    point = nextPoint(point);\n  }\n\n  return null;\n}\n\n/**\n * returns whether point has character or not.\n *\n * @param {Point} point\n * @return {Boolean}\n */\nfunction isCharPoint(point) {\n  if (!isText(point.node)) {\n    return false;\n  }\n\n  const ch = point.node.nodeValue.charAt(point.offset - 1);\n  return ch && (ch !== ' ' && ch !== NBSP_CHAR);\n}\n\n/**\n * returns whether point has space or not.\n *\n * @param {Point} point\n * @return {Boolean}\n */\nfunction isSpacePoint(point) {\n  if (!isText(point.node)) {\n    return false;\n  }\n\n  const ch = point.node.nodeValue.charAt(point.offset - 1);\n  return ch === ' ' || ch === NBSP_CHAR;\n}\n\n/**\n * @method walkPoint\n *\n * @param {BoundaryPoint} startPoint\n * @param {BoundaryPoint} endPoint\n * @param {Function} handler\n * @param {Boolean} isSkipInnerOffset\n */\nfunction walkPoint(startPoint, endPoint, handler, isSkipInnerOffset) {\n  let point = startPoint;\n\n  while (point) {\n    handler(point);\n\n    if (isSamePoint(point, endPoint)) {\n      break;\n    }\n\n    const isSkipOffset = isSkipInnerOffset &&\n                       startPoint.node !== point.node &&\n                       endPoint.node !== point.node;\n    point = nextPointWithEmptyNode(point, isSkipOffset);\n  }\n}\n\n/**\n * @method makeOffsetPath\n *\n * return offsetPath(array of offset) from ancestor\n *\n * @param {Node} ancestor - ancestor node\n * @param {Node} node\n */\nfunction makeOffsetPath(ancestor, node) {\n  const ancestors = listAncestor(node, func.eq(ancestor));\n  return ancestors.map(position).reverse();\n}\n\n/**\n * @method fromOffsetPath\n *\n * return element from offsetPath(array of offset)\n *\n * @param {Node} ancestor - ancestor node\n * @param {array} offsets - offsetPath\n */\nfunction fromOffsetPath(ancestor, offsets) {\n  let current = ancestor;\n  for (let i = 0, len = offsets.length; i < len; i++) {\n    if (current.childNodes.length <= offsets[i]) {\n      current = current.childNodes[current.childNodes.length - 1];\n    } else {\n      current = current.childNodes[offsets[i]];\n    }\n  }\n  return current;\n}\n\n/**\n * @method splitNode\n *\n * split element or #text\n *\n * @param {BoundaryPoint} point\n * @param {Object} [options]\n * @param {Boolean} [options.isSkipPaddingBlankHTML] - default: false\n * @param {Boolean} [options.isNotSplitEdgePoint] - default: false\n * @param {Boolean} [options.isDiscardEmptySplits] - default: false\n * @return {Node} right node of boundaryPoint\n */\nfunction splitNode(point, options) {\n  let isSkipPaddingBlankHTML = options && options.isSkipPaddingBlankHTML;\n  const isNotSplitEdgePoint = options && options.isNotSplitEdgePoint;\n  const isDiscardEmptySplits = options && options.isDiscardEmptySplits;\n\n  if (isDiscardEmptySplits) {\n    isSkipPaddingBlankHTML = true;\n  }\n\n  // edge case\n  if (isEdgePoint(point) && (isText(point.node) || isNotSplitEdgePoint)) {\n    if (isLeftEdgePoint(point)) {\n      return point.node;\n    } else if (isRightEdgePoint(point)) {\n      return point.node.nextSibling;\n    }\n  }\n\n  // split #text\n  if (isText(point.node)) {\n    return point.node.splitText(point.offset);\n  } else {\n    const childNode = point.node.childNodes[point.offset];\n    const clone = insertAfter(point.node.cloneNode(false), point.node);\n    appendChildNodes(clone, listNext(childNode));\n\n    if (!isSkipPaddingBlankHTML) {\n      paddingBlankHTML(point.node);\n      paddingBlankHTML(clone);\n    }\n\n    if (isDiscardEmptySplits) {\n      if (isEmpty(point.node)) {\n        remove(point.node);\n      }\n      if (isEmpty(clone)) {\n        remove(clone);\n        return point.node.nextSibling;\n      }\n    }\n\n    return clone;\n  }\n}\n\n/**\n * @method splitTree\n *\n * split tree by point\n *\n * @param {Node} root - split root\n * @param {BoundaryPoint} point\n * @param {Object} [options]\n * @param {Boolean} [options.isSkipPaddingBlankHTML] - default: false\n * @param {Boolean} [options.isNotSplitEdgePoint] - default: false\n * @return {Node} right node of boundaryPoint\n */\nfunction splitTree(root, point, options) {\n  // ex) [#text, <span>, <p>]\n  const ancestors = listAncestor(point.node, func.eq(root));\n\n  if (!ancestors.length) {\n    return null;\n  } else if (ancestors.length === 1) {\n    return splitNode(point, options);\n  }\n\n  return ancestors.reduce(function(node, parent) {\n    if (node === point.node) {\n      node = splitNode(point, options);\n    }\n\n    return splitNode({\n      node: parent,\n      offset: node ? position(node) : nodeLength(parent),\n    }, options);\n  });\n}\n\n/**\n * split point\n *\n * @param {Point} point\n * @param {Boolean} isInline\n * @return {Object}\n */\nfunction splitPoint(point, isInline) {\n  // find splitRoot, container\n  //  - inline: splitRoot is a child of paragraph\n  //  - block: splitRoot is a child of bodyContainer\n  const pred = isInline ? isPara : isBodyContainer;\n  const ancestors = listAncestor(point.node, pred);\n  const topAncestor = lists.last(ancestors) || point.node;\n\n  let splitRoot, container;\n  if (pred(topAncestor)) {\n    splitRoot = ancestors[ancestors.length - 2];\n    container = topAncestor;\n  } else {\n    splitRoot = topAncestor;\n    container = splitRoot.parentNode;\n  }\n\n  // if splitRoot is exists, split with splitTree\n  let pivot = splitRoot && splitTree(splitRoot, point, {\n    isSkipPaddingBlankHTML: isInline,\n    isNotSplitEdgePoint: isInline,\n  });\n\n  // if container is point.node, find pivot with point.offset\n  if (!pivot && container === point.node) {\n    pivot = point.node.childNodes[point.offset];\n  }\n\n  return {\n    rightNode: pivot,\n    container: container,\n  };\n}\n\nfunction create(nodeName) {\n  return document.createElement(nodeName);\n}\n\nfunction createText(text) {\n  return document.createTextNode(text);\n}\n\n/**\n * @method remove\n *\n * remove node, (isRemoveChild: remove child or not)\n *\n * @param {Node} node\n * @param {Boolean} isRemoveChild\n */\nfunction remove(node, isRemoveChild) {\n  if (!node || !node.parentNode) { return; }\n  if (node.removeNode) { return node.removeNode(isRemoveChild); }\n\n  const parent = node.parentNode;\n  if (!isRemoveChild) {\n    const nodes = [];\n    for (let i = 0, len = node.childNodes.length; i < len; i++) {\n      nodes.push(node.childNodes[i]);\n    }\n\n    for (let i = 0, len = nodes.length; i < len; i++) {\n      parent.insertBefore(nodes[i], node);\n    }\n  }\n\n  parent.removeChild(node);\n}\n\n/**\n * @method removeWhile\n *\n * @param {Node} node\n * @param {Function} pred\n */\nfunction removeWhile(node, pred) {\n  while (node) {\n    if (isEditable(node) || !pred(node)) {\n      break;\n    }\n\n    const parent = node.parentNode;\n    remove(node);\n    node = parent;\n  }\n}\n\n/**\n * @method replace\n *\n * replace node with provided nodeName\n *\n * @param {Node} node\n * @param {String} nodeName\n * @return {Node} - new node\n */\nfunction replace(node, nodeName) {\n  if (node.nodeName.toUpperCase() === nodeName.toUpperCase()) {\n    return node;\n  }\n\n  const newNode = create(nodeName);\n\n  if (node.style.cssText) {\n    newNode.style.cssText = node.style.cssText;\n  }\n\n  appendChildNodes(newNode, lists.from(node.childNodes));\n  insertAfter(newNode, node);\n  remove(node);\n\n  return newNode;\n}\n\nconst isTextarea = makePredByNodeName('TEXTAREA');\n\n/**\n * @param {jQuery} $node\n * @param {Boolean} [stripLinebreaks] - default: false\n */\nfunction value($node, stripLinebreaks) {\n  const val = isTextarea($node[0]) ? $node.val() : $node.html();\n  if (stripLinebreaks) {\n    return val.replace(/[\\n\\r]/g, '');\n  }\n  return val;\n}\n\n/**\n * @method html\n *\n * get the HTML contents of node\n *\n * @param {jQuery} $node\n * @param {Boolean} [isNewlineOnBlock]\n */\nfunction html($node, isNewlineOnBlock) {\n  let markup = value($node);\n\n  if (isNewlineOnBlock) {\n    const regexTag = /<(\\/?)(\\b(?!!)[^>\\s]*)(.*?)(\\s*\\/?>)/g;\n    markup = markup.replace(regexTag, function(match, endSlash, name) {\n      name = name.toUpperCase();\n      const isEndOfInlineContainer = /^DIV|^TD|^TH|^P|^LI|^H[1-7]/.test(name) &&\n                                   !!endSlash;\n      const isBlockNode = /^BLOCKQUOTE|^TABLE|^TBODY|^TR|^HR|^UL|^OL/.test(name);\n\n      return match + ((isEndOfInlineContainer || isBlockNode) ? '\\n' : '');\n    });\n    markup = markup.trim();\n  }\n\n  return markup;\n}\n\nfunction posFromPlaceholder(placeholder) {\n  const $placeholder = $(placeholder);\n  const pos = $placeholder.offset();\n  const height = $placeholder.outerHeight(true); // include margin\n\n  return {\n    left: pos.left,\n    top: pos.top + height,\n  };\n}\n\nfunction attachEvents($node, events) {\n  Object.keys(events).forEach(function(key) {\n    $node.on(key, events[key]);\n  });\n}\n\nfunction detachEvents($node, events) {\n  Object.keys(events).forEach(function(key) {\n    $node.off(key, events[key]);\n  });\n}\n\n/**\n * @method isCustomStyleTag\n *\n * assert if a node contains a \"note-styletag\" class,\n * which implies that's a custom-made style tag node\n *\n * @param {Node} an HTML DOM node\n */\nfunction isCustomStyleTag(node) {\n  return node && !isText(node) && lists.contains(node.classList, 'note-styletag');\n}\n\nexport default {\n  /** @property {String} NBSP_CHAR */\n  NBSP_CHAR,\n  /** @property {String} ZERO_WIDTH_NBSP_CHAR */\n  ZERO_WIDTH_NBSP_CHAR,\n  /** @property {String} blank */\n  blank: blankHTML,\n  /** @property {String} emptyPara */\n  emptyPara: `<p>${blankHTML}</p>`,\n  makePredByNodeName,\n  isEditable,\n  isControlSizing,\n  isText,\n  isElement,\n  isVoid,\n  isPara,\n  isPurePara,\n  isHeading,\n  isInline,\n  isBlock: func.not(isInline),\n  isBodyInline,\n  isBody,\n  isParaInline,\n  isPre,\n  isList,\n  isTable,\n  isData,\n  isCell,\n  isBlockquote,\n  isBodyContainer,\n  isAnchor,\n  isDiv: makePredByNodeName('DIV'),\n  isLi,\n  isBR: makePredByNodeName('BR'),\n  isSpan: makePredByNodeName('SPAN'),\n  isB: makePredByNodeName('B'),\n  isU: makePredByNodeName('U'),\n  isS: makePredByNodeName('S'),\n  isI: makePredByNodeName('I'),\n  isImg: makePredByNodeName('IMG'),\n  isTextarea,\n  deepestChildIsEmpty,\n  isEmpty,\n  isEmptyAnchor: func.and(isAnchor, isEmpty),\n  isClosestSibling,\n  withClosestSiblings,\n  nodeLength,\n  isLeftEdgePoint,\n  isRightEdgePoint,\n  isEdgePoint,\n  isLeftEdgeOf,\n  isRightEdgeOf,\n  isLeftEdgePointOf,\n  isRightEdgePointOf,\n  prevPoint,\n  nextPoint,\n  nextPointWithEmptyNode,\n  isSamePoint,\n  isVisiblePoint,\n  prevPointUntil,\n  nextPointUntil,\n  isCharPoint,\n  isSpacePoint,\n  walkPoint,\n  ancestor,\n  singleChildAncestor,\n  listAncestor,\n  lastAncestor,\n  listNext,\n  listPrev,\n  listDescendant,\n  commonAncestor,\n  wrap,\n  insertAfter,\n  appendChildNodes,\n  position,\n  hasChildren,\n  makeOffsetPath,\n  fromOffsetPath,\n  splitTree,\n  splitPoint,\n  create,\n  createText,\n  remove,\n  removeWhile,\n  replace,\n  html,\n  value,\n  posFromPlaceholder,\n  attachEvents,\n  detachEvents,\n  isCustomStyleTag,\n};\n", "import $ from 'jquery';\nimport func from './core/func';\nimport lists from './core/lists';\nimport dom from './core/dom';\n\nexport default class Context {\n  /**\n   * @param {jQuery} $note\n   * @param {Object} options\n   */\n  constructor($note, options) {\n    this.$note = $note;\n\n    this.memos = {};\n    this.modules = {};\n    this.layoutInfo = {};\n    this.options = $.extend(true, {}, options);\n\n    // init ui with options\n    $.summernote.ui = $.summernote.ui_template(this.options);\n    this.ui = $.summernote.ui;\n\n    this.initialize();\n  }\n\n  /**\n   * create layout and initialize modules and other resources\n   */\n  initialize() {\n    this.layoutInfo = this.ui.createLayout(this.$note);\n    this._initialize();\n    this.$note.hide();\n    return this;\n  }\n\n  /**\n   * destroy modules and other resources and remove layout\n   */\n  destroy() {\n    this._destroy();\n    this.$note.removeData('summernote');\n    this.ui.removeLayout(this.$note, this.layoutInfo);\n  }\n\n  /**\n   * destory modules and other resources and initialize it again\n   */\n  reset() {\n    const disabled = this.isDisabled();\n    this.code(dom.emptyPara);\n    this._destroy();\n    this._initialize();\n\n    if (disabled) {\n      this.disable();\n    }\n  }\n\n  _initialize() {\n    // set own id\n    this.options.id = func.uniqueId($.now());\n    // set default container for tooltips, popovers, and dialogs\n    this.options.container = this.options.container || this.layoutInfo.editor;\n\n    // add optional buttons\n    const buttons = $.extend({}, this.options.buttons);\n    Object.keys(buttons).forEach((key) => {\n      this.memo('button.' + key, buttons[key]);\n    });\n\n    const modules = $.extend({}, this.options.modules, $.summernote.plugins || {});\n\n    // add and initialize modules\n    Object.keys(modules).forEach((key) => {\n      this.module(key, modules[key], true);\n    });\n\n    Object.keys(this.modules).forEach((key) => {\n      this.initializeModule(key);\n    });\n  }\n\n  _destroy() {\n    // destroy modules with reversed order\n    Object.keys(this.modules).reverse().forEach((key) => {\n      this.removeModule(key);\n    });\n\n    Object.keys(this.memos).forEach((key) => {\n      this.removeMemo(key);\n    });\n    // trigger custom onDestroy callback\n    this.triggerEvent('destroy', this);\n  }\n\n  code(html) {\n    const isActivated = this.invoke('codeview.isActivated');\n\n    if (html === undefined) {\n      this.invoke('codeview.sync');\n      return isActivated ? this.layoutInfo.codable.val() : this.layoutInfo.editable.html();\n    } else {\n      if (isActivated) {\n        this.invoke('codeview.sync', html);\n      } else {\n        this.layoutInfo.editable.html(html);\n      }\n      this.$note.val(html);\n      this.triggerEvent('change', html, this.layoutInfo.editable);\n    }\n  }\n\n  isDisabled() {\n    return this.layoutInfo.editable.attr('contenteditable') === 'false';\n  }\n\n  enable() {\n    this.layoutInfo.editable.attr('contenteditable', true);\n    this.invoke('toolbar.activate', true);\n    this.triggerEvent('disable', false);\n    this.options.editing = true;\n  }\n\n  disable() {\n    // close codeview if codeview is opend\n    if (this.invoke('codeview.isActivated')) {\n      this.invoke('codeview.deactivate');\n    }\n    this.layoutInfo.editable.attr('contenteditable', false);\n    this.options.editing = false;\n    this.invoke('toolbar.deactivate', true);\n\n    this.triggerEvent('disable', true);\n  }\n\n  triggerEvent() {\n    const namespace = lists.head(arguments);\n    const args = lists.tail(lists.from(arguments));\n\n    const callback = this.options.callbacks[func.namespaceToCamel(namespace, 'on')];\n    if (callback) {\n      callback.apply(this.$note[0], args);\n    }\n    this.$note.trigger('summernote.' + namespace, args);\n  }\n\n  initializeModule(key) {\n    const module = this.modules[key];\n    module.shouldInitialize = module.shouldInitialize || func.ok;\n    if (!module.shouldInitialize()) {\n      return;\n    }\n\n    // initialize module\n    if (module.initialize) {\n      module.initialize();\n    }\n\n    // attach events\n    if (module.events) {\n      dom.attachEvents(this.$note, module.events);\n    }\n  }\n\n  module(key, ModuleClass, withoutIntialize) {\n    if (arguments.length === 1) {\n      return this.modules[key];\n    }\n\n    this.modules[key] = new ModuleClass(this);\n\n    if (!withoutIntialize) {\n      this.initializeModule(key);\n    }\n  }\n\n  removeModule(key) {\n    const module = this.modules[key];\n    if (module.shouldInitialize()) {\n      if (module.events) {\n        dom.detachEvents(this.$note, module.events);\n      }\n\n      if (module.destroy) {\n        module.destroy();\n      }\n    }\n\n    delete this.modules[key];\n  }\n\n  memo(key, obj) {\n    if (arguments.length === 1) {\n      return this.memos[key];\n    }\n    this.memos[key] = obj;\n  }\n\n  removeMemo(key) {\n    if (this.memos[key] && this.memos[key].destroy) {\n      this.memos[key].destroy();\n    }\n\n    delete this.memos[key];\n  }\n\n  /**\n   * Some buttons need to change their visual style immediately once they get pressed\n   */\n  createInvokeHandlerAndUpdateState(namespace, value) {\n    return (event) => {\n      this.createInvokeHandler(namespace, value)(event);\n      this.invoke('buttons.updateCurrentStyle');\n    };\n  }\n\n  createInvokeHandler(namespace, value) {\n    return (event) => {\n      event.preventDefault();\n      const $target = $(event.target);\n      this.invoke(namespace, value || $target.closest('[data-value]').data('value'), $target);\n    };\n  }\n\n  invoke() {\n    const namespace = lists.head(arguments);\n    const args = lists.tail(lists.from(arguments));\n\n    const splits = namespace.split('.');\n    const hasSeparator = splits.length > 1;\n    const moduleName = hasSeparator && lists.head(splits);\n    const methodName = hasSeparator ? lists.last(splits) : lists.head(splits);\n\n    const module = this.modules[moduleName || 'editor'];\n    if (!moduleName && this[methodName]) {\n      return this[methodName].apply(this, args);\n    } else if (module && module[methodName] && module.shouldInitialize()) {\n      return module[methodName].apply(module, args);\n    }\n  }\n}\n", "import $ from 'jquery';\nimport env from './env';\nimport func from './func';\nimport lists from './lists';\nimport dom from './dom';\n\n/**\n * return boundaryPoint from TextRange, inspired by <PERSON>'s HuskyRange.js\n *\n * @param {TextRange} textRange\n * @param {Boolean} isStart\n * @return {BoundaryPoint}\n *\n * @see http://msdn.microsoft.com/en-us/library/ie/ms535872(v=vs.85).aspx\n */\nfunction textRangeToPoint(textRange, isStart) {\n  let container = textRange.parentElement();\n  let offset;\n\n  const tester = document.body.createTextRange();\n  let prevContainer;\n  const childNodes = lists.from(container.childNodes);\n  for (offset = 0; offset < childNodes.length; offset++) {\n    if (dom.isText(childNodes[offset])) {\n      continue;\n    }\n    tester.moveToElementText(childNodes[offset]);\n    if (tester.compareEndPoints('StartToStart', textRange) >= 0) {\n      break;\n    }\n    prevContainer = childNodes[offset];\n  }\n\n  if (offset !== 0 && dom.isText(childNodes[offset - 1])) {\n    const textRangeStart = document.body.createTextRange();\n    let curTextNode = null;\n    textRangeStart.moveToElementText(prevContainer || container);\n    textRangeStart.collapse(!prevContainer);\n    curTextNode = prevContainer ? prevContainer.nextSibling : container.firstChild;\n\n    const pointTester = textRange.duplicate();\n    pointTester.setEndPoint('StartToStart', textRangeStart);\n    let textCount = pointTester.text.replace(/[\\r\\n]/g, '').length;\n\n    while (textCount > curTextNode.nodeValue.length && curTextNode.nextSibling) {\n      textCount -= curTextNode.nodeValue.length;\n      curTextNode = curTextNode.nextSibling;\n    }\n\n    // [workaround] enforce IE to re-reference curTextNode, hack\n    const dummy = curTextNode.nodeValue; // eslint-disable-line\n\n    if (isStart && curTextNode.nextSibling && dom.isText(curTextNode.nextSibling) &&\n      textCount === curTextNode.nodeValue.length) {\n      textCount -= curTextNode.nodeValue.length;\n      curTextNode = curTextNode.nextSibling;\n    }\n\n    container = curTextNode;\n    offset = textCount;\n  }\n\n  return {\n    cont: container,\n    offset: offset,\n  };\n}\n\n/**\n * return TextRange from boundary point (inspired by google closure-library)\n * @param {BoundaryPoint} point\n * @return {TextRange}\n */\nfunction pointToTextRange(point) {\n  const textRangeInfo = function(container, offset) {\n    let node, isCollapseToStart;\n\n    if (dom.isText(container)) {\n      const prevTextNodes = dom.listPrev(container, func.not(dom.isText));\n      const prevContainer = lists.last(prevTextNodes).previousSibling;\n      node = prevContainer || container.parentNode;\n      offset += lists.sum(lists.tail(prevTextNodes), dom.nodeLength);\n      isCollapseToStart = !prevContainer;\n    } else {\n      node = container.childNodes[offset] || container;\n      if (dom.isText(node)) {\n        return textRangeInfo(node, 0);\n      }\n\n      offset = 0;\n      isCollapseToStart = false;\n    }\n\n    return {\n      node: node,\n      collapseToStart: isCollapseToStart,\n      offset: offset,\n    };\n  };\n\n  const textRange = document.body.createTextRange();\n  const info = textRangeInfo(point.node, point.offset);\n\n  textRange.moveToElementText(info.node);\n  textRange.collapse(info.collapseToStart);\n  textRange.moveStart('character', info.offset);\n  return textRange;\n}\n\n/**\n   * Wrapped Range\n   *\n   * @constructor\n   * @param {Node} sc - start container\n   * @param {Number} so - start offset\n   * @param {Node} ec - end container\n   * @param {Number} eo - end offset\n   */\nclass WrappedRange {\n  constructor(sc, so, ec, eo) {\n    this.sc = sc;\n    this.so = so;\n    this.ec = ec;\n    this.eo = eo;\n\n    // isOnEditable: judge whether range is on editable or not\n    this.isOnEditable = this.makeIsOn(dom.isEditable);\n    // isOnList: judge whether range is on list node or not\n    this.isOnList = this.makeIsOn(dom.isList);\n    // isOnAnchor: judge whether range is on anchor node or not\n    this.isOnAnchor = this.makeIsOn(dom.isAnchor);\n    // isOnCell: judge whether range is on cell node or not\n    this.isOnCell = this.makeIsOn(dom.isCell);\n    // isOnData: judge whether range is on data node or not\n    this.isOnData = this.makeIsOn(dom.isData);\n  }\n\n  // nativeRange: get nativeRange from sc, so, ec, eo\n  nativeRange() {\n    if (env.isW3CRangeSupport) {\n      const w3cRange = document.createRange();\n      w3cRange.setStart(this.sc, this.so);\n      w3cRange.setEnd(this.ec, this.eo);\n\n      return w3cRange;\n    } else {\n      const textRange = pointToTextRange({\n        node: this.sc,\n        offset: this.so,\n      });\n\n      textRange.setEndPoint('EndToEnd', pointToTextRange({\n        node: this.ec,\n        offset: this.eo,\n      }));\n\n      return textRange;\n    }\n  }\n\n  getPoints() {\n    return {\n      sc: this.sc,\n      so: this.so,\n      ec: this.ec,\n      eo: this.eo,\n    };\n  }\n\n  getStartPoint() {\n    return {\n      node: this.sc,\n      offset: this.so,\n    };\n  }\n\n  getEndPoint() {\n    return {\n      node: this.ec,\n      offset: this.eo,\n    };\n  }\n\n  /**\n   * select update visible range\n   */\n  select() {\n    const nativeRng = this.nativeRange();\n    if (env.isW3CRangeSupport) {\n      const selection = document.getSelection();\n      if (selection.rangeCount > 0) {\n        selection.removeAllRanges();\n      }\n      selection.addRange(nativeRng);\n    } else {\n      nativeRng.select();\n    }\n\n    return this;\n  }\n\n  /**\n   * Moves the scrollbar to start container(sc) of current range\n   *\n   * @return {WrappedRange}\n   */\n  scrollIntoView(container) {\n    const height = $(container).height();\n    if (container.scrollTop + height < this.sc.offsetTop) {\n      container.scrollTop += Math.abs(container.scrollTop + height - this.sc.offsetTop);\n    }\n\n    return this;\n  }\n\n  /**\n   * @return {WrappedRange}\n   */\n  normalize() {\n    /**\n     * @param {BoundaryPoint} point\n     * @param {Boolean} isLeftToRight - true: prefer to choose right node\n     *                                - false: prefer to choose left node\n     * @return {BoundaryPoint}\n     */\n    const getVisiblePoint = function(point, isLeftToRight) {\n      if (!point) {\n        return point;\n      }\n\n      // Just use the given point [XXX:Adhoc]\n      //  - case 01. if the point is on the middle of the node\n      //  - case 02. if the point is on the right edge and prefer to choose left node\n      //  - case 03. if the point is on the left edge and prefer to choose right node\n      //  - case 04. if the point is on the right edge and prefer to choose right node but the node is void\n      //  - case 05. if the point is on the left edge and prefer to choose left node but the node is void\n      //  - case 06. if the point is on the block node and there is no children\n      if (dom.isVisiblePoint(point)) {\n        if (!dom.isEdgePoint(point) ||\n            (dom.isRightEdgePoint(point) && !isLeftToRight) ||\n            (dom.isLeftEdgePoint(point) && isLeftToRight) ||\n            (dom.isRightEdgePoint(point) && isLeftToRight && dom.isVoid(point.node.nextSibling)) ||\n            (dom.isLeftEdgePoint(point) && !isLeftToRight && dom.isVoid(point.node.previousSibling)) ||\n            (dom.isBlock(point.node) && dom.isEmpty(point.node))) {\n          return point;\n        }\n      }\n\n      // point on block's edge\n      const block = dom.ancestor(point.node, dom.isBlock);\n      let hasRightNode = false;\n\n      if (!hasRightNode) {\n        const prevPoint = dom.prevPoint(point) || { node: null };\n        hasRightNode = (dom.isLeftEdgePointOf(point, block) || dom.isVoid(prevPoint.node)) && !isLeftToRight;\n      }\n\n      let hasLeftNode = false;\n      if (!hasLeftNode) {\n        const nextPoint = dom.nextPoint(point) || { node: null };\n        hasLeftNode = (dom.isRightEdgePointOf(point, block) || dom.isVoid(nextPoint.node)) && isLeftToRight;\n      }\n\n      if (hasRightNode || hasLeftNode) {\n        // returns point already on visible point\n        if (dom.isVisiblePoint(point)) {\n          return point;\n        }\n        // reverse direction\n        isLeftToRight = !isLeftToRight;\n      }\n\n      const nextPoint = isLeftToRight ? dom.nextPointUntil(dom.nextPoint(point), dom.isVisiblePoint)\n        : dom.prevPointUntil(dom.prevPoint(point), dom.isVisiblePoint);\n      return nextPoint || point;\n    };\n\n    const endPoint = getVisiblePoint(this.getEndPoint(), false);\n    const startPoint = this.isCollapsed() ? endPoint : getVisiblePoint(this.getStartPoint(), true);\n\n    return new WrappedRange(\n      startPoint.node,\n      startPoint.offset,\n      endPoint.node,\n      endPoint.offset\n    );\n  }\n\n  /**\n   * returns matched nodes on range\n   *\n   * @param {Function} [pred] - predicate function\n   * @param {Object} [options]\n   * @param {Boolean} [options.includeAncestor]\n   * @param {Boolean} [options.fullyContains]\n   * @return {Node[]}\n   */\n  nodes(pred, options) {\n    pred = pred || func.ok;\n\n    const includeAncestor = options && options.includeAncestor;\n    const fullyContains = options && options.fullyContains;\n\n    // TODO compare points and sort\n    const startPoint = this.getStartPoint();\n    const endPoint = this.getEndPoint();\n\n    const nodes = [];\n    const leftEdgeNodes = [];\n\n    dom.walkPoint(startPoint, endPoint, function(point) {\n      if (dom.isEditable(point.node)) {\n        return;\n      }\n\n      let node;\n      if (fullyContains) {\n        if (dom.isLeftEdgePoint(point)) {\n          leftEdgeNodes.push(point.node);\n        }\n        if (dom.isRightEdgePoint(point) && lists.contains(leftEdgeNodes, point.node)) {\n          node = point.node;\n        }\n      } else if (includeAncestor) {\n        node = dom.ancestor(point.node, pred);\n      } else {\n        node = point.node;\n      }\n\n      if (node && pred(node)) {\n        nodes.push(node);\n      }\n    }, true);\n\n    return lists.unique(nodes);\n  }\n\n  /**\n   * returns commonAncestor of range\n   * @return {Element} - commonAncestor\n   */\n  commonAncestor() {\n    return dom.commonAncestor(this.sc, this.ec);\n  }\n\n  /**\n   * returns expanded range by pred\n   *\n   * @param {Function} pred - predicate function\n   * @return {WrappedRange}\n   */\n  expand(pred) {\n    const startAncestor = dom.ancestor(this.sc, pred);\n    const endAncestor = dom.ancestor(this.ec, pred);\n\n    if (!startAncestor && !endAncestor) {\n      return new WrappedRange(this.sc, this.so, this.ec, this.eo);\n    }\n\n    const boundaryPoints = this.getPoints();\n\n    if (startAncestor) {\n      boundaryPoints.sc = startAncestor;\n      boundaryPoints.so = 0;\n    }\n\n    if (endAncestor) {\n      boundaryPoints.ec = endAncestor;\n      boundaryPoints.eo = dom.nodeLength(endAncestor);\n    }\n\n    return new WrappedRange(\n      boundaryPoints.sc,\n      boundaryPoints.so,\n      boundaryPoints.ec,\n      boundaryPoints.eo\n    );\n  }\n\n  /**\n   * @param {Boolean} isCollapseToStart\n   * @return {WrappedRange}\n   */\n  collapse(isCollapseToStart) {\n    if (isCollapseToStart) {\n      return new WrappedRange(this.sc, this.so, this.sc, this.so);\n    } else {\n      return new WrappedRange(this.ec, this.eo, this.ec, this.eo);\n    }\n  }\n\n  /**\n   * splitText on range\n   */\n  splitText() {\n    const isSameContainer = this.sc === this.ec;\n    const boundaryPoints = this.getPoints();\n\n    if (dom.isText(this.ec) && !dom.isEdgePoint(this.getEndPoint())) {\n      this.ec.splitText(this.eo);\n    }\n\n    if (dom.isText(this.sc) && !dom.isEdgePoint(this.getStartPoint())) {\n      boundaryPoints.sc = this.sc.splitText(this.so);\n      boundaryPoints.so = 0;\n\n      if (isSameContainer) {\n        boundaryPoints.ec = boundaryPoints.sc;\n        boundaryPoints.eo = this.eo - this.so;\n      }\n    }\n\n    return new WrappedRange(\n      boundaryPoints.sc,\n      boundaryPoints.so,\n      boundaryPoints.ec,\n      boundaryPoints.eo\n    );\n  }\n\n  /**\n   * delete contents on range\n   * @return {WrappedRange}\n   */\n  deleteContents() {\n    if (this.isCollapsed()) {\n      return this;\n    }\n\n    const rng = this.splitText();\n    const nodes = rng.nodes(null, {\n      fullyContains: true,\n    });\n\n    // find new cursor point\n    const point = dom.prevPointUntil(rng.getStartPoint(), function(point) {\n      return !lists.contains(nodes, point.node);\n    });\n\n    const emptyParents = [];\n    $.each(nodes, function(idx, node) {\n      // find empty parents\n      const parent = node.parentNode;\n      if (point.node !== parent && dom.nodeLength(parent) === 1) {\n        emptyParents.push(parent);\n      }\n      dom.remove(node, false);\n    });\n\n    // remove empty parents\n    $.each(emptyParents, function(idx, node) {\n      dom.remove(node, false);\n    });\n\n    return new WrappedRange(\n      point.node,\n      point.offset,\n      point.node,\n      point.offset\n    ).normalize();\n  }\n\n  /**\n   * makeIsOn: return isOn(pred) function\n   */\n  makeIsOn(pred) {\n    return function() {\n      const ancestor = dom.ancestor(this.sc, pred);\n      return !!ancestor && (ancestor === dom.ancestor(this.ec, pred));\n    };\n  }\n\n  /**\n   * @param {Function} pred\n   * @return {Boolean}\n   */\n  isLeftEdgeOf(pred) {\n    if (!dom.isLeftEdgePoint(this.getStartPoint())) {\n      return false;\n    }\n\n    const node = dom.ancestor(this.sc, pred);\n    return node && dom.isLeftEdgeOf(this.sc, node);\n  }\n\n  /**\n   * returns whether range was collapsed or not\n   */\n  isCollapsed() {\n    return this.sc === this.ec && this.so === this.eo;\n  }\n\n  /**\n   * wrap inline nodes which children of body with paragraph\n   *\n   * @return {WrappedRange}\n   */\n  wrapBodyInlineWithPara() {\n    if (dom.isBodyContainer(this.sc) && dom.isEmpty(this.sc)) {\n      this.sc.innerHTML = dom.emptyPara;\n      return new WrappedRange(this.sc.firstChild, 0, this.sc.firstChild, 0);\n    }\n\n    /**\n     * [workaround] firefox often create range on not visible point. so normalize here.\n     *  - firefox: |<p>text</p>|\n     *  - chrome: <p>|text|</p>\n     */\n    const rng = this.normalize();\n    if (dom.isParaInline(this.sc) || dom.isPara(this.sc)) {\n      return rng;\n    }\n\n    // find inline top ancestor\n    let topAncestor;\n    if (dom.isInline(rng.sc)) {\n      const ancestors = dom.listAncestor(rng.sc, func.not(dom.isInline));\n      topAncestor = lists.last(ancestors);\n      if (!dom.isInline(topAncestor)) {\n        topAncestor = ancestors[ancestors.length - 2] || rng.sc.childNodes[rng.so];\n      }\n    } else {\n      topAncestor = rng.sc.childNodes[rng.so > 0 ? rng.so - 1 : 0];\n    }\n\n    if (topAncestor) {\n      // siblings not in paragraph\n      let inlineSiblings = dom.listPrev(topAncestor, dom.isParaInline).reverse();\n      inlineSiblings = inlineSiblings.concat(dom.listNext(topAncestor.nextSibling, dom.isParaInline));\n\n      // wrap with paragraph\n      if (inlineSiblings.length) {\n        const para = dom.wrap(lists.head(inlineSiblings), 'p');\n        dom.appendChildNodes(para, lists.tail(inlineSiblings));\n      }\n    }\n\n    return this.normalize();\n  }\n\n  /**\n   * insert node at current cursor\n   *\n   * @param {Node} node\n   * @return {Node}\n   */\n  insertNode(node) {\n    let rng = this;\n\n    if (dom.isText(node) || dom.isInline(node)) {\n      rng = this.wrapBodyInlineWithPara().deleteContents();\n    }\n\n    const info = dom.splitPoint(rng.getStartPoint(), dom.isInline(node));\n    if (info.rightNode) {\n      info.rightNode.parentNode.insertBefore(node, info.rightNode);\n      if (dom.isEmpty(info.rightNode) && dom.isPara(node)) {\n        info.rightNode.parentNode.removeChild(info.rightNode);\n      }\n    } else {\n      info.container.appendChild(node);\n    }\n\n    return node;\n  }\n\n  /**\n   * insert html at current cursor\n   */\n  pasteHTML(markup) {\n    markup = $.trim(markup);\n\n    const contentsContainer = $('<div></div>').html(markup)[0];\n    let childNodes = lists.from(contentsContainer.childNodes);\n\n    // const rng = this.wrapBodyInlineWithPara().deleteContents();\n    const rng = this;\n    let reversed = false;\n\n    if (rng.so >= 0) {\n      childNodes = childNodes.reverse();\n      reversed = true;\n    }\n\n    childNodes = childNodes.map(function(childNode) {\n      return rng.insertNode(childNode);\n    });\n\n    if (reversed) {\n      childNodes = childNodes.reverse();\n    }\n    return childNodes;\n  }\n\n  /**\n   * returns text in range\n   *\n   * @return {String}\n   */\n  toString() {\n    const nativeRng = this.nativeRange();\n    return env.isW3CRangeSupport ? nativeRng.toString() : nativeRng.text;\n  }\n\n  /**\n   * returns range for word before cursor\n   *\n   * @param {Boolean} [findAfter] - find after cursor, default: false\n   * @return {WrappedRange}\n   */\n  getWordRange(findAfter) {\n    let endPoint = this.getEndPoint();\n\n    if (!dom.isCharPoint(endPoint)) {\n      return this;\n    }\n\n    const startPoint = dom.prevPointUntil(endPoint, function(point) {\n      return !dom.isCharPoint(point);\n    });\n\n    if (findAfter) {\n      endPoint = dom.nextPointUntil(endPoint, function(point) {\n        return !dom.isCharPoint(point);\n      });\n    }\n\n    return new WrappedRange(\n      startPoint.node,\n      startPoint.offset,\n      endPoint.node,\n      endPoint.offset\n    );\n  }\n\n  /**\n   * returns range for words before cursor\n   *\n   * @param {Boolean} [findAfter] - find after cursor, default: false\n   * @return {WrappedRange}\n   */\n  getWordsRange(findAfter) {\n    var endPoint = this.getEndPoint();\n\n    var isNotTextPoint = function(point) {\n      return !dom.isCharPoint(point) && !dom.isSpacePoint(point);\n    };\n\n    if (isNotTextPoint(endPoint)) {\n      return this;\n    }\n\n    var startPoint = dom.prevPointUntil(endPoint, isNotTextPoint);\n\n    if (findAfter) {\n      endPoint = dom.nextPointUntil(endPoint, isNotTextPoint);\n    }\n\n    return new WrappedRange(\n      startPoint.node,\n      startPoint.offset,\n      endPoint.node,\n      endPoint.offset\n    );\n  }\n\n  /**\n   * returns range for words before cursor that match with a Regex\n   *\n   * example:\n   *  range: 'hi @Peter Pan'\n   *  regex: '/@[a-z ]+/i'\n   *  return range: '@Peter Pan'\n   *\n   * @param {RegExp} [regex]\n   * @return {WrappedRange|null}\n   */\n  getWordsMatchRange(regex) {\n    var endPoint = this.getEndPoint();\n\n    var startPoint = dom.prevPointUntil(endPoint, function(point) {\n      if (!dom.isCharPoint(point) && !dom.isSpacePoint(point)) {\n        return true;\n      }\n      var rng = new WrappedRange(\n        point.node,\n        point.offset,\n        endPoint.node,\n        endPoint.offset\n      );\n      var result = regex.exec(rng.toString());\n      return result && result.index === 0;\n    });\n\n    var rng = new WrappedRange(\n      startPoint.node,\n      startPoint.offset,\n      endPoint.node,\n      endPoint.offset\n    );\n\n    var text = rng.toString();\n    var result = regex.exec(text);\n\n    if (result && result[0].length === text.length) {\n      return rng;\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * create offsetPath bookmark\n   *\n   * @param {Node} editable\n   */\n  bookmark(editable) {\n    return {\n      s: {\n        path: dom.makeOffsetPath(editable, this.sc),\n        offset: this.so,\n      },\n      e: {\n        path: dom.makeOffsetPath(editable, this.ec),\n        offset: this.eo,\n      },\n    };\n  }\n\n  /**\n   * create offsetPath bookmark base on paragraph\n   *\n   * @param {Node[]} paras\n   */\n  paraBookmark(paras) {\n    return {\n      s: {\n        path: lists.tail(dom.makeOffsetPath(lists.head(paras), this.sc)),\n        offset: this.so,\n      },\n      e: {\n        path: lists.tail(dom.makeOffsetPath(lists.last(paras), this.ec)),\n        offset: this.eo,\n      },\n    };\n  }\n\n  /**\n   * getClientRects\n   * @return {Rect[]}\n   */\n  getClientRects() {\n    const nativeRng = this.nativeRange();\n    return nativeRng.getClientRects();\n  }\n}\n\n/**\n * Data structure\n *  * BoundaryPoint: a point of dom tree\n *  * BoundaryPoints: two boundaryPoints corresponding to the start and the end of the Range\n *\n * See to http://www.w3.org/TR/DOM-Level-2-Traversal-Range/ranges.html#Level-2-Range-Position\n */\nexport default {\n  /**\n   * create Range Object From arguments or Browser Selection\n   *\n   * @param {Node} sc - start container\n   * @param {Number} so - start offset\n   * @param {Node} ec - end container\n   * @param {Number} eo - end offset\n   * @return {WrappedRange}\n   */\n  create: function(sc, so, ec, eo) {\n    if (arguments.length === 4) {\n      return new WrappedRange(sc, so, ec, eo);\n    } else if (arguments.length === 2) { // collapsed\n      ec = sc;\n      eo = so;\n      return new WrappedRange(sc, so, ec, eo);\n    } else {\n      let wrappedRange = this.createFromSelection();\n\n      if (!wrappedRange && arguments.length === 1) {\n        let bodyElement = arguments[0];\n        if (dom.isEditable(bodyElement)) {\n          bodyElement = bodyElement.lastChild;\n        }\n        return this.createFromBodyElement(bodyElement, dom.emptyPara === arguments[0].innerHTML);\n      }\n      return wrappedRange;\n    }\n  },\n\n  createFromBodyElement: function(bodyElement, isCollapseToStart = false) {\n    var wrappedRange = this.createFromNode(bodyElement);\n    return wrappedRange.collapse(isCollapseToStart);\n  },\n\n  createFromSelection: function() {\n    let sc, so, ec, eo;\n    if (env.isW3CRangeSupport) {\n      const selection = document.getSelection();\n      if (!selection || selection.rangeCount === 0) {\n        return null;\n      } else if (dom.isBody(selection.anchorNode)) {\n        // Firefox: returns entire body as range on initialization.\n        // We won't never need it.\n        return null;\n      }\n\n      const nativeRng = selection.getRangeAt(0);\n      sc = nativeRng.startContainer;\n      so = nativeRng.startOffset;\n      ec = nativeRng.endContainer;\n      eo = nativeRng.endOffset;\n    } else { // IE8: TextRange\n      const textRange = document.selection.createRange();\n      const textRangeEnd = textRange.duplicate();\n      textRangeEnd.collapse(false);\n      const textRangeStart = textRange;\n      textRangeStart.collapse(true);\n\n      let startPoint = textRangeToPoint(textRangeStart, true);\n      let endPoint = textRangeToPoint(textRangeEnd, false);\n\n      // same visible point case: range was collapsed.\n      if (dom.isText(startPoint.node) && dom.isLeftEdgePoint(startPoint) &&\n        dom.isTextNode(endPoint.node) && dom.isRightEdgePoint(endPoint) &&\n        endPoint.node.nextSibling === startPoint.node) {\n        startPoint = endPoint;\n      }\n\n      sc = startPoint.cont;\n      so = startPoint.offset;\n      ec = endPoint.cont;\n      eo = endPoint.offset;\n    }\n\n    return new WrappedRange(sc, so, ec, eo);\n  },\n\n  /**\n   * @method\n   *\n   * create WrappedRange from node\n   *\n   * @param {Node} node\n   * @return {WrappedRange}\n   */\n  createFromNode: function(node) {\n    let sc = node;\n    let so = 0;\n    let ec = node;\n    let eo = dom.nodeLength(ec);\n\n    // browsers can't target a picture or void node\n    if (dom.isVoid(sc)) {\n      so = dom.listPrev(sc).length - 1;\n      sc = sc.parentNode;\n    }\n    if (dom.isBR(ec)) {\n      eo = dom.listPrev(ec).length - 1;\n      ec = ec.parentNode;\n    } else if (dom.isVoid(ec)) {\n      eo = dom.listPrev(ec).length;\n      ec = ec.parentNode;\n    }\n\n    return this.create(sc, so, ec, eo);\n  },\n\n  /**\n   * create WrappedRange from node after position\n   *\n   * @param {Node} node\n   * @return {WrappedRange}\n   */\n  createFromNodeBefore: function(node) {\n    return this.createFromNode(node).collapse(true);\n  },\n\n  /**\n   * create WrappedRange from node after position\n   *\n   * @param {Node} node\n   * @return {WrappedRange}\n   */\n  createFromNodeAfter: function(node) {\n    return this.createFromNode(node).collapse();\n  },\n\n  /**\n   * @method\n   *\n   * create WrappedRange from bookmark\n   *\n   * @param {Node} editable\n   * @param {Object} bookmark\n   * @return {WrappedRange}\n   */\n  createFromBookmark: function(editable, bookmark) {\n    const sc = dom.fromOffsetPath(editable, bookmark.s.path);\n    const so = bookmark.s.offset;\n    const ec = dom.fromOffsetPath(editable, bookmark.e.path);\n    const eo = bookmark.e.offset;\n    return new WrappedRange(sc, so, ec, eo);\n  },\n\n  /**\n   * @method\n   *\n   * create WrappedRange from paraBookmark\n   *\n   * @param {Object} bookmark\n   * @param {Node[]} paras\n   * @return {WrappedRange}\n   */\n  createFromParaBookmark: function(bookmark, paras) {\n    const so = bookmark.s.offset;\n    const eo = bookmark.e.offset;\n    const sc = dom.fromOffsetPath(lists.head(paras), bookmark.s.path);\n    const ec = dom.fromOffsetPath(lists.last(paras), bookmark.e.path);\n\n    return new WrappedRange(sc, so, ec, eo);\n  },\n};\n", "import $ from 'jquery';\nimport env from './base/core/env';\nimport lists from './base/core/lists';\nimport Context from './base/Context';\n\n$.fn.extend({\n  /**\n   * Summernote API\n   *\n   * @param {Object|String}\n   * @return {this}\n   */\n  summernote: function() {\n    const type = $.type(lists.head(arguments));\n    const isExternalAPICalled = type === 'string';\n    const hasInitOptions = type === 'object';\n\n    const options = $.extend({}, $.summernote.options, hasInitOptions ? lists.head(arguments) : {});\n\n    // Update options\n    options.langInfo = $.extend(true, {}, $.summernote.lang['en-US'], $.summernote.lang[options.lang]);\n    options.icons = $.extend(true, {}, $.summernote.options.icons, options.icons);\n    options.tooltip = options.tooltip === 'auto' ? !env.isSupportTouch : options.tooltip;\n\n    this.each((idx, note) => {\n      const $note = $(note);\n      if (!$note.data('summernote')) {\n        const context = new Context($note, options);\n        $note.data('summernote', context);\n        $note.data('summernote').triggerEvent('init', context.layoutInfo);\n      }\n    });\n\n    const $note = this.first();\n    if ($note.length) {\n      const context = $note.data('summernote');\n      if (isExternalAPICalled) {\n        return context.invoke.apply(context, lists.from(arguments));\n      } else if (options.focus) {\n        context.invoke('editor.focus');\n      }\n    }\n\n    return this;\n  },\n});\n", "import lists from './lists';\nimport func from './func';\n\nconst KEY_MAP = {\n  'BACKSPACE': 8,\n  'TAB': 9,\n  'ENTER': 13,\n  'ESCAPE': 27,\n  'SPACE': 32,\n  'DELETE': 46,\n\n  // Arrow\n  'LEFT': 37,\n  'UP': 38,\n  'RIGHT': 39,\n  'DOWN': 40,\n\n  // Number: 0-9\n  'NUM0': 48,\n  'NUM1': 49,\n  'NUM2': 50,\n  'NUM3': 51,\n  'NUM4': 52,\n  'NUM5': 53,\n  'NUM6': 54,\n  'NUM7': 55,\n  'NUM8': 56,\n\n  // Alphabet: a-z\n  'B': 66,\n  'E': 69,\n  'I': 73,\n  'J': 74,\n  'K': 75,\n  'L': 76,\n  'R': 82,\n  'S': 83,\n  'U': 85,\n  'V': 86,\n  'Y': 89,\n  'Z': 90,\n\n  'SLASH': 191,\n  'LEFTBRACKET': 219,\n  'BACKSLASH': 220,\n  'RIGHTBRACKET': 221,\n\n  // Navigation\n  'HOME': 36,\n  'END': 35,\n  'PAGEUP': 33,\n  'PAGEDOWN': 34,\n};\n\n/**\n * @class core.key\n *\n * Object for keycodes.\n *\n * @singleton\n * @alternateClassName key\n */\nexport default {\n  /**\n   * @method isEdit\n   *\n   * @param {Number} keyCode\n   * @return {Boolean}\n   */\n  isEdit: (keyCode) => {\n    return lists.contains([\n      KEY_MAP.BACKSPACE,\n      KEY_MAP.TAB,\n      KEY_MAP.ENTER,\n      KEY_MAP.SPACE,\n      KEY_MAP.DELETE,\n    ], keyCode);\n  },\n  /**\n   * @method isMove\n   *\n   * @param {Number} keyCode\n   * @return {Boolean}\n   */\n  isMove: (keyCode) => {\n    return lists.contains([\n      KEY_MAP.LEFT,\n      KEY_MAP.UP,\n      KEY_MAP.RIGHT,\n      KEY_MAP.DOWN,\n    ], keyCode);\n  },\n  /**\n   * @method isNavigation\n   *\n   * @param {Number} keyCode\n   * @return {Boolean}\n   */\n  isNavigation: (keyCode) => {\n    return lists.contains([\n      KEY_MAP.HOME,\n      KEY_MAP.END,\n      KEY_MAP.PAGEUP,\n      KEY_MAP.PAGEDOWN,\n    ], keyCode);\n  },\n  /**\n   * @property {Object} nameFromCode\n   * @property {String} nameFromCode.8 \"BACKSPACE\"\n   */\n  nameFromCode: func.invertObject(KEY_MAP),\n  code: KEY_MAP,\n};\n", "import range from '../core/range';\n\nexport default class History {\n  constructor(context) {\n    this.stack = [];\n    this.stackOffset = -1;\n    this.context = context;\n    this.$editable = context.layoutInfo.editable;\n    this.editable = this.$editable[0];\n  }\n\n  makeSnapshot() {\n    const rng = range.create(this.editable);\n    const emptyBookmark = { s: { path: [], offset: 0 }, e: { path: [], offset: 0 } };\n\n    return {\n      contents: this.$editable.html(),\n      bookmark: ((rng && rng.isOnEditable()) ? rng.bookmark(this.editable) : emptyBookmark),\n    };\n  }\n\n  applySnapshot(snapshot) {\n    if (snapshot.contents !== null) {\n      this.$editable.html(snapshot.contents);\n    }\n    if (snapshot.bookmark !== null) {\n      range.createFromBookmark(this.editable, snapshot.bookmark).select();\n    }\n  }\n\n  /**\n  * @method rewind\n  * Rewinds the history stack back to the first snapshot taken.\n  * Leaves the stack intact, so that \"Redo\" can still be used.\n  */\n  rewind() {\n    // Create snap shot if not yet recorded\n    if (this.$editable.html() !== this.stack[this.stackOffset].contents) {\n      this.recordUndo();\n    }\n\n    // Return to the first available snapshot.\n    this.stackOffset = 0;\n\n    // Apply that snapshot.\n    this.applySnapshot(this.stack[this.stackOffset]);\n  }\n\n  /**\n  *  @method commit\n  *  Resets history stack, but keeps current editor's content.\n  */\n  commit() {\n    // Clear the stack.\n    this.stack = [];\n\n    // Restore stackOffset to its original value.\n    this.stackOffset = -1;\n\n    // Record our first snapshot (of nothing).\n    this.recordUndo();\n  }\n\n  /**\n  * @method reset\n  * Resets the history stack completely; reverting to an empty editor.\n  */\n  reset() {\n    // Clear the stack.\n    this.stack = [];\n\n    // Restore stackOffset to its original value.\n    this.stackOffset = -1;\n\n    // Clear the editable area.\n    this.$editable.html('');\n\n    // Record our first snapshot (of nothing).\n    this.recordUndo();\n  }\n\n  /**\n   * undo\n   */\n  undo() {\n    // Create snap shot if not yet recorded\n    if (this.$editable.html() !== this.stack[this.stackOffset].contents) {\n      this.recordUndo();\n    }\n\n    if (this.stackOffset > 0) {\n      this.stackOffset--;\n      this.applySnapshot(this.stack[this.stackOffset]);\n    }\n  }\n\n  /**\n   * redo\n   */\n  redo() {\n    if (this.stack.length - 1 > this.stackOffset) {\n      this.stackOffset++;\n      this.applySnapshot(this.stack[this.stackOffset]);\n    }\n  }\n\n  /**\n   * recorded undo\n   */\n  recordUndo() {\n    this.stackOffset++;\n\n    // Wash out stack after stackOffset\n    if (this.stack.length > this.stackOffset) {\n      this.stack = this.stack.slice(0, this.stackOffset);\n    }\n\n    // Create new snapshot and push it to the end\n    this.stack.push(this.makeSnapshot());\n\n    // If the stack size reachs to the limit, then slice it\n    if (this.stack.length > this.context.options.historyLimit) {\n      this.stack.shift();\n      this.stackOffset -= 1;\n    }\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\nexport default class Style {\n  /**\n   * @method jQueryCSS\n   *\n   * [workaround] for old jQuery\n   * passing an array of style properties to .css()\n   * will result in an object of property-value pairs.\n   * (compability with version < 1.9)\n   *\n   * @private\n   * @param  {jQuery} $obj\n   * @param  {Array} propertyNames - An array of one or more CSS properties.\n   * @return {Object}\n   */\n  jQueryCSS($obj, propertyNames) {\n    if (env.jqueryVersion < 1.9) {\n      const result = {};\n      $.each(propertyNames, (idx, propertyName) => {\n        result[propertyName] = $obj.css(propertyName);\n      });\n      return result;\n    }\n    return $obj.css(propertyNames);\n  }\n\n  /**\n   * returns style object from node\n   *\n   * @param {jQuery} $node\n   * @return {Object}\n   */\n  fromNode($node) {\n    const properties = ['font-family', 'font-size', 'text-align', 'list-style-type', 'line-height'];\n    const styleInfo = this.jQueryCSS($node, properties) || {};\n\n    const fontSize = $node[0].style.fontSize || styleInfo['font-size'];\n\n    styleInfo['font-size'] = parseInt(fontSize, 10);\n    styleInfo['font-size-unit'] = fontSize.match(/[a-z%]+$/);\n\n    return styleInfo;\n  }\n\n  /**\n   * paragraph level style\n   *\n   * @param {WrappedRange} rng\n   * @param {Object} styleInfo\n   */\n  stylePara(rng, styleInfo) {\n    $.each(rng.nodes(dom.isPara, {\n      includeAncestor: true,\n    }), (idx, para) => {\n      $(para).css(styleInfo);\n    });\n  }\n\n  /**\n   * insert and returns styleNodes on range.\n   *\n   * @param {WrappedRange} rng\n   * @param {Object} [options] - options for styleNodes\n   * @param {String} [options.nodeName] - default: `SPAN`\n   * @param {Boolean} [options.expandClosestSibling] - default: `false`\n   * @param {Boolean} [options.onlyPartialContains] - default: `false`\n   * @return {Node[]}\n   */\n  styleNodes(rng, options) {\n    rng = rng.splitText();\n\n    const nodeName = (options && options.nodeName) || 'SPAN';\n    const expandClosestSibling = !!(options && options.expandClosestSibling);\n    const onlyPartialContains = !!(options && options.onlyPartialContains);\n\n    if (rng.isCollapsed()) {\n      return [rng.insertNode(dom.create(nodeName))];\n    }\n\n    let pred = dom.makePredByNodeName(nodeName);\n    const nodes = rng.nodes(dom.isText, {\n      fullyContains: true,\n    }).map((text) => {\n      return dom.singleChildAncestor(text, pred) || dom.wrap(text, nodeName);\n    });\n\n    if (expandClosestSibling) {\n      if (onlyPartialContains) {\n        const nodesInRange = rng.nodes();\n        // compose with partial contains predication\n        pred = func.and(pred, (node) => {\n          return lists.contains(nodesInRange, node);\n        });\n      }\n\n      return nodes.map((node) => {\n        const siblings = dom.withClosestSiblings(node, pred);\n        const head = lists.head(siblings);\n        const tails = lists.tail(siblings);\n        $.each(tails, (idx, elem) => {\n          dom.appendChildNodes(head, elem.childNodes);\n          dom.remove(elem);\n        });\n        return lists.head(siblings);\n      });\n    } else {\n      return nodes;\n    }\n  }\n\n  /**\n   * get current style on cursor\n   *\n   * @param {WrappedRange} rng\n   * @return {Object} - object contains style properties.\n   */\n  current(rng) {\n    const $cont = $(!dom.isElement(rng.sc) ? rng.sc.parentNode : rng.sc);\n    let styleInfo = this.fromNode($cont);\n\n    // document.queryCommandState for toggle state\n    // [workaround] prevent Firefox nsresult: \"0x80004005 (NS_ERROR_FAILURE)\"\n    try {\n      styleInfo = $.extend(styleInfo, {\n        'font-bold': document.queryCommandState('bold') ? 'bold' : 'normal',\n        'font-italic': document.queryCommandState('italic') ? 'italic' : 'normal',\n        'font-underline': document.queryCommandState('underline') ? 'underline' : 'normal',\n        'font-subscript': document.queryCommandState('subscript') ? 'subscript' : 'normal',\n        'font-superscript': document.queryCommandState('superscript') ? 'superscript' : 'normal',\n        'font-strikethrough': document.queryCommandState('strikethrough') ? 'strikethrough' : 'normal',\n        'font-family': document.queryCommandValue('fontname') || styleInfo['font-family'],\n      });\n    } catch (e) {\n      // eslint-disable-next-line\n    }\n\n    // list-style-type to list-style(unordered, ordered)\n    if (!rng.isOnList()) {\n      styleInfo['list-style'] = 'none';\n    } else {\n      const orderedTypes = ['circle', 'disc', 'disc-leading-zero', 'square'];\n      const isUnordered = orderedTypes.indexOf(styleInfo['list-style-type']) > -1;\n      styleInfo['list-style'] = isUnordered ? 'unordered' : 'ordered';\n    }\n\n    const para = dom.ancestor(rng.sc, dom.isPara);\n    if (para && para.style['line-height']) {\n      styleInfo['line-height'] = para.style.lineHeight;\n    } else {\n      const lineHeight = parseInt(styleInfo['line-height'], 10) / parseInt(styleInfo['font-size'], 10);\n      styleInfo['line-height'] = lineHeight.toFixed(1);\n    }\n\n    styleInfo.anchor = rng.isOnAnchor() && dom.ancestor(rng.sc, dom.isAnchor);\n    styleInfo.ancestors = dom.listAncestor(rng.sc, dom.isEditable);\n    styleInfo.range = rng;\n\n    return styleInfo;\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport func from '../core/func';\nimport dom from '../core/dom';\nimport range from '../core/range';\n\nexport default class Bullet {\n  /**\n   * toggle ordered list\n   */\n  insertOrderedList(editable) {\n    this.toggleList('OL', editable);\n  }\n\n  /**\n   * toggle unordered list\n   */\n  insertUnorderedList(editable) {\n    this.toggleList('UL', editable);\n  }\n\n  /**\n   * indent\n   */\n  indent(editable) {\n    const rng = range.create(editable).wrapBodyInlineWithPara();\n\n    const paras = rng.nodes(dom.isPara, { includeAncestor: true });\n    const clustereds = lists.clusterBy(paras, func.peq2('parentNode'));\n\n    $.each(clustereds, (idx, paras) => {\n      const head = lists.head(paras);\n      if (dom.isLi(head)) {\n        const previousList = this.findList(head.previousSibling);\n        if (previousList) {\n          paras\n            .map(para => previousList.appendChild(para));\n        } else {\n          this.wrapList(paras, head.parentNode.nodeName);\n          paras\n            .map((para) => para.parentNode)\n            .map((para) => this.appendToPrevious(para));\n        }\n      } else {\n        $.each(paras, (idx, para) => {\n          $(para).css('marginLeft', (idx, val) => {\n            return (parseInt(val, 10) || 0) + 25;\n          });\n        });\n      }\n    });\n\n    rng.select();\n  }\n\n  /**\n   * outdent\n   */\n  outdent(editable) {\n    const rng = range.create(editable).wrapBodyInlineWithPara();\n\n    const paras = rng.nodes(dom.isPara, { includeAncestor: true });\n    const clustereds = lists.clusterBy(paras, func.peq2('parentNode'));\n\n    $.each(clustereds, (idx, paras) => {\n      const head = lists.head(paras);\n      if (dom.isLi(head)) {\n        this.releaseList([paras]);\n      } else {\n        $.each(paras, (idx, para) => {\n          $(para).css('marginLeft', (idx, val) => {\n            val = (parseInt(val, 10) || 0);\n            return val > 25 ? val - 25 : '';\n          });\n        });\n      }\n    });\n\n    rng.select();\n  }\n\n  /**\n   * toggle list\n   *\n   * @param {String} listName - OL or UL\n   */\n  toggleList(listName, editable) {\n    const rng = range.create(editable).wrapBodyInlineWithPara();\n\n    let paras = rng.nodes(dom.isPara, { includeAncestor: true });\n    const bookmark = rng.paraBookmark(paras);\n    const clustereds = lists.clusterBy(paras, func.peq2('parentNode'));\n\n    // paragraph to list\n    if (lists.find(paras, dom.isPurePara)) {\n      let wrappedParas = [];\n      $.each(clustereds, (idx, paras) => {\n        wrappedParas = wrappedParas.concat(this.wrapList(paras, listName));\n      });\n      paras = wrappedParas;\n    // list to paragraph or change list style\n    } else {\n      const diffLists = rng.nodes(dom.isList, {\n        includeAncestor: true,\n      }).filter((listNode) => {\n        return !$.nodeName(listNode, listName);\n      });\n\n      if (diffLists.length) {\n        $.each(diffLists, (idx, listNode) => {\n          dom.replace(listNode, listName);\n        });\n      } else {\n        paras = this.releaseList(clustereds, true);\n      }\n    }\n\n    range.createFromParaBookmark(bookmark, paras).select();\n  }\n\n  /**\n   * @param {Node[]} paras\n   * @param {String} listName\n   * @return {Node[]}\n   */\n  wrapList(paras, listName) {\n    const head = lists.head(paras);\n    const last = lists.last(paras);\n\n    const prevList = dom.isList(head.previousSibling) && head.previousSibling;\n    const nextList = dom.isList(last.nextSibling) && last.nextSibling;\n\n    const listNode = prevList || dom.insertAfter(dom.create(listName || 'UL'), last);\n\n    // P to LI\n    paras = paras.map((para) => {\n      return dom.isPurePara(para) ? dom.replace(para, 'LI') : para;\n    });\n\n    // append to list(<ul>, <ol>)\n    dom.appendChildNodes(listNode, paras);\n\n    if (nextList) {\n      dom.appendChildNodes(listNode, lists.from(nextList.childNodes));\n      dom.remove(nextList);\n    }\n\n    return paras;\n  }\n\n  /**\n   * @method releaseList\n   *\n   * @param {Array[]} clustereds\n   * @param {Boolean} isEscapseToBody\n   * @return {Node[]}\n   */\n  releaseList(clustereds, isEscapseToBody) {\n    let releasedParas = [];\n\n    $.each(clustereds, (idx, paras) => {\n      const head = lists.head(paras);\n      const last = lists.last(paras);\n\n      const headList = isEscapseToBody ? dom.lastAncestor(head, dom.isList) : head.parentNode;\n      const parentItem = headList.parentNode;\n\n      if (headList.parentNode.nodeName === 'LI') {\n        paras.map(para => {\n          const newList = this.findNextSiblings(para);\n\n          if (parentItem.nextSibling) {\n            parentItem.parentNode.insertBefore(\n              para,\n              parentItem.nextSibling\n            );\n          } else {\n            parentItem.parentNode.appendChild(para);\n          }\n\n          if (newList.length) {\n            this.wrapList(newList, headList.nodeName);\n            para.appendChild(newList[0].parentNode);\n          }\n        });\n\n        if (headList.children.length === 0) {\n          parentItem.removeChild(headList);\n        }\n\n        if (parentItem.childNodes.length === 0) {\n          parentItem.parentNode.removeChild(parentItem);\n        }\n      } else {\n        const lastList = headList.childNodes.length > 1 ? dom.splitTree(headList, {\n          node: last.parentNode,\n          offset: dom.position(last) + 1,\n        }, {\n          isSkipPaddingBlankHTML: true,\n        }) : null;\n\n        const middleList = dom.splitTree(headList, {\n          node: head.parentNode,\n          offset: dom.position(head),\n        }, {\n          isSkipPaddingBlankHTML: true,\n        });\n\n        paras = isEscapseToBody ? dom.listDescendant(middleList, dom.isLi)\n          : lists.from(middleList.childNodes).filter(dom.isLi);\n\n        // LI to P\n        if (isEscapseToBody || !dom.isList(headList.parentNode)) {\n          paras = paras.map((para) => {\n            return dom.replace(para, 'P');\n          });\n        }\n\n        $.each(lists.from(paras).reverse(), (idx, para) => {\n          dom.insertAfter(para, headList);\n        });\n\n        // remove empty lists\n        const rootLists = lists.compact([headList, middleList, lastList]);\n        $.each(rootLists, (idx, rootList) => {\n          const listNodes = [rootList].concat(dom.listDescendant(rootList, dom.isList));\n          $.each(listNodes.reverse(), (idx, listNode) => {\n            if (!dom.nodeLength(listNode)) {\n              dom.remove(listNode, true);\n            }\n          });\n        });\n      }\n\n      releasedParas = releasedParas.concat(paras);\n    });\n\n    return releasedParas;\n  }\n\n  /**\n   * @method appendToPrevious\n   *\n   * Appends list to previous list item, if\n   * none exist it wraps the list in a new list item.\n   *\n   * @param {HTMLNode} ListItem\n   * @return {HTMLNode}\n   */\n  appendToPrevious(node) {\n    return node.previousSibling\n      ? dom.appendChildNodes(node.previousSibling, [node])\n      : this.wrapList([node], 'LI');\n  }\n\n  /**\n   * @method findList\n   *\n   * Finds an existing list in list item\n   *\n   * @param {HTMLNode} ListItem\n   * @return {Array[]}\n   */\n  findList(node) {\n    return node\n      ? lists.find(node.children, child => ['OL', 'UL'].indexOf(child.nodeName) > -1)\n      : null;\n  }\n\n  /**\n   * @method findNextSiblings\n   *\n   * Finds all list item siblings that follow it\n   *\n   * @param {HTMLNode} ListItem\n   * @return {HTMLNode}\n   */\n  findNextSiblings(node) {\n    const siblings = [];\n    while (node.nextSibling) {\n      siblings.push(node.nextSibling);\n      node = node.nextSibling;\n    }\n    return siblings;\n  }\n}\n", "import $ from 'jquery';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport Bullet from '../editing/Bullet';\n\n/**\n * @class editing.Typing\n *\n * Typing\n *\n */\nexport default class Typing {\n  constructor(context) {\n    // a Bullet instance to toggle lists off\n    this.bullet = new Bullet();\n    this.options = context.options;\n  }\n\n  /**\n   * insert tab\n   *\n   * @param {WrappedRange} rng\n   * @param {Number} tabsize\n   */\n  insertTab(rng, tabsize) {\n    const tab = dom.createText(new Array(tabsize + 1).join(dom.NBSP_CHAR));\n    rng = rng.deleteContents();\n    rng.insertNode(tab, true);\n\n    rng = range.create(tab, tabsize);\n    rng.select();\n  }\n\n  /**\n   * insert paragraph\n   *\n   * @param {jQuery} $editable\n   * @param {WrappedRange} rng Can be used in unit tests to \"mock\" the range\n   *\n   * blockquoteBreakingLevel\n   *   0 - No break, the new paragraph remains inside the quote\n   *   1 - Break the first blockquote in the ancestors list\n   *   2 - Break all blockquotes, so that the new paragraph is not quoted (this is the default)\n   */\n  insertParagraph(editable, rng) {\n    rng = rng || range.create(editable);\n\n    // deleteContents on range.\n    rng = rng.deleteContents();\n\n    // Wrap range if it needs to be wrapped by paragraph\n    rng = rng.wrapBodyInlineWithPara();\n\n    // finding paragraph\n    const splitRoot = dom.ancestor(rng.sc, dom.isPara);\n\n    let nextPara;\n    // on paragraph: split paragraph\n    if (splitRoot) {\n      // if it is an empty line with li\n      if (dom.isLi(splitRoot) && (dom.isEmpty(splitRoot) || dom.deepestChildIsEmpty(splitRoot))) {\n        // toggle UL/OL and escape\n        this.bullet.toggleList(splitRoot.parentNode.nodeName);\n        return;\n      } else {\n        let blockquote = null;\n        if (this.options.blockquoteBreakingLevel === 1) {\n          blockquote = dom.ancestor(splitRoot, dom.isBlockquote);\n        } else if (this.options.blockquoteBreakingLevel === 2) {\n          blockquote = dom.lastAncestor(splitRoot, dom.isBlockquote);\n        }\n\n        if (blockquote) {\n          // We're inside a blockquote and options ask us to break it\n          nextPara = $(dom.emptyPara)[0];\n          // If the split is right before a <br>, remove it so that there's no \"empty line\"\n          // after the split in the new blockquote created\n          if (dom.isRightEdgePoint(rng.getStartPoint()) && dom.isBR(rng.sc.nextSibling)) {\n            $(rng.sc.nextSibling).remove();\n          }\n          const split = dom.splitTree(blockquote, rng.getStartPoint(), { isDiscardEmptySplits: true });\n          if (split) {\n            split.parentNode.insertBefore(nextPara, split);\n          } else {\n            dom.insertAfter(nextPara, blockquote); // There's no split if we were at the end of the blockquote\n          }\n        } else {\n          nextPara = dom.splitTree(splitRoot, rng.getStartPoint());\n\n          // not a blockquote, just insert the paragraph\n          let emptyAnchors = dom.listDescendant(splitRoot, dom.isEmptyAnchor);\n          emptyAnchors = emptyAnchors.concat(dom.listDescendant(nextPara, dom.isEmptyAnchor));\n\n          $.each(emptyAnchors, (idx, anchor) => {\n            dom.remove(anchor);\n          });\n\n          // replace empty heading, pre or custom-made styleTag with P tag\n          if ((dom.isHeading(nextPara) || dom.isPre(nextPara) || dom.isCustomStyleTag(nextPara)) && dom.isEmpty(nextPara)) {\n            nextPara = dom.replace(nextPara, 'p');\n          }\n        }\n      }\n    // no paragraph: insert empty paragraph\n    } else {\n      const next = rng.sc.childNodes[rng.so];\n      nextPara = $(dom.emptyPara)[0];\n      if (next) {\n        rng.sc.insertBefore(nextPara, next);\n      } else {\n        rng.sc.appendChild(nextPara);\n      }\n    }\n\n    range.create(nextPara, 0).normalize().select().scrollIntoView(editable);\n  }\n}\n", "import $ from 'jquery';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport lists from '../core/lists';\n\n/**\n * @class Create a virtual table to create what actions to do in change.\n * @param {object} startPoint Cell selected to apply change.\n * @param {enum} where  Where change will be applied Row or Col. Use enum: TableResultAction.where\n * @param {enum} action Action to be applied. Use enum: TableResultAction.requestAction\n * @param {object} domTable Dom element of table to make changes.\n */\nconst TableResultAction = function(startPoint, where, action, domTable) {\n  const _startPoint = { 'colPos': 0, 'rowPos': 0 };\n  const _virtualTable = [];\n  const _actionCellList = [];\n\n  /// ///////////////////////////////////////////\n  // Private functions\n  /// ///////////////////////////////////////////\n\n  /**\n   * Set the startPoint of action.\n   */\n  function setStartPoint() {\n    if (!startPoint || !startPoint.tagName || (startPoint.tagName.toLowerCase() !== 'td' && startPoint.tagName.toLowerCase() !== 'th')) {\n      // Impossible to identify start Cell point\n      return;\n    }\n    _startPoint.colPos = startPoint.cellIndex;\n    if (!startPoint.parentElement || !startPoint.parentElement.tagName || startPoint.parentElement.tagName.toLowerCase() !== 'tr') {\n      // Impossible to identify start Row point\n      return;\n    }\n    _startPoint.rowPos = startPoint.parentElement.rowIndex;\n  }\n\n  /**\n   * Define virtual table position info object.\n   *\n   * @param {int} rowIndex Index position in line of virtual table.\n   * @param {int} cellIndex Index position in column of virtual table.\n   * @param {object} baseRow Row affected by this position.\n   * @param {object} baseCell Cell affected by this position.\n   * @param {bool} isSpan Inform if it is an span cell/row.\n   */\n  function setVirtualTablePosition(rowIndex, cellIndex, baseRow, baseCell, isRowSpan, isColSpan, isVirtualCell) {\n    const objPosition = {\n      'baseRow': baseRow,\n      'baseCell': baseCell,\n      'isRowSpan': isRowSpan,\n      'isColSpan': isColSpan,\n      'isVirtual': isVirtualCell,\n    };\n    if (!_virtualTable[rowIndex]) {\n      _virtualTable[rowIndex] = [];\n    }\n    _virtualTable[rowIndex][cellIndex] = objPosition;\n  }\n\n  /**\n   * Create action cell object.\n   *\n   * @param {object} virtualTableCellObj Object of specific position on virtual table.\n   * @param {enum} resultAction Action to be applied in that item.\n   */\n  function getActionCell(virtualTableCellObj, resultAction, virtualRowPosition, virtualColPosition) {\n    return {\n      'baseCell': virtualTableCellObj.baseCell,\n      'action': resultAction,\n      'virtualTable': {\n        'rowIndex': virtualRowPosition,\n        'cellIndex': virtualColPosition,\n      },\n    };\n  }\n\n  /**\n   * Recover free index of row to append Cell.\n   *\n   * @param {int} rowIndex Index of row to find free space.\n   * @param {int} cellIndex Index of cell to find free space in table.\n   */\n  function recoverCellIndex(rowIndex, cellIndex) {\n    if (!_virtualTable[rowIndex]) {\n      return cellIndex;\n    }\n    if (!_virtualTable[rowIndex][cellIndex]) {\n      return cellIndex;\n    }\n\n    let newCellIndex = cellIndex;\n    while (_virtualTable[rowIndex][newCellIndex]) {\n      newCellIndex++;\n      if (!_virtualTable[rowIndex][newCellIndex]) {\n        return newCellIndex;\n      }\n    }\n  }\n\n  /**\n   * Recover info about row and cell and add information to virtual table.\n   *\n   * @param {object} row Row to recover information.\n   * @param {object} cell Cell to recover information.\n   */\n  function addCellInfoToVirtual(row, cell) {\n    const cellIndex = recoverCellIndex(row.rowIndex, cell.cellIndex);\n    const cellHasColspan = (cell.colSpan > 1);\n    const cellHasRowspan = (cell.rowSpan > 1);\n    const isThisSelectedCell = (row.rowIndex === _startPoint.rowPos && cell.cellIndex === _startPoint.colPos);\n    setVirtualTablePosition(row.rowIndex, cellIndex, row, cell, cellHasRowspan, cellHasColspan, false);\n\n    // Add span rows to virtual Table.\n    const rowspanNumber = cell.attributes.rowSpan ? parseInt(cell.attributes.rowSpan.value, 10) : 0;\n    if (rowspanNumber > 1) {\n      for (let rp = 1; rp < rowspanNumber; rp++) {\n        const rowspanIndex = row.rowIndex + rp;\n        adjustStartPoint(rowspanIndex, cellIndex, cell, isThisSelectedCell);\n        setVirtualTablePosition(rowspanIndex, cellIndex, row, cell, true, cellHasColspan, true);\n      }\n    }\n\n    // Add span cols to virtual table.\n    const colspanNumber = cell.attributes.colSpan ? parseInt(cell.attributes.colSpan.value, 10) : 0;\n    if (colspanNumber > 1) {\n      for (let cp = 1; cp < colspanNumber; cp++) {\n        const cellspanIndex = recoverCellIndex(row.rowIndex, (cellIndex + cp));\n        adjustStartPoint(row.rowIndex, cellspanIndex, cell, isThisSelectedCell);\n        setVirtualTablePosition(row.rowIndex, cellspanIndex, row, cell, cellHasRowspan, true, true);\n      }\n    }\n  }\n\n  /**\n   * Process validation and adjust of start point if needed\n   *\n   * @param {int} rowIndex\n   * @param {int} cellIndex\n   * @param {object} cell\n   * @param {bool} isSelectedCell\n   */\n  function adjustStartPoint(rowIndex, cellIndex, cell, isSelectedCell) {\n    if (rowIndex === _startPoint.rowPos && _startPoint.colPos >= cell.cellIndex && cell.cellIndex <= cellIndex && !isSelectedCell) {\n      _startPoint.colPos++;\n    }\n  }\n\n  /**\n   * Create virtual table of cells with all cells, including span cells.\n   */\n  function createVirtualTable() {\n    const rows = domTable.rows;\n    for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n      const cells = rows[rowIndex].cells;\n      for (let cellIndex = 0; cellIndex < cells.length; cellIndex++) {\n        addCellInfoToVirtual(rows[rowIndex], cells[cellIndex]);\n      }\n    }\n  }\n\n  /**\n   * Get action to be applied on the cell.\n   *\n   * @param {object} cell virtual table cell to apply action\n   */\n  function getDeleteResultActionToCell(cell) {\n    switch (where) {\n      case TableResultAction.where.Column:\n        if (cell.isColSpan) {\n          return TableResultAction.resultAction.SubtractSpanCount;\n        }\n        break;\n      case TableResultAction.where.Row:\n        if (!cell.isVirtual && cell.isRowSpan) {\n          return TableResultAction.resultAction.AddCell;\n        } else if (cell.isRowSpan) {\n          return TableResultAction.resultAction.SubtractSpanCount;\n        }\n        break;\n    }\n    return TableResultAction.resultAction.RemoveCell;\n  }\n\n  /**\n   * Get action to be applied on the cell.\n   *\n   * @param {object} cell virtual table cell to apply action\n   */\n  function getAddResultActionToCell(cell) {\n    switch (where) {\n      case TableResultAction.where.Column:\n        if (cell.isColSpan) {\n          return TableResultAction.resultAction.SumSpanCount;\n        } else if (cell.isRowSpan && cell.isVirtual) {\n          return TableResultAction.resultAction.Ignore;\n        }\n        break;\n      case TableResultAction.where.Row:\n        if (cell.isRowSpan) {\n          return TableResultAction.resultAction.SumSpanCount;\n        } else if (cell.isColSpan && cell.isVirtual) {\n          return TableResultAction.resultAction.Ignore;\n        }\n        break;\n    }\n    return TableResultAction.resultAction.AddCell;\n  }\n\n  function init() {\n    setStartPoint();\n    createVirtualTable();\n  }\n\n  /// ///////////////////////////////////////////\n  // Public functions\n  /// ///////////////////////////////////////////\n\n  /**\n   * Recover array os what to do in table.\n   */\n  this.getActionList = function() {\n    const fixedRow = (where === TableResultAction.where.Row) ? _startPoint.rowPos : -1;\n    const fixedCol = (where === TableResultAction.where.Column) ? _startPoint.colPos : -1;\n\n    let actualPosition = 0;\n    let canContinue = true;\n    while (canContinue) {\n      const rowPosition = (fixedRow >= 0) ? fixedRow : actualPosition;\n      const colPosition = (fixedCol >= 0) ? fixedCol : actualPosition;\n      const row = _virtualTable[rowPosition];\n      if (!row) {\n        canContinue = false;\n        return _actionCellList;\n      }\n      const cell = row[colPosition];\n      if (!cell) {\n        canContinue = false;\n        return _actionCellList;\n      }\n\n      // Define action to be applied in this cell\n      let resultAction = TableResultAction.resultAction.Ignore;\n      switch (action) {\n        case TableResultAction.requestAction.Add:\n          resultAction = getAddResultActionToCell(cell);\n          break;\n        case TableResultAction.requestAction.Delete:\n          resultAction = getDeleteResultActionToCell(cell);\n          break;\n      }\n      _actionCellList.push(getActionCell(cell, resultAction, rowPosition, colPosition));\n      actualPosition++;\n    }\n\n    return _actionCellList;\n  };\n\n  init();\n};\n/**\n*\n* Where action occours enum.\n*/\nTableResultAction.where = { 'Row': 0, 'Column': 1 };\n/**\n*\n* Requested action to apply enum.\n*/\nTableResultAction.requestAction = { 'Add': 0, 'Delete': 1 };\n/**\n*\n* Result action to be executed enum.\n*/\nTableResultAction.resultAction = { 'Ignore': 0, 'SubtractSpanCount': 1, 'RemoveCell': 2, 'AddCell': 3, 'SumSpanCount': 4 };\n\n/**\n *\n * @class editing.Table\n *\n * Table\n *\n */\nexport default class Table {\n  /**\n   * handle tab key\n   *\n   * @param {WrappedRange} rng\n   * @param {Boolean} isShift\n   */\n  tab(rng, isShift) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const table = dom.ancestor(cell, dom.isTable);\n    const cells = dom.listDescendant(table, dom.isCell);\n\n    const nextCell = lists[isShift ? 'prev' : 'next'](cells, cell);\n    if (nextCell) {\n      range.create(nextCell, 0).select();\n    }\n  }\n\n  /**\n   * Add a new row\n   *\n   * @param {WrappedRange} rng\n   * @param {String} position (top/bottom)\n   * @return {Node}\n   */\n  addRow(rng, position) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n\n    const currentTr = $(cell).closest('tr');\n    const trAttributes = this.recoverAttributes(currentTr);\n    const html = $('<tr' + trAttributes + '></tr>');\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Row,\n      TableResultAction.requestAction.Add, $(currentTr).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let idCell = 0; idCell < actions.length; idCell++) {\n      const currentCell = actions[idCell];\n      const tdAttributes = this.recoverAttributes(currentCell.baseCell);\n      switch (currentCell.action) {\n        case TableResultAction.resultAction.AddCell:\n          html.append('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          break;\n        case TableResultAction.resultAction.SumSpanCount:\n          {\n            if (position === 'top') {\n              const baseCellTr = currentCell.baseCell.parent;\n              const isTopFromRowSpan = (!baseCellTr ? 0 : currentCell.baseCell.closest('tr').rowIndex) <= currentTr[0].rowIndex;\n              if (isTopFromRowSpan) {\n                const newTd = $('<div></div>').append($('<td' + tdAttributes + '>' + dom.blank + '</td>').removeAttr('rowspan')).html();\n                html.append(newTd);\n                break;\n              }\n            }\n            let rowspanNumber = parseInt(currentCell.baseCell.rowSpan, 10);\n            rowspanNumber++;\n            currentCell.baseCell.setAttribute('rowSpan', rowspanNumber);\n          }\n          break;\n      }\n    }\n\n    if (position === 'top') {\n      currentTr.before(html);\n    } else {\n      const cellHasRowspan = (cell.rowSpan > 1);\n      if (cellHasRowspan) {\n        const lastTrIndex = currentTr[0].rowIndex + (cell.rowSpan - 2);\n        $($(currentTr).parent().find('tr')[lastTrIndex]).after($(html));\n        return;\n      }\n      currentTr.after(html);\n    }\n  }\n\n  /**\n   * Add a new col\n   *\n   * @param {WrappedRange} rng\n   * @param {String} position (left/right)\n   * @return {Node}\n   */\n  addCol(rng, position) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const row = $(cell).closest('tr');\n    const rowsGroup = $(row).siblings();\n    rowsGroup.push(row);\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Column,\n      TableResultAction.requestAction.Add, $(row).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {\n      const currentCell = actions[actionIndex];\n      const tdAttributes = this.recoverAttributes(currentCell.baseCell);\n      switch (currentCell.action) {\n        case TableResultAction.resultAction.AddCell:\n          if (position === 'right') {\n            $(currentCell.baseCell).after('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          } else {\n            $(currentCell.baseCell).before('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          }\n          break;\n        case TableResultAction.resultAction.SumSpanCount:\n          if (position === 'right') {\n            let colspanNumber = parseInt(currentCell.baseCell.colSpan, 10);\n            colspanNumber++;\n            currentCell.baseCell.setAttribute('colSpan', colspanNumber);\n          } else {\n            $(currentCell.baseCell).before('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          }\n          break;\n      }\n    }\n  }\n\n  /*\n  * Copy attributes from element.\n  *\n  * @param {object} Element to recover attributes.\n  * @return {string} Copied string elements.\n  */\n  recoverAttributes(el) {\n    let resultStr = '';\n\n    if (!el) {\n      return resultStr;\n    }\n\n    const attrList = el.attributes || [];\n\n    for (let i = 0; i < attrList.length; i++) {\n      if (attrList[i].name.toLowerCase() === 'id') {\n        continue;\n      }\n\n      if (attrList[i].specified) {\n        resultStr += ' ' + attrList[i].name + '=\\'' + attrList[i].value + '\\'';\n      }\n    }\n\n    return resultStr;\n  }\n\n  /**\n   * Delete current row\n   *\n   * @param {WrappedRange} rng\n   * @return {Node}\n   */\n  deleteRow(rng) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const row = $(cell).closest('tr');\n    const cellPos = row.children('td, th').index($(cell));\n    const rowPos = row[0].rowIndex;\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Row,\n      TableResultAction.requestAction.Delete, $(row).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {\n      if (!actions[actionIndex]) {\n        continue;\n      }\n\n      const baseCell = actions[actionIndex].baseCell;\n      const virtualPosition = actions[actionIndex].virtualTable;\n      const hasRowspan = (baseCell.rowSpan && baseCell.rowSpan > 1);\n      let rowspanNumber = (hasRowspan) ? parseInt(baseCell.rowSpan, 10) : 0;\n      switch (actions[actionIndex].action) {\n        case TableResultAction.resultAction.Ignore:\n          continue;\n        case TableResultAction.resultAction.AddCell:\n          {\n            const nextRow = row.next('tr')[0];\n            if (!nextRow) { continue; }\n            const cloneRow = row[0].cells[cellPos];\n            if (hasRowspan) {\n              if (rowspanNumber > 2) {\n                rowspanNumber--;\n                nextRow.insertBefore(cloneRow, nextRow.cells[cellPos]);\n                nextRow.cells[cellPos].setAttribute('rowSpan', rowspanNumber);\n                nextRow.cells[cellPos].innerHTML = '';\n              } else if (rowspanNumber === 2) {\n                nextRow.insertBefore(cloneRow, nextRow.cells[cellPos]);\n                nextRow.cells[cellPos].removeAttribute('rowSpan');\n                nextRow.cells[cellPos].innerHTML = '';\n              }\n            }\n          }\n          continue;\n        case TableResultAction.resultAction.SubtractSpanCount:\n          if (hasRowspan) {\n            if (rowspanNumber > 2) {\n              rowspanNumber--;\n              baseCell.setAttribute('rowSpan', rowspanNumber);\n              if (virtualPosition.rowIndex !== rowPos && baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n            } else if (rowspanNumber === 2) {\n              baseCell.removeAttribute('rowSpan');\n              if (virtualPosition.rowIndex !== rowPos && baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n            }\n          }\n          continue;\n        case TableResultAction.resultAction.RemoveCell:\n          // Do not need remove cell because row will be deleted.\n          continue;\n      }\n    }\n    row.remove();\n  }\n\n  /**\n   * Delete current col\n   *\n   * @param {WrappedRange} rng\n   * @return {Node}\n   */\n  deleteCol(rng) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const row = $(cell).closest('tr');\n    const cellPos = row.children('td, th').index($(cell));\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Column,\n      TableResultAction.requestAction.Delete, $(row).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {\n      if (!actions[actionIndex]) {\n        continue;\n      }\n      switch (actions[actionIndex].action) {\n        case TableResultAction.resultAction.Ignore:\n          continue;\n        case TableResultAction.resultAction.SubtractSpanCount:\n          {\n            const baseCell = actions[actionIndex].baseCell;\n            const hasColspan = (baseCell.colSpan && baseCell.colSpan > 1);\n            if (hasColspan) {\n              let colspanNumber = (baseCell.colSpan) ? parseInt(baseCell.colSpan, 10) : 0;\n              if (colspanNumber > 2) {\n                colspanNumber--;\n                baseCell.setAttribute('colSpan', colspanNumber);\n                if (baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n              } else if (colspanNumber === 2) {\n                baseCell.removeAttribute('colSpan');\n                if (baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n              }\n            }\n          }\n          continue;\n        case TableResultAction.resultAction.RemoveCell:\n          dom.remove(actions[actionIndex].baseCell, true);\n          continue;\n      }\n    }\n  }\n\n  /**\n   * create empty table element\n   *\n   * @param {Number} rowCount\n   * @param {Number} colCount\n   * @return {Node}\n   */\n  createTable(colCount, rowCount, options) {\n    const tds = [];\n    let tdHTML;\n    for (let idxCol = 0; idxCol < colCount; idxCol++) {\n      tds.push('<td>' + dom.blank + '</td>');\n    }\n    tdHTML = tds.join('');\n\n    const trs = [];\n    let trHTML;\n    for (let idxRow = 0; idxRow < rowCount; idxRow++) {\n      trs.push('<tr>' + tdHTML + '</tr>');\n    }\n    trHTML = trs.join('');\n    const $table = $('<table>' + trHTML + '</table>');\n    if (options && options.tableClassName) {\n      $table.addClass(options.tableClassName);\n    }\n\n    return $table[0];\n  }\n\n  /**\n   * Delete current table\n   *\n   * @param {WrappedRange} rng\n   * @return {Node}\n   */\n  deleteTable(rng) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    $(cell).closest('table').remove();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport { readFileAsDataURL, createImage } from '../core/async';\nimport History from '../editing/History';\nimport Style from '../editing/Style';\nimport Typing from '../editing/Typing';\nimport Table from '../editing/Table';\nimport Bullet from '../editing/Bullet';\n\nconst KEY_BOGUS = 'bogus';\n\n/**\n * @class Editor\n */\nexport default class Editor {\n  constructor(context) {\n    this.context = context;\n\n    this.$note = context.layoutInfo.note;\n    this.$editor = context.layoutInfo.editor;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n\n    this.editable = this.$editable[0];\n    this.lastRange = null;\n    this.snapshot = null;\n\n    this.style = new Style();\n    this.table = new Table();\n    this.typing = new Typing(context);\n    this.bullet = new Bullet();\n    this.history = new History(context);\n\n    this.context.memo('help.escape', this.lang.help.escape);\n    this.context.memo('help.undo', this.lang.help.undo);\n    this.context.memo('help.redo', this.lang.help.redo);\n    this.context.memo('help.tab', this.lang.help.tab);\n    this.context.memo('help.untab', this.lang.help.untab);\n    this.context.memo('help.insertParagraph', this.lang.help.insertParagraph);\n    this.context.memo('help.insertOrderedList', this.lang.help.insertOrderedList);\n    this.context.memo('help.insertUnorderedList', this.lang.help.insertUnorderedList);\n    this.context.memo('help.indent', this.lang.help.indent);\n    this.context.memo('help.outdent', this.lang.help.outdent);\n    this.context.memo('help.formatPara', this.lang.help.formatPara);\n    this.context.memo('help.insertHorizontalRule', this.lang.help.insertHorizontalRule);\n    this.context.memo('help.fontName', this.lang.help.fontName);\n\n    // native commands(with execCommand), generate function for execCommand\n    const commands = [\n      'bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript',\n      'justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull',\n      'formatBlock', 'removeFormat', 'backColor',\n    ];\n\n    for (let idx = 0, len = commands.length; idx < len; idx++) {\n      this[commands[idx]] = ((sCmd) => {\n        return (value) => {\n          this.beforeCommand();\n          document.execCommand(sCmd, false, value);\n          this.afterCommand(true);\n        };\n      })(commands[idx]);\n      this.context.memo('help.' + commands[idx], this.lang.help[commands[idx]]);\n    }\n\n    this.fontName = this.wrapCommand((value) => {\n      return this.fontStyling('font-family', env.validFontName(value));\n    });\n\n    this.fontSize = this.wrapCommand((value) => {\n      const unit = this.currentStyle()['font-size-unit'];\n      return this.fontStyling('font-size', value + unit);\n    });\n\n    this.fontSizeUnit = this.wrapCommand((value) => {\n      const size = this.currentStyle()['font-size'];\n      return this.fontStyling('font-size', size + value);\n    });\n\n    for (let idx = 1; idx <= 6; idx++) {\n      this['formatH' + idx] = ((idx) => {\n        return () => {\n          this.formatBlock('H' + idx);\n        };\n      })(idx);\n      this.context.memo('help.formatH' + idx, this.lang.help['formatH' + idx]);\n    }\n\n    this.insertParagraph = this.wrapCommand(() => {\n      this.typing.insertParagraph(this.editable);\n    });\n\n    this.insertOrderedList = this.wrapCommand(() => {\n      this.bullet.insertOrderedList(this.editable);\n    });\n\n    this.insertUnorderedList = this.wrapCommand(() => {\n      this.bullet.insertUnorderedList(this.editable);\n    });\n\n    this.indent = this.wrapCommand(() => {\n      this.bullet.indent(this.editable);\n    });\n\n    this.outdent = this.wrapCommand(() => {\n      this.bullet.outdent(this.editable);\n    });\n\n    /**\n     * insertNode\n     * insert node\n     * @param {Node} node\n     */\n    this.insertNode = this.wrapCommand((node) => {\n      if (this.isLimited($(node).text().length)) {\n        return;\n      }\n      const rng = this.getLastRange();\n      rng.insertNode(node);\n      this.setLastRange(range.createFromNodeAfter(node).select());\n    });\n\n    /**\n     * insert text\n     * @param {String} text\n     */\n    this.insertText = this.wrapCommand((text) => {\n      if (this.isLimited(text.length)) {\n        return;\n      }\n      const rng = this.getLastRange();\n      const textNode = rng.insertNode(dom.createText(text));\n      this.setLastRange(range.create(textNode, dom.nodeLength(textNode)).select());\n    });\n\n    /**\n     * paste HTML\n     * @param {String} markup\n     */\n    this.pasteHTML = this.wrapCommand((markup) => {\n      if (this.isLimited(markup.length)) {\n        return;\n      }\n      markup = this.context.invoke('codeview.purify', markup);\n      const contents = this.getLastRange().pasteHTML(markup);\n      this.setLastRange(range.createFromNodeAfter(lists.last(contents)).select());\n    });\n\n    /**\n     * formatBlock\n     *\n     * @param {String} tagName\n     */\n    this.formatBlock = this.wrapCommand((tagName, $target) => {\n      const onApplyCustomStyle = this.options.callbacks.onApplyCustomStyle;\n      if (onApplyCustomStyle) {\n        onApplyCustomStyle.call(this, $target, this.context, this.onFormatBlock);\n      } else {\n        this.onFormatBlock(tagName, $target);\n      }\n    });\n\n    /**\n     * insert horizontal rule\n     */\n    this.insertHorizontalRule = this.wrapCommand(() => {\n      const hrNode = this.getLastRange().insertNode(dom.create('HR'));\n      if (hrNode.nextSibling) {\n        this.setLastRange(range.create(hrNode.nextSibling, 0).normalize().select());\n      }\n    });\n\n    /**\n     * lineHeight\n     * @param {String} value\n     */\n    this.lineHeight = this.wrapCommand((value) => {\n      this.style.stylePara(this.getLastRange(), {\n        lineHeight: value,\n      });\n    });\n\n    /**\n     * create link (command)\n     *\n     * @param {Object} linkInfo\n     */\n    this.createLink = this.wrapCommand((linkInfo) => {\n      let linkUrl = linkInfo.url;\n      const linkText = linkInfo.text;\n      const isNewWindow = linkInfo.isNewWindow;\n      const checkProtocol = linkInfo.checkProtocol;\n      let rng = linkInfo.range || this.getLastRange();\n      const additionalTextLength = linkText.length - rng.toString().length;\n      if (additionalTextLength > 0 && this.isLimited(additionalTextLength)) {\n        return;\n      }\n      const isTextChanged = rng.toString() !== linkText;\n\n      // handle spaced urls from input\n      if (typeof linkUrl === 'string') {\n        linkUrl = linkUrl.trim();\n      }\n\n      if (this.options.onCreateLink) {\n        linkUrl = this.options.onCreateLink(linkUrl);\n      } else if (checkProtocol) {\n        // if url doesn't have any protocol and not even a relative or a label, use http:// as default\n        linkUrl = /^([A-Za-z][A-Za-z0-9+-.]*\\:|#|\\/)/.test(linkUrl)\n          ? linkUrl : this.options.defaultProtocol + linkUrl;\n      }\n\n      let anchors = [];\n      if (isTextChanged) {\n        rng = rng.deleteContents();\n        const anchor = rng.insertNode($('<A>' + linkText + '</A>')[0]);\n        anchors.push(anchor);\n      } else {\n        anchors = this.style.styleNodes(rng, {\n          nodeName: 'A',\n          expandClosestSibling: true,\n          onlyPartialContains: true,\n        });\n      }\n\n      $.each(anchors, (idx, anchor) => {\n        $(anchor).attr('href', linkUrl);\n        if (isNewWindow) {\n          $(anchor).attr('target', '_blank');\n        } else {\n          $(anchor).removeAttr('target');\n        }\n      });\n\n      this.setLastRange(\n        this.createRangeFromList(anchors).select()\n      );\n    });\n\n    /**\n     * setting color\n     *\n     * @param {Object} sObjColor  color code\n     * @param {String} sObjColor.foreColor foreground color\n     * @param {String} sObjColor.backColor background color\n     */\n    this.color = this.wrapCommand((colorInfo) => {\n      const foreColor = colorInfo.foreColor;\n      const backColor = colorInfo.backColor;\n\n      if (foreColor) { document.execCommand('foreColor', false, foreColor); }\n      if (backColor) { document.execCommand('backColor', false, backColor); }\n    });\n\n    /**\n     * Set foreground color\n     *\n     * @param {String} colorCode foreground color code\n     */\n    this.foreColor = this.wrapCommand((colorInfo) => {\n      document.execCommand('foreColor', false, colorInfo);\n    });\n\n    /**\n     * insert Table\n     *\n     * @param {String} dimension of table (ex : \"5x5\")\n     */\n    this.insertTable = this.wrapCommand((dim) => {\n      const dimension = dim.split('x');\n\n      const rng = this.getLastRange().deleteContents();\n      rng.insertNode(this.table.createTable(dimension[0], dimension[1], this.options));\n    });\n\n    /**\n     * remove media object and Figure Elements if media object is img with Figure.\n     */\n    this.removeMedia = this.wrapCommand(() => {\n      let $target = $(this.restoreTarget()).parent();\n      if ($target.closest('figure').length) {\n        $target.closest('figure').remove();\n      } else {\n        $target = $(this.restoreTarget()).detach();\n      }\n      this.context.triggerEvent('media.delete', $target, this.$editable);\n    });\n\n    /**\n     * float me\n     *\n     * @param {String} value\n     */\n    this.floatMe = this.wrapCommand((value) => {\n      const $target = $(this.restoreTarget());\n      $target.toggleClass('note-float-left', value === 'left');\n      $target.toggleClass('note-float-right', value === 'right');\n      $target.css('float', (value === 'none' ? '' : value));\n    });\n\n    /**\n     * resize overlay element\n     * @param {String} value\n     */\n    this.resize = this.wrapCommand((value) => {\n      const $target = $(this.restoreTarget());\n      value = parseFloat(value);\n      if (value === 0) {\n        $target.css('width', '');\n      } else {\n        $target.css({\n          width: value * 100 + '%',\n          height: '',\n        });\n      }\n    });\n  }\n\n  initialize() {\n    // bind custom events\n    this.$editable.on('keydown', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        this.context.triggerEvent('enter', event);\n      }\n      this.context.triggerEvent('keydown', event);\n\n      // keep a snapshot to limit text on input event\n      this.snapshot = this.history.makeSnapshot();\n      this.hasKeyShortCut = false;\n      if (!event.isDefaultPrevented()) {\n        if (this.options.shortcuts) {\n          this.hasKeyShortCut = this.handleKeyMap(event);\n        } else {\n          this.preventDefaultEditableShortCuts(event);\n        }\n      }\n      if (this.isLimited(1, event)) {\n        const lastRange = this.getLastRange();\n        if (lastRange.eo - lastRange.so === 0) {\n          return false;\n        }\n      }\n      this.setLastRange();\n\n      // record undo in the key event except keyMap.\n      if (this.options.recordEveryKeystroke) {\n        if (this.hasKeyShortCut === false) {\n          this.history.recordUndo();\n        }\n      }\n    }).on('keyup', (event) => {\n      this.setLastRange();\n      this.context.triggerEvent('keyup', event);\n    }).on('focus', (event) => {\n      this.setLastRange();\n      this.context.triggerEvent('focus', event);\n    }).on('blur', (event) => {\n      this.context.triggerEvent('blur', event);\n    }).on('mousedown', (event) => {\n      this.context.triggerEvent('mousedown', event);\n    }).on('mouseup', (event) => {\n      this.setLastRange();\n      this.history.recordUndo();\n      this.context.triggerEvent('mouseup', event);\n    }).on('scroll', (event) => {\n      this.context.triggerEvent('scroll', event);\n    }).on('paste', (event) => {\n      this.setLastRange();\n      this.context.triggerEvent('paste', event);\n    }).on('input', () => {\n      // To limit composition characters (e.g. Korean)\n      if (this.isLimited(0) && this.snapshot) {\n        this.history.applySnapshot(this.snapshot);\n      }\n    });\n\n    this.$editable.attr('spellcheck', this.options.spellCheck);\n\n    this.$editable.attr('autocorrect', this.options.spellCheck);\n\n    if (this.options.disableGrammar) {\n      this.$editable.attr('data-gramm', false);\n    }\n\n    // init content before set event\n    this.$editable.html(dom.html(this.$note) || dom.emptyPara);\n\n    this.$editable.on(env.inputEventName, func.debounce(() => {\n      this.context.triggerEvent('change', this.$editable.html(), this.$editable);\n    }, 10));\n\n    this.$editable.on('focusin', (event) => {\n      this.context.triggerEvent('focusin', event);\n    }).on('focusout', (event) => {\n      this.context.triggerEvent('focusout', event);\n    });\n\n    if (this.options.airMode) {\n      if (this.options.overrideContextMenu) {\n        this.$editor.on('contextmenu', (event) => {\n          this.context.triggerEvent('contextmenu', event);\n          return false;\n        });\n      }\n    } else {\n      if (this.options.width) {\n        this.$editor.outerWidth(this.options.width);\n      }\n      if (this.options.height) {\n        this.$editable.outerHeight(this.options.height);\n      }\n      if (this.options.maxHeight) {\n        this.$editable.css('max-height', this.options.maxHeight);\n      }\n      if (this.options.minHeight) {\n        this.$editable.css('min-height', this.options.minHeight);\n      }\n    }\n\n    this.history.recordUndo();\n    this.setLastRange();\n  }\n\n  destroy() {\n    this.$editable.off();\n  }\n\n  handleKeyMap(event) {\n    const keyMap = this.options.keyMap[env.isMac ? 'mac' : 'pc'];\n    const keys = [];\n\n    if (event.metaKey) { keys.push('CMD'); }\n    if (event.ctrlKey && !event.altKey) { keys.push('CTRL'); }\n    if (event.shiftKey) { keys.push('SHIFT'); }\n\n    const keyName = key.nameFromCode[event.keyCode];\n    if (keyName) {\n      keys.push(keyName);\n    }\n\n    const eventName = keyMap[keys.join('+')];\n\n    if (keyName === 'TAB' && !this.options.tabDisable) {\n      this.afterCommand();\n    } else if (eventName) {\n      if (this.context.invoke(eventName) !== false) {\n        event.preventDefault();\n        // if keyMap action was invoked\n        return true;\n      }\n    } else if (key.isEdit(event.keyCode)) {\n      this.afterCommand();\n    }\n    return false;\n  }\n\n  preventDefaultEditableShortCuts(event) {\n    // B(Bold, 66) / I(Italic, 73) / U(Underline, 85)\n    if ((event.ctrlKey || event.metaKey) &&\n      lists.contains([66, 73, 85], event.keyCode)) {\n      event.preventDefault();\n    }\n  }\n\n  isLimited(pad, event) {\n    pad = pad || 0;\n\n    if (typeof event !== 'undefined') {\n      if (key.isMove(event.keyCode) ||\n          key.isNavigation(event.keyCode) ||\n          (event.ctrlKey || event.metaKey) ||\n          lists.contains([key.code.BACKSPACE, key.code.DELETE], event.keyCode)) {\n        return false;\n      }\n    }\n\n    if (this.options.maxTextLength > 0) {\n      if ((this.$editable.text().length + pad) > this.options.maxTextLength) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * create range\n   * @return {WrappedRange}\n   */\n  createRange() {\n    this.focus();\n    this.setLastRange();\n    return this.getLastRange();\n  }\n\n  /**\n   * create a new range from the list of elements\n   *\n   * @param {list} dom element list\n   * @return {WrappedRange}\n   */\n  createRangeFromList(lst) {\n    const startRange = range.createFromNodeBefore(lists.head(lst));\n    const startPoint = startRange.getStartPoint();\n    const endRange = range.createFromNodeAfter(lists.last(lst));\n    const endPoint = endRange.getEndPoint();\n\n    return range.create(\n      startPoint.node,\n      startPoint.offset,\n      endPoint.node,\n      endPoint.offset\n    );\n  }\n\n  /**\n   * set the last range\n   *\n   * if given rng is exist, set rng as the last range\n   * or create a new range at the end of the document\n   *\n   * @param {WrappedRange} rng\n   */\n  setLastRange(rng) {\n    if (rng) {\n      this.lastRange = rng;\n    } else {\n      this.lastRange = range.create(this.editable);\n\n      if ($(this.lastRange.sc).closest('.note-editable').length === 0) {\n        this.lastRange = range.createFromBodyElement(this.editable);\n      }\n    }\n  }\n\n  /**\n   * get the last range\n   *\n   * if there is a saved last range, return it\n   * or create a new range and return it\n   *\n   * @return {WrappedRange}\n   */\n  getLastRange() {\n    if (!this.lastRange) {\n      this.setLastRange();\n    }\n    return this.lastRange;\n  }\n\n  /**\n   * saveRange\n   *\n   * save current range\n   *\n   * @param {Boolean} [thenCollapse=false]\n   */\n  saveRange(thenCollapse) {\n    if (thenCollapse) {\n      this.getLastRange().collapse().select();\n    }\n  }\n\n  /**\n   * restoreRange\n   *\n   * restore lately range\n   */\n  restoreRange() {\n    if (this.lastRange) {\n      this.lastRange.select();\n      this.focus();\n    }\n  }\n\n  saveTarget(node) {\n    this.$editable.data('target', node);\n  }\n\n  clearTarget() {\n    this.$editable.removeData('target');\n  }\n\n  restoreTarget() {\n    return this.$editable.data('target');\n  }\n\n  /**\n   * currentStyle\n   *\n   * current style\n   * @return {Object|Boolean} unfocus\n   */\n  currentStyle() {\n    let rng = range.create();\n    if (rng) {\n      rng = rng.normalize();\n    }\n    return rng ? this.style.current(rng) : this.style.fromNode(this.$editable);\n  }\n\n  /**\n   * style from node\n   *\n   * @param {jQuery} $node\n   * @return {Object}\n   */\n  styleFromNode($node) {\n    return this.style.fromNode($node);\n  }\n\n  /**\n   * undo\n   */\n  undo() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n    this.history.undo();\n    this.context.triggerEvent('change', this.$editable.html(), this.$editable);\n  }\n\n  /*\n  * commit\n  */\n  commit() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n    this.history.commit();\n    this.context.triggerEvent('change', this.$editable.html(), this.$editable);\n  }\n\n  /**\n   * redo\n   */\n  redo() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n    this.history.redo();\n    this.context.triggerEvent('change', this.$editable.html(), this.$editable);\n  }\n\n  /**\n   * before command\n   */\n  beforeCommand() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n\n    // Set styleWithCSS before run a command\n    document.execCommand('styleWithCSS', false, this.options.styleWithCSS);\n\n    // keep focus on editable before command execution\n    this.focus();\n  }\n\n  /**\n   * after command\n   * @param {Boolean} isPreventTrigger\n   */\n  afterCommand(isPreventTrigger) {\n    this.normalizeContent();\n    this.history.recordUndo();\n    if (!isPreventTrigger) {\n      this.context.triggerEvent('change', this.$editable.html(), this.$editable);\n    }\n  }\n\n  /**\n   * handle tab key\n   */\n  tab() {\n    const rng = this.getLastRange();\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.table.tab(rng);\n    } else {\n      if (this.options.tabSize === 0) {\n        return false;\n      }\n\n      if (!this.isLimited(this.options.tabSize)) {\n        this.beforeCommand();\n        this.typing.insertTab(rng, this.options.tabSize);\n        this.afterCommand();\n      }\n    }\n  }\n\n  /**\n   * handle shift+tab key\n   */\n  untab() {\n    const rng = this.getLastRange();\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.table.tab(rng, true);\n    } else {\n      if (this.options.tabSize === 0) {\n        return false;\n      }\n    }\n  }\n\n  /**\n   * run given function between beforeCommand and afterCommand\n   */\n  wrapCommand(fn) {\n    return function() {\n      this.beforeCommand();\n      fn.apply(this, arguments);\n      this.afterCommand();\n    };\n  }\n\n  /**\n   * insert image\n   *\n   * @param {String} src\n   * @param {String|Function} param\n   * @return {Promise}\n   */\n  insertImage(src, param) {\n    return createImage(src, param).then(($image) => {\n      this.beforeCommand();\n\n      if (typeof param === 'function') {\n        param($image);\n      } else {\n        if (typeof param === 'string') {\n          $image.attr('data-filename', param);\n        }\n        $image.css('width', Math.min(this.$editable.width(), $image.width()));\n      }\n\n      $image.show();\n      this.getLastRange().insertNode($image[0]);\n      this.setLastRange(range.createFromNodeAfter($image[0]).select());\n      this.afterCommand();\n    }).fail((e) => {\n      this.context.triggerEvent('image.upload.error', e);\n    });\n  }\n\n  /**\n   * insertImages\n   * @param {File[]} files\n   */\n  insertImagesAsDataURL(files) {\n    $.each(files, (idx, file) => {\n      const filename = file.name;\n      if (this.options.maximumImageFileSize && this.options.maximumImageFileSize < file.size) {\n        this.context.triggerEvent('image.upload.error', this.lang.image.maximumFileSizeError);\n      } else {\n        readFileAsDataURL(file).then((dataURL) => {\n          return this.insertImage(dataURL, filename);\n        }).fail(() => {\n          this.context.triggerEvent('image.upload.error');\n        });\n      }\n    });\n  }\n\n  /**\n   * insertImagesOrCallback\n   * @param {File[]} files\n   */\n  insertImagesOrCallback(files) {\n    const callbacks = this.options.callbacks;\n    // If onImageUpload set,\n    if (callbacks.onImageUpload) {\n      this.context.triggerEvent('image.upload', files);\n      // else insert Image as dataURL\n    } else {\n      this.insertImagesAsDataURL(files);\n    }\n  }\n\n  /**\n   * return selected plain text\n   * @return {String} text\n   */\n  getSelectedText() {\n    let rng = this.getLastRange();\n\n    // if range on anchor, expand range with anchor\n    if (rng.isOnAnchor()) {\n      rng = range.createFromNode(dom.ancestor(rng.sc, dom.isAnchor));\n    }\n\n    return rng.toString();\n  }\n\n  onFormatBlock(tagName, $target) {\n    // [workaround] for MSIE, IE need `<`\n    document.execCommand('FormatBlock', false, env.isMSIE ? '<' + tagName + '>' : tagName);\n\n    // support custom class\n    if ($target && $target.length) {\n      // find the exact element has given tagName\n      if ($target[0].tagName.toUpperCase() !== tagName.toUpperCase()) {\n        $target = $target.find(tagName);\n      }\n\n      if ($target && $target.length) {\n        const className = $target[0].className || '';\n        if (className) {\n          const currentRange = this.createRange();\n\n          const $parent = $([currentRange.sc, currentRange.ec]).closest(tagName);\n          $parent.addClass(className);\n        }\n      }\n    }\n  }\n\n  formatPara() {\n    this.formatBlock('P');\n  }\n\n  fontStyling(target, value) {\n    const rng = this.getLastRange();\n\n    if (rng !== '') {\n      const spans = this.style.styleNodes(rng);\n      this.$editor.find('.note-status-output').html('');\n      $(spans).css(target, value);\n\n      // [workaround] added styled bogus span for style\n      //  - also bogus character needed for cursor position\n      if (rng.isCollapsed()) {\n        const firstSpan = lists.head(spans);\n        if (firstSpan && !dom.nodeLength(firstSpan)) {\n          firstSpan.innerHTML = dom.ZERO_WIDTH_NBSP_CHAR;\n          range.createFromNode(firstSpan.firstChild).select();\n          this.setLastRange();\n          this.$editable.data(KEY_BOGUS, firstSpan);\n        }\n      } else {\n        this.setLastRange(\n          this.createRangeFromList(spans).select()\n        );\n      }\n    } else {\n      const noteStatusOutput = $.now();\n      this.$editor.find('.note-status-output').html('<div id=\"note-status-output-' + noteStatusOutput + '\" class=\"alert alert-info\">' + this.lang.output.noSelection + '</div>');\n      setTimeout(function() { $('#note-status-output-' + noteStatusOutput).remove(); }, 5000);\n    }\n  }\n\n  /**\n   * unlink\n   *\n   * @type command\n   */\n  unlink() {\n    let rng = this.getLastRange();\n    if (rng.isOnAnchor()) {\n      const anchor = dom.ancestor(rng.sc, dom.isAnchor);\n      rng = range.createFromNode(anchor);\n      rng.select();\n      this.setLastRange();\n\n      this.beforeCommand();\n      document.execCommand('unlink');\n      this.afterCommand();\n    }\n  }\n\n  /**\n   * returns link info\n   *\n   * @return {Object}\n   * @return {WrappedRange} return.range\n   * @return {String} return.text\n   * @return {Boolean} [return.isNewWindow=true]\n   * @return {String} [return.url=\"\"]\n   */\n  getLinkInfo() {\n    const rng = this.getLastRange().expand(dom.isAnchor);\n    // Get the first anchor on range(for edit).\n    const $anchor = $(lists.head(rng.nodes(dom.isAnchor)));\n    const linkInfo = {\n      range: rng,\n      text: rng.toString(),\n      url: $anchor.length ? $anchor.attr('href') : '',\n    };\n\n    // When anchor exists,\n    if ($anchor.length) {\n      // Set isNewWindow by checking its target.\n      linkInfo.isNewWindow = $anchor.attr('target') === '_blank';\n    }\n\n    return linkInfo;\n  }\n\n  addRow(position) {\n    const rng = this.getLastRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.addRow(rng, position);\n      this.afterCommand();\n    }\n  }\n\n  addCol(position) {\n    const rng = this.getLastRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.addCol(rng, position);\n      this.afterCommand();\n    }\n  }\n\n  deleteRow() {\n    const rng = this.getLastRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.deleteRow(rng);\n      this.afterCommand();\n    }\n  }\n\n  deleteCol() {\n    const rng = this.getLastRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.deleteCol(rng);\n      this.afterCommand();\n    }\n  }\n\n  deleteTable() {\n    const rng = this.getLastRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.deleteTable(rng);\n      this.afterCommand();\n    }\n  }\n\n  /**\n   * @param {Position} pos\n   * @param {jQuery} $target - target element\n   * @param {Boolean} [bKeepRatio] - keep ratio\n   */\n  resizeTo(pos, $target, bKeepRatio) {\n    let imageSize;\n    if (bKeepRatio) {\n      const newRatio = pos.y / pos.x;\n      const ratio = $target.data('ratio');\n      imageSize = {\n        width: ratio > newRatio ? pos.x : pos.y / ratio,\n        height: ratio > newRatio ? pos.x * ratio : pos.y,\n      };\n    } else {\n      imageSize = {\n        width: pos.x,\n        height: pos.y,\n      };\n    }\n\n    $target.css(imageSize);\n  }\n\n  /**\n   * returns whether editable area has focus or not.\n   */\n  hasFocus() {\n    return this.$editable.is(':focus');\n  }\n\n  /**\n   * set focus\n   */\n  focus() {\n    // [workaround] Screen will move when page is scolled in IE.\n    //  - do focus when not focused\n    if (!this.hasFocus()) {\n      this.$editable.focus();\n    }\n  }\n\n  /**\n   * returns whether contents is empty or not.\n   * @return {Boolean}\n   */\n  isEmpty() {\n    return dom.isEmpty(this.$editable[0]) || dom.emptyPara === this.$editable.html();\n  }\n\n  /**\n   * Removes all contents and restores the editable instance to an _emptyPara_.\n   */\n  empty() {\n    this.context.invoke('code', dom.emptyPara);\n  }\n\n  /**\n   * normalize content\n   */\n  normalizeContent() {\n    this.$editable[0].normalize();\n  }\n}\n", "import $ from 'jquery';\n\n/**\n * @method readFileAsDataURL\n *\n * read contents of file as representing URL\n *\n * @param {File} file\n * @return {Promise} - then: dataUrl\n */\nexport function readFileAsDataURL(file) {\n  return $.Deferred((deferred) => {\n    $.extend(new FileReader(), {\n      onload: (e) => {\n        const dataURL = e.target.result;\n        deferred.resolve(dataURL);\n      },\n      onerror: (err) => {\n        deferred.reject(err);\n      },\n    }).readAsDataURL(file);\n  }).promise();\n}\n\n/**\n * @method createImage\n *\n * create `<image>` from url string\n *\n * @param {String} url\n * @return {Promise} - then: $image\n */\nexport function createImage(url) {\n  return $.Deferred((deferred) => {\n    const $img = $('<img>');\n\n    $img.one('load', () => {\n      $img.off('error abort');\n      deferred.resolve($img);\n    }).one('error abort', () => {\n      $img.off('load').detach();\n      deferred.reject($img);\n    }).css({\n      display: 'none',\n    }).appendTo(document.body).attr('src', url);\n  }).promise();\n}\n", "import lists from '../core/lists';\n\nexport default class Clipboard {\n  constructor(context) {\n    this.context = context;\n    this.$editable = context.layoutInfo.editable;\n  }\n\n  initialize() {\n    this.$editable.on('paste', this.pasteByEvent.bind(this));\n  }\n\n  /**\n   * paste by clipboard event\n   *\n   * @param {Event} event\n   */\n  pasteByEvent(event) {\n    const clipboardData = event.originalEvent.clipboardData;\n\n    if (clipboardData && clipboardData.items && clipboardData.items.length) {\n      const item = clipboardData.items.length > 1 ? clipboardData.items[1] : lists.head(clipboardData.items);\n      if (item.kind === 'file' && item.type.indexOf('image/') !== -1) {\n        // paste img file\n        this.context.invoke('editor.insertImagesOrCallback', [item.getAsFile()]);\n        event.preventDefault();\n      } else if (item.kind === 'string') {\n        // paste text with maxTextLength check\n        if (this.context.invoke('editor.isLimited', clipboardData.getData('Text').length)) {\n          event.preventDefault();\n        }\n      }\n    } else if (window.clipboardData) {\n      // for IE\n      let text = window.clipboardData.getData('text');\n      if (this.context.invoke('editor.isLimited', text.length)) {\n        event.preventDefault();\n      }\n    }\n    // Call editor.afterCommand after proceeding default event handler\n    setTimeout(() => {\n      this.context.invoke('editor.afterCommand');\n    }, 10);\n  }\n}\n", "import $ from 'jquery';\n\nexport default class Dropzone {\n  constructor(context) {\n    this.context = context;\n    this.$eventListener = $(document);\n    this.$editor = context.layoutInfo.editor;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n    this.documentEventHandlers = {};\n\n    this.$dropzone = $([\n      '<div class=\"note-dropzone\">',\n        '<div class=\"note-dropzone-message\"></div>',\n      '</div>',\n    ].join('')).prependTo(this.$editor);\n  }\n\n  /**\n   * attach Drag and Drop Events\n   */\n  initialize() {\n    if (this.options.disableDragAndDrop) {\n      // prevent default drop event\n      this.documentEventHandlers.onDrop = (e) => {\n        e.preventDefault();\n      };\n      // do not consider outside of dropzone\n      this.$eventListener = this.$dropzone;\n      this.$eventListener.on('drop', this.documentEventHandlers.onDrop);\n    } else {\n      this.attachDragAndDropEvent();\n    }\n  }\n\n  /**\n   * attach Drag and Drop Events\n   */\n  attachDragAndDropEvent() {\n    let collection = $();\n    const $dropzoneMessage = this.$dropzone.find('.note-dropzone-message');\n\n    this.documentEventHandlers.onDragenter = (e) => {\n      const isCodeview = this.context.invoke('codeview.isActivated');\n      const hasEditorSize = this.$editor.width() > 0 && this.$editor.height() > 0;\n      if (!isCodeview && !collection.length && hasEditorSize) {\n        this.$editor.addClass('dragover');\n        this.$dropzone.width(this.$editor.width());\n        this.$dropzone.height(this.$editor.height());\n        $dropzoneMessage.text(this.lang.image.dragImageHere);\n      }\n      collection = collection.add(e.target);\n    };\n\n    this.documentEventHandlers.onDragleave = (e) => {\n      collection = collection.not(e.target);\n\n      // If nodeName is BODY, then just make it over (fix for IE)\n      if (!collection.length || e.target.nodeName === 'BODY') {\n        collection = $();\n        this.$editor.removeClass('dragover');\n      }\n    };\n\n    this.documentEventHandlers.onDrop = () => {\n      collection = $();\n      this.$editor.removeClass('dragover');\n    };\n\n    // show dropzone on dragenter when dragging a object to document\n    // -but only if the editor is visible, i.e. has a positive width and height\n    this.$eventListener.on('dragenter', this.documentEventHandlers.onDragenter)\n      .on('dragleave', this.documentEventHandlers.onDragleave)\n      .on('drop', this.documentEventHandlers.onDrop);\n\n    // change dropzone's message on hover.\n    this.$dropzone.on('dragenter', () => {\n      this.$dropzone.addClass('hover');\n      $dropzoneMessage.text(this.lang.image.dropImage);\n    }).on('dragleave', () => {\n      this.$dropzone.removeClass('hover');\n      $dropzoneMessage.text(this.lang.image.dragImageHere);\n    });\n\n    // attach dropImage\n    this.$dropzone.on('drop', (event) => {\n      const dataTransfer = event.originalEvent.dataTransfer;\n\n      // stop the browser from opening the dropped content\n      event.preventDefault();\n\n      if (dataTransfer && dataTransfer.files && dataTransfer.files.length) {\n        this.$editable.focus();\n        this.context.invoke('editor.insertImagesOrCallback', dataTransfer.files);\n      } else {\n        $.each(dataTransfer.types, (idx, type) => {\n          // skip moz-specific types\n          if (type.toLowerCase().indexOf('_moz_') > -1) {\n            return;\n          }\n          const content = dataTransfer.getData(type);\n\n          if (type.toLowerCase().indexOf('text') > -1) {\n            this.context.invoke('editor.pasteHTML', content);\n          } else {\n            $(content).each((idx, item) => {\n              this.context.invoke('editor.insertNode', item);\n            });\n          }\n        });\n      }\n    }).on('dragover', false); // prevent default dragover event\n  }\n\n  destroy() {\n    Object.keys(this.documentEventHandlers).forEach((key) => {\n      this.$eventListener.off(key.substr(2).toLowerCase(), this.documentEventHandlers[key]);\n    });\n    this.documentEventHandlers = {};\n  }\n}\n", "import dom from '../core/dom';\nimport key from '../core/key';\n\n/**\n * @class Codeview\n */\nexport default class CodeView {\n  constructor(context) {\n    this.context = context;\n    this.$editor = context.layoutInfo.editor;\n    this.$editable = context.layoutInfo.editable;\n    this.$codable = context.layoutInfo.codable;\n    this.options = context.options;\n    this.CodeMirrorConstructor = window.CodeMirror;\n\n    if (this.options.codemirror.CodeMirrorConstructor) {\n      this.CodeMirrorConstructor = this.options.codemirror.CodeMirrorConstructor;\n    }\n  }\n\n  sync(html) {\n    const isCodeview = this.isActivated();\n    const CodeMirror = this.CodeMirrorConstructor;\n\n    if (isCodeview) {\n      if (html) {\n        if (CodeMirror) {\n          this.$codable.data('cmEditor').getDoc().setValue(html);\n        } else {\n          this.$codable.val(html);\n        }\n      } else {\n        if (CodeMirror) {\n          this.$codable.data('cmEditor').save();\n        }\n      }\n    }\n  }\n\n  initialize() {\n    this.$codable.on('keyup', (event) => {\n      if (event.keyCode === key.code.ESCAPE) {\n        this.deactivate();\n      }\n    });\n  }\n\n  /**\n   * @return {Boolean}\n   */\n  isActivated() {\n    return this.$editor.hasClass('codeview');\n  }\n\n  /**\n   * toggle codeview\n   */\n  toggle() {\n    if (this.isActivated()) {\n      this.deactivate();\n    } else {\n      this.activate();\n    }\n    this.context.triggerEvent('codeview.toggled');\n  }\n\n  /**\n   * purify input value\n   * @param value\n   * @returns {*}\n   */\n  purify(value) {\n    if (this.options.codeviewFilter) {\n      // filter code view regex\n      value = value.replace(this.options.codeviewFilterRegex, '');\n      // allow specific iframe tag\n      if (this.options.codeviewIframeFilter) {\n        const whitelist = this.options.codeviewIframeWhitelistSrc.concat(this.options.codeviewIframeWhitelistSrcBase);\n        value = value.replace(/(<iframe.*?>.*?(?:<\\/iframe>)?)/gi, function(tag) {\n          // remove if src attribute is duplicated\n          if (/<.+src(?==?('|\"|\\s)?)[\\s\\S]+src(?=('|\"|\\s)?)[^>]*?>/i.test(tag)) {\n            return '';\n          }\n          for (const src of whitelist) {\n            // pass if src is trusted\n            if ((new RegExp('src=\"(https?:)?\\/\\/' + src.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&') + '\\/(.+)\"')).test(tag)) {\n              return tag;\n            }\n          }\n          return '';\n        });\n      }\n    }\n    return value;\n  }\n\n  /**\n   * activate code view\n   */\n  activate() {\n    const CodeMirror = this.CodeMirrorConstructor;\n    this.$codable.val(dom.html(this.$editable, this.options.prettifyHtml));\n    this.$codable.height(this.$editable.height());\n\n    this.context.invoke('toolbar.updateCodeview', true);\n    this.context.invoke('airPopover.updateCodeview', true);\n\n    this.$editor.addClass('codeview');\n    this.$codable.focus();\n\n    // activate CodeMirror as codable\n    if (CodeMirror) {\n      const cmEditor = CodeMirror.fromTextArea(this.$codable[0], this.options.codemirror);\n\n      // CodeMirror TernServer\n      if (this.options.codemirror.tern) {\n        const server = new CodeMirror.TernServer(this.options.codemirror.tern);\n        cmEditor.ternServer = server;\n        cmEditor.on('cursorActivity', (cm) => {\n          server.updateArgHints(cm);\n        });\n      }\n\n      cmEditor.on('blur', (event) => {\n        this.context.triggerEvent('blur.codeview', cmEditor.getValue(), event);\n      });\n      cmEditor.on('change', () => {\n        this.context.triggerEvent('change.codeview', cmEditor.getValue(), cmEditor);\n      });\n\n      // CodeMirror hasn't Padding.\n      cmEditor.setSize(null, this.$editable.outerHeight());\n      this.$codable.data('cmEditor', cmEditor);\n    } else {\n      this.$codable.on('blur', (event) => {\n        this.context.triggerEvent('blur.codeview', this.$codable.val(), event);\n      });\n      this.$codable.on('input', () => {\n        this.context.triggerEvent('change.codeview', this.$codable.val(), this.$codable);\n      });\n    }\n  }\n\n  /**\n   * deactivate code view\n   */\n  deactivate() {\n    const CodeMirror = this.CodeMirrorConstructor;\n    // deactivate CodeMirror as codable\n    if (CodeMirror) {\n      const cmEditor = this.$codable.data('cmEditor');\n      this.$codable.val(cmEditor.getValue());\n      cmEditor.toTextArea();\n    }\n\n    const value = this.purify(dom.value(this.$codable, this.options.prettifyHtml) || dom.emptyPara);\n    const isChange = this.$editable.html() !== value;\n\n    this.$editable.html(value);\n    this.$editable.height(this.options.height ? this.$codable.height() : 'auto');\n    this.$editor.removeClass('codeview');\n\n    if (isChange) {\n      this.context.triggerEvent('change', this.$editable.html(), this.$editable);\n    }\n\n    this.$editable.focus();\n\n    this.context.invoke('toolbar.updateCodeview', false);\n    this.context.invoke('airPopover.updateCodeview', false);\n  }\n\n  destroy() {\n    if (this.isActivated()) {\n      this.deactivate();\n    }\n  }\n}\n", "import $ from 'jquery';\nconst EDITABLE_PADDING = 24;\n\nexport default class Statusbar {\n  constructor(context) {\n    this.$document = $(document);\n    this.$statusbar = context.layoutInfo.statusbar;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n  }\n\n  initialize() {\n    if (this.options.airMode || this.options.disableResizeEditor) {\n      this.destroy();\n      return;\n    }\n\n    this.$statusbar.on('mousedown', (event) => {\n      event.preventDefault();\n      event.stopPropagation();\n\n      const editableTop = this.$editable.offset().top - this.$document.scrollTop();\n      const onMouseMove = (event) => {\n        let height = event.clientY - (editableTop + EDITABLE_PADDING);\n\n        height = (this.options.minheight > 0) ? Math.max(height, this.options.minheight) : height;\n        height = (this.options.maxHeight > 0) ? Math.min(height, this.options.maxHeight) : height;\n\n        this.$editable.height(height);\n      };\n\n      this.$document.on('mousemove', onMouseMove).one('mouseup', () => {\n        this.$document.off('mousemove', onMouseMove);\n      });\n    });\n  }\n\n  destroy() {\n    this.$statusbar.off();\n    this.$statusbar.addClass('locked');\n  }\n}\n", "import $ from 'jquery';\n\nexport default class Fullscreen {\n  constructor(context) {\n    this.context = context;\n\n    this.$editor = context.layoutInfo.editor;\n    this.$toolbar = context.layoutInfo.toolbar;\n    this.$editable = context.layoutInfo.editable;\n    this.$codable = context.layoutInfo.codable;\n\n    this.$window = $(window);\n    this.$scrollbar = $('html, body');\n\n    this.onResize = () => {\n      this.resizeTo({\n        h: this.$window.height() - this.$toolbar.outerHeight(),\n      });\n    };\n  }\n\n  resizeTo(size) {\n    this.$editable.css('height', size.h);\n    this.$codable.css('height', size.h);\n    if (this.$codable.data('cmeditor')) {\n      this.$codable.data('cmeditor').setsize(null, size.h);\n    }\n  }\n\n  /**\n   * toggle fullscreen\n   */\n  toggle() {\n    this.$editor.toggleClass('fullscreen');\n    if (this.isFullscreen()) {\n      this.$editable.data('orgHeight', this.$editable.css('height'));\n      this.$editable.data('orgMaxHeight', this.$editable.css('maxHeight'));\n      this.$editable.css('maxHeight', '');\n      this.$window.on('resize', this.onResize).trigger('resize');\n      this.$scrollbar.css('overflow', 'hidden');\n    } else {\n      this.$window.off('resize', this.onResize);\n      this.resizeTo({ h: this.$editable.data('orgHeight') });\n      this.$editable.css('maxHeight', this.$editable.css('orgMaxHeight'));\n      this.$scrollbar.css('overflow', 'visible');\n    }\n\n    this.context.invoke('toolbar.updateFullscreen', this.isFullscreen());\n  }\n\n  isFullscreen() {\n    return this.$editor.hasClass('fullscreen');\n  }\n}\n", "import $ from 'jquery';\nimport dom from '../core/dom';\n\nexport default class Handle {\n  constructor(context) {\n    this.context = context;\n    this.$document = $(document);\n    this.$editingArea = context.layoutInfo.editingArea;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n\n    this.events = {\n      'summernote.mousedown': (we, e) => {\n        if (this.update(e.target, e)) {\n          e.preventDefault();\n        }\n      },\n      'summernote.keyup summernote.scroll summernote.change summernote.dialog.shown': () => {\n        this.update();\n      },\n      'summernote.disable summernote.blur': () => {\n        this.hide();\n      },\n      'summernote.codeview.toggled': () => {\n        this.update();\n      },\n    };\n  }\n\n  initialize() {\n    this.$handle = $([\n      '<div class=\"note-handle\">',\n        '<div class=\"note-control-selection\">',\n          '<div class=\"note-control-selection-bg\"></div>',\n          '<div class=\"note-control-holder note-control-nw\"></div>',\n          '<div class=\"note-control-holder note-control-ne\"></div>',\n          '<div class=\"note-control-holder note-control-sw\"></div>',\n          '<div class=\"',\n            (this.options.disableResizeImage ? 'note-control-holder' : 'note-control-sizing'),\n          ' note-control-se\"></div>',\n          (this.options.disableResizeImage ? '' : '<div class=\"note-control-selection-info\"></div>'),\n        '</div>',\n      '</div>',\n    ].join('')).prependTo(this.$editingArea);\n\n    this.$handle.on('mousedown', (event) => {\n      if (dom.isControlSizing(event.target)) {\n        event.preventDefault();\n        event.stopPropagation();\n\n        const $target = this.$handle.find('.note-control-selection').data('target');\n        const posStart = $target.offset();\n        const scrollTop = this.$document.scrollTop();\n\n        const onMouseMove = (event) => {\n          this.context.invoke('editor.resizeTo', {\n            x: event.clientX - posStart.left,\n            y: event.clientY - (posStart.top - scrollTop),\n          }, $target, !event.shiftKey);\n\n          this.update($target[0], event);\n        };\n\n        this.$document\n          .on('mousemove', onMouseMove)\n          .one('mouseup', (e) => {\n            e.preventDefault();\n            this.$document.off('mousemove', onMouseMove);\n            this.context.invoke('editor.afterCommand');\n          });\n\n        if (!$target.data('ratio')) { // original ratio.\n          $target.data('ratio', $target.height() / $target.width());\n        }\n      }\n    });\n\n    // Listen for scrolling on the handle overlay.\n    this.$handle.on('wheel', (e) => {\n      e.preventDefault();\n      this.update();\n    });\n  }\n\n  destroy() {\n    this.$handle.remove();\n  }\n\n  update(target, event) {\n    if (this.context.isDisabled()) {\n      return false;\n    }\n\n    const isImage = dom.isImg(target);\n    const $selection = this.$handle.find('.note-control-selection');\n\n    this.context.invoke('imagePopover.update', target, event);\n\n    if (isImage) {\n      const $image = $(target);\n      const position = $image.position();\n      const pos = {\n        left: position.left + parseInt($image.css('marginLeft'), 10),\n        top: position.top + parseInt($image.css('marginTop'), 10),\n      };\n\n      // exclude margin\n      const imageSize = {\n        w: $image.outerWidth(false),\n        h: $image.outerHeight(false),\n      };\n\n      $selection.css({\n        display: 'block',\n        left: pos.left,\n        top: pos.top,\n        width: imageSize.w,\n        height: imageSize.h,\n      }).data('target', $image); // save current image element.\n\n      const origImageObj = new Image();\n      origImageObj.src = $image.attr('src');\n\n      const sizingText = imageSize.w + 'x' + imageSize.h + ' (' + this.lang.image.original + ': ' + origImageObj.width + 'x' + origImageObj.height + ')';\n      $selection.find('.note-control-selection-info').text(sizingText);\n      this.context.invoke('editor.saveTarget', target);\n    } else {\n      this.hide();\n    }\n\n    return isImage;\n  }\n\n  /**\n   * hide\n   *\n   * @param {jQuery} $handle\n   */\n  hide() {\n    this.context.invoke('editor.clearTarget');\n    this.$handle.children().hide();\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport key from '../core/key';\n\nconst defaultScheme = 'http://';\nconst linkPattern = /^([A-Za-z][A-Za-z0-9+-.]*\\:[\\/]{2}|tel:|mailto:[A-Z0-9._%+-]+@)?(www\\.)?(.+)$/i;\n\nexport default class AutoLink {\n  constructor(context) {\n    this.context = context;\n    this.options = context.options;\n    this.events = {\n      'summernote.keyup': (we, e) => {\n        if (!e.isDefaultPrevented()) {\n          this.handleKeyup(e);\n        }\n      },\n      'summernote.keydown': (we, e) => {\n        this.handleKeydown(e);\n      },\n    };\n  }\n\n  initialize() {\n    this.lastWordRange = null;\n  }\n\n  destroy() {\n    this.lastWordRange = null;\n  }\n\n  replace() {\n    if (!this.lastWordRange) {\n      return;\n    }\n\n    const keyword = this.lastWordRange.toString();\n    const match = keyword.match(linkPattern);\n\n    if (match && (match[1] || match[2])) {\n      const link = match[1] ? keyword : defaultScheme + keyword;\n      const urlText = this.options.showDomainOnlyForAutolink ?\n        keyword.replace(/^(?:https?:\\/\\/)?(?:tel?:?)?(?:mailto?:?)?(?:www\\.)?/i, '').split('/')[0]\n        : keyword;\n      const node = $('<a />').html(urlText).attr('href', link)[0];\n      if (this.context.options.linkTargetBlank) {\n        $(node).attr('target', '_blank');\n      }\n\n      this.lastWordRange.insertNode(node);\n      this.lastWordRange = null;\n      this.context.invoke('editor.focus');\n    }\n  }\n\n  handleKeydown(e) {\n    if (lists.contains([key.code.ENTER, key.code.SPACE], e.keyCode)) {\n      const wordRange = this.context.invoke('editor.createRange').getWordRange();\n      this.lastWordRange = wordRange;\n    }\n  }\n\n  handleKeyup(e) {\n    if (lists.contains([key.code.ENTER, key.code.SPACE], e.keyCode)) {\n      this.replace();\n    }\n  }\n}\n", "import dom from '../core/dom';\n\n/**\n * textarea auto sync.\n */\nexport default class AutoSync {\n  constructor(context) {\n    this.$note = context.layoutInfo.note;\n    this.events = {\n      'summernote.change': () => {\n        this.$note.val(context.invoke('code'));\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return dom.isTextarea(this.$note[0]);\n  }\n}\n", "import lists from '../core/lists';\nimport dom from '../core/dom';\nimport key from '../core/key';\n\nexport default class AutoReplace {\n  constructor(context) {\n    this.context = context;\n    this.options = context.options.replace || {};\n\n    this.keys = [key.code.ENTER, key.code.SPACE, key.code.PERIOD, key.code.COMMA, key.code.SEMICOLON, key.code.SLASH];\n    this.previousKeydownCode = null;\n\n    this.events = {\n      'summernote.keyup': (we, e) => {\n        if (!e.isDefaultPrevented()) {\n          this.handleKeyup(e);\n        }\n      },\n      'summernote.keydown': (we, e) => {\n        this.handleKeydown(e);\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return !!this.options.match;\n  }\n\n  initialize() {\n    this.lastWord = null;\n  }\n\n  destroy() {\n    this.lastWord = null;\n  }\n\n  replace() {\n    if (!this.lastWord) {\n      return;\n    }\n\n    const self = this;\n    const keyword = this.lastWord.toString();\n    this.options.match(keyword, function(match) {\n      if (match) {\n        let node = '';\n\n        if (typeof match === 'string') {\n          node = dom.createText(match);\n        } else if (match instanceof jQuery) {\n          node = match[0];\n        } else if (match instanceof Node) {\n          node = match;\n        }\n\n        if (!node) return;\n        self.lastWord.insertNode(node);\n        self.lastWord = null;\n        self.context.invoke('editor.focus');\n      }\n    });\n  }\n\n  handleKeydown(e) {\n    // this forces it to remember the last whole word, even if multiple termination keys are pressed\n    // before the previous key is let go.\n    if (this.previousKeydownCode && lists.contains(this.keys, this.previousKeydownCode)) {\n      this.previousKeydownCode = e.keyCode;\n      return;\n    }\n\n    if (lists.contains(this.keys, e.keyCode)) {\n      const wordRange = this.context.invoke('editor.createRange').getWordRange();\n      this.lastWord = wordRange;\n    }\n    this.previousKeydownCode = e.keyCode;\n  }\n\n  handleKeyup(e) {\n    if (lists.contains(this.keys, e.keyCode)) {\n      this.replace();\n    }\n  }\n}\n", "import $ from 'jquery';\nexport default class Placeholder {\n  constructor(context) {\n    this.context = context;\n\n    this.$editingArea = context.layoutInfo.editingArea;\n    this.options = context.options;\n\n    if (this.options.inheritPlaceholder === true) {\n      // get placeholder value from the original element\n      this.options.placeholder = this.context.$note.attr('placeholder') || this.options.placeholder;\n    }\n\n    this.events = {\n      'summernote.init summernote.change': () => {\n        this.update();\n      },\n      'summernote.codeview.toggled': () => {\n        this.update();\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return !!this.options.placeholder;\n  }\n\n  initialize() {\n    this.$placeholder = $('<div class=\"note-placeholder\">');\n    this.$placeholder.on('click', () => {\n      this.context.invoke('focus');\n    }).html(this.options.placeholder).prependTo(this.$editingArea);\n\n    this.update();\n  }\n\n  destroy() {\n    this.$placeholder.remove();\n  }\n\n  update() {\n    const isShow = !this.context.invoke('codeview.isActivated') && this.context.invoke('editor.isEmpty');\n    this.$placeholder.toggle(isShow);\n  }\n}\n", "import $ from 'jquery';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport env from '../core/env';\n\nexport default class Buttons {\n  constructor(context) {\n    this.ui = $.summernote.ui;\n    this.context = context;\n    this.$toolbar = context.layoutInfo.toolbar;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n    this.invertedKeyMap = func.invertObject(\n      this.options.keyMap[env.isMac ? 'mac' : 'pc']\n    );\n  }\n\n  representShortcut(editorMethod) {\n    let shortcut = this.invertedKeyMap[editorMethod];\n    if (!this.options.shortcuts || !shortcut) {\n      return '';\n    }\n\n    if (env.isMac) {\n      shortcut = shortcut.replace('CMD', '⌘').replace('SHIFT', '⇧');\n    }\n\n    shortcut = shortcut.replace('BACKSLASH', '\\\\')\n      .replace('SLASH', '/')\n      .replace('LEFTBRACKET', '[')\n      .replace('RIGHTBRACKET', ']');\n\n    return ' (' + shortcut + ')';\n  }\n\n  button(o) {\n    if (!this.options.tooltip && o.tooltip) {\n      delete o.tooltip;\n    }\n    o.container = this.options.container;\n    return this.ui.button(o);\n  }\n\n  initialize() {\n    this.addToolbarButtons();\n    this.addImagePopoverButtons();\n    this.addLinkPopoverButtons();\n    this.addTablePopoverButtons();\n    this.fontInstalledMap = {};\n  }\n\n  destroy() {\n    delete this.fontInstalledMap;\n  }\n\n  isFontInstalled(name) {\n    if (!Object.prototype.hasOwnProperty.call(this.fontInstalledMap, name)) {\n      this.fontInstalledMap[name] = env.isFontInstalled(name) ||\n        lists.contains(this.options.fontNamesIgnoreCheck, name);\n    }\n    return this.fontInstalledMap[name];\n  }\n\n  isFontDeservedToAdd(name) {\n    name = name.toLowerCase();\n    return (name !== '' && this.isFontInstalled(name) && env.genericFontFamilies.indexOf(name) === -1);\n  }\n\n  colorPalette(className, tooltip, backColor, foreColor) {\n    return this.ui.buttonGroup({\n      className: 'note-color ' + className,\n      children: [\n        this.button({\n          className: 'note-current-color-button',\n          contents: this.ui.icon(this.options.icons.font + ' note-recent-color'),\n          tooltip: tooltip,\n          click: (e) => {\n            const $button = $(e.currentTarget);\n            if (backColor && foreColor) {\n              this.context.invoke('editor.color', {\n                backColor: $button.attr('data-backColor'),\n                foreColor: $button.attr('data-foreColor'),\n              });\n            } else if (backColor) {\n              this.context.invoke('editor.color', {\n                backColor: $button.attr('data-backColor'),\n              });\n            } else if (foreColor) {\n              this.context.invoke('editor.color', {\n                foreColor: $button.attr('data-foreColor'),\n              });\n            }\n          },\n          callback: ($button) => {\n            const $recentColor = $button.find('.note-recent-color');\n            if (backColor) {\n              $recentColor.css('background-color', this.options.colorButton.backColor);\n              $button.attr('data-backColor', this.options.colorButton.backColor);\n            }\n            if (foreColor) {\n              $recentColor.css('color', this.options.colorButton.foreColor);\n              $button.attr('data-foreColor', this.options.colorButton.foreColor);\n            } else {\n              $recentColor.css('color', 'transparent');\n            }\n          },\n        }),\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents('', this.options),\n          tooltip: this.lang.color.more,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdown({\n          items: (backColor ? [\n            '<div class=\"note-palette\">',\n              '<div class=\"note-palette-title\">' + this.lang.color.background + '</div>',\n              '<div>',\n                '<button type=\"button\" class=\"note-color-reset btn btn-light btn-default\" data-event=\"backColor\" data-value=\"transparent\">',\n                  this.lang.color.transparent,\n                '</button>',\n              '</div>',\n              '<div class=\"note-holder\" data-event=\"backColor\"><!-- back colors --></div>',\n              '<div>',\n                '<button type=\"button\" class=\"note-color-select btn btn-light btn-default\" data-event=\"openPalette\" data-value=\"backColorPicker\">',\n                  this.lang.color.cpSelect,\n                '</button>',\n                '<input type=\"color\" id=\"backColorPicker\" class=\"note-btn note-color-select-btn\" value=\"' + this.options.colorButton.backColor + '\" data-event=\"backColorPalette\">',\n              '</div>',\n              '<div class=\"note-holder-custom\" id=\"backColorPalette\" data-event=\"backColor\"></div>',\n            '</div>',\n          ].join('') : '') +\n          (foreColor ? [\n            '<div class=\"note-palette\">',\n              '<div class=\"note-palette-title\">' + this.lang.color.foreground + '</div>',\n              '<div>',\n                '<button type=\"button\" class=\"note-color-reset btn btn-light btn-default\" data-event=\"removeFormat\" data-value=\"foreColor\">',\n                  this.lang.color.resetToDefault,\n                '</button>',\n              '</div>',\n              '<div class=\"note-holder\" data-event=\"foreColor\"><!-- fore colors --></div>',\n              '<div>',\n                '<button type=\"button\" class=\"note-color-select btn btn-light btn-default\" data-event=\"openPalette\" data-value=\"foreColorPicker\">',\n                  this.lang.color.cpSelect,\n                '</button>',\n                '<input type=\"color\" id=\"foreColorPicker\" class=\"note-btn note-color-select-btn\" value=\"' + this.options.colorButton.foreColor + '\" data-event=\"foreColorPalette\">',\n              '</div>', // Fix missing Div, Commented to find easily if it's wrong\n              '<div class=\"note-holder-custom\" id=\"foreColorPalette\" data-event=\"foreColor\"></div>',\n            '</div>',\n          ].join('') : ''),\n          callback: ($dropdown) => {\n            $dropdown.find('.note-holder').each((idx, item) => {\n              const $holder = $(item);\n              $holder.append(this.ui.palette({\n                colors: this.options.colors,\n                colorsName: this.options.colorsName,\n                eventName: $holder.data('event'),\n                container: this.options.container,\n                tooltip: this.options.tooltip,\n              }).render());\n            });\n            /* TODO: do we have to record recent custom colors within cookies? */\n            var customColors = [\n              ['#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF'],\n            ];\n            $dropdown.find('.note-holder-custom').each((idx, item) => {\n              const $holder = $(item);\n              $holder.append(this.ui.palette({\n                colors: customColors,\n                colorsName: customColors,\n                eventName: $holder.data('event'),\n                container: this.options.container,\n                tooltip: this.options.tooltip,\n              }).render());\n            });\n            $dropdown.find('input[type=color]').each((idx, item) => {\n              $(item).change(function() {\n                const $chip = $dropdown.find('#' + $(this).data('event')).find('.note-color-btn').first();\n                const color = this.value.toUpperCase();\n                $chip.css('background-color', color)\n                  .attr('aria-label', color)\n                  .attr('data-value', color)\n                  .attr('data-original-title', color);\n                $chip.click();\n              });\n            });\n          },\n          click: (event) => {\n            event.stopPropagation();\n\n            const $parent = $('.' + className).find('.note-dropdown-menu');\n            const $button = $(event.target);\n            const eventName = $button.data('event');\n            const value = $button.attr('data-value');\n\n            if (eventName === 'openPalette') {\n              const $picker = $parent.find('#' + value);\n              const $palette = $($parent.find('#' + $picker.data('event')).find('.note-color-row')[0]);\n\n              // Shift palette chips\n              const $chip = $palette.find('.note-color-btn').last().detach();\n\n              // Set chip attributes\n              const color = $picker.val();\n              $chip.css('background-color', color)\n                .attr('aria-label', color)\n                .attr('data-value', color)\n                .attr('data-original-title', color);\n              $palette.prepend($chip);\n              $picker.click();\n            } else {\n              if (lists.contains(['backColor', 'foreColor'], eventName)) {\n                const key = eventName === 'backColor' ? 'background-color' : 'color';\n                const $color = $button.closest('.note-color').find('.note-recent-color');\n                const $currentButton = $button.closest('.note-color').find('.note-current-color-button');\n\n                $color.css(key, value);\n                $currentButton.attr('data-' + eventName, value);\n              }\n              this.context.invoke('editor.' + eventName, value);\n            }\n          },\n        }),\n      ],\n    }).render();\n  }\n\n  addToolbarButtons() {\n    this.context.memo('button.style', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(\n            this.ui.icon(this.options.icons.magic), this.options\n          ),\n          tooltip: this.lang.style.style,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdown({\n          className: 'dropdown-style',\n          items: this.options.styleTags,\n          title: this.lang.style.style,\n          template: (item) => {\n            // TBD: need to be simplified\n            if (typeof item === 'string') {\n              item = {\n                tag: item,\n                title: (Object.prototype.hasOwnProperty.call(this.lang.style, item) ? this.lang.style[item] : item),\n              };\n            }\n\n            const tag = item.tag;\n            const title = item.title;\n            const style = item.style ? ' style=\"' + item.style + '\" ' : '';\n            const className = item.className ? ' class=\"' + item.className + '\"' : '';\n\n            return '<' + tag + style + className + '>' + title + '</' + tag + '>';\n          },\n          click: this.context.createInvokeHandler('editor.formatBlock'),\n        }),\n      ]).render();\n    });\n\n    for (let styleIdx = 0, styleLen = this.options.styleTags.length; styleIdx < styleLen; styleIdx++) {\n      const item = this.options.styleTags[styleIdx];\n\n      this.context.memo('button.style.' + item, () => {\n        return this.button({\n          className: 'note-btn-style-' + item,\n          contents: '<div data-value=\"' + item + '\">' + item.toUpperCase() + '</div>',\n          tooltip: this.lang.style[item],\n          click: this.context.createInvokeHandler('editor.formatBlock'),\n        }).render();\n      });\n    }\n\n    this.context.memo('button.bold', () => {\n      return this.button({\n        className: 'note-btn-bold',\n        contents: this.ui.icon(this.options.icons.bold),\n        tooltip: this.lang.font.bold + this.representShortcut('bold'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.bold'),\n      }).render();\n    });\n\n    this.context.memo('button.italic', () => {\n      return this.button({\n        className: 'note-btn-italic',\n        contents: this.ui.icon(this.options.icons.italic),\n        tooltip: this.lang.font.italic + this.representShortcut('italic'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.italic'),\n      }).render();\n    });\n\n    this.context.memo('button.underline', () => {\n      return this.button({\n        className: 'note-btn-underline',\n        contents: this.ui.icon(this.options.icons.underline),\n        tooltip: this.lang.font.underline + this.representShortcut('underline'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.underline'),\n      }).render();\n    });\n\n    this.context.memo('button.clear', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.eraser),\n        tooltip: this.lang.font.clear + this.representShortcut('removeFormat'),\n        click: this.context.createInvokeHandler('editor.removeFormat'),\n      }).render();\n    });\n\n    this.context.memo('button.strikethrough', () => {\n      return this.button({\n        className: 'note-btn-strikethrough',\n        contents: this.ui.icon(this.options.icons.strikethrough),\n        tooltip: this.lang.font.strikethrough + this.representShortcut('strikethrough'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.strikethrough'),\n      }).render();\n    });\n\n    this.context.memo('button.superscript', () => {\n      return this.button({\n        className: 'note-btn-superscript',\n        contents: this.ui.icon(this.options.icons.superscript),\n        tooltip: this.lang.font.superscript,\n        click: this.context.createInvokeHandlerAndUpdateState('editor.superscript'),\n      }).render();\n    });\n\n    this.context.memo('button.subscript', () => {\n      return this.button({\n        className: 'note-btn-subscript',\n        contents: this.ui.icon(this.options.icons.subscript),\n        tooltip: this.lang.font.subscript,\n        click: this.context.createInvokeHandlerAndUpdateState('editor.subscript'),\n      }).render();\n    });\n\n    this.context.memo('button.fontname', () => {\n      const styleInfo = this.context.invoke('editor.currentStyle');\n\n      if (this.options.addDefaultFonts) {\n        // Add 'default' fonts into the fontnames array if not exist\n        $.each(styleInfo['font-family'].split(','), (idx, fontname) => {\n          fontname = fontname.trim().replace(/['\"]+/g, '');\n          if (this.isFontDeservedToAdd(fontname)) {\n            if (this.options.fontNames.indexOf(fontname) === -1) {\n              this.options.fontNames.push(fontname);\n            }\n          }\n        });\n      }\n\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(\n            '<span class=\"note-current-fontname\"></span>', this.options\n          ),\n          tooltip: this.lang.font.name,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdownCheck({\n          className: 'dropdown-fontname',\n          checkClassName: this.options.icons.menuCheck,\n          items: this.options.fontNames.filter(this.isFontInstalled.bind(this)),\n          title: this.lang.font.name,\n          template: (item) => {\n            return '<span style=\"font-family: ' + env.validFontName(item) + '\">' + item + '</span>';\n          },\n          click: this.context.createInvokeHandlerAndUpdateState('editor.fontName'),\n        }),\n      ]).render();\n    });\n\n    this.context.memo('button.fontsize', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents('<span class=\"note-current-fontsize\"></span>', this.options),\n          tooltip: this.lang.font.size,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdownCheck({\n          className: 'dropdown-fontsize',\n          checkClassName: this.options.icons.menuCheck,\n          items: this.options.fontSizes,\n          title: this.lang.font.size,\n          click: this.context.createInvokeHandlerAndUpdateState('editor.fontSize'),\n        }),\n      ]).render();\n    });\n\n    this.context.memo('button.fontsizeunit', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents('<span class=\"note-current-fontsizeunit\"></span>', this.options),\n          tooltip: this.lang.font.sizeunit,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdownCheck({\n          className: 'dropdown-fontsizeunit',\n          checkClassName: this.options.icons.menuCheck,\n          items: this.options.fontSizeUnits,\n          title: this.lang.font.sizeunit,\n          click: this.context.createInvokeHandlerAndUpdateState('editor.fontSizeUnit'),\n        }),\n      ]).render();\n    });\n\n    this.context.memo('button.color', () => {\n      return this.colorPalette('note-color-all', this.lang.color.recent, true, true);\n    });\n\n    this.context.memo('button.forecolor', () => {\n      return this.colorPalette('note-color-fore', this.lang.color.foreground, false, true);\n    });\n\n    this.context.memo('button.backcolor', () => {\n      return this.colorPalette('note-color-back', this.lang.color.background, true, false);\n    });\n\n    this.context.memo('button.ul', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.unorderedlist),\n        tooltip: this.lang.lists.unordered + this.representShortcut('insertUnorderedList'),\n        click: this.context.createInvokeHandler('editor.insertUnorderedList'),\n      }).render();\n    });\n\n    this.context.memo('button.ol', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.orderedlist),\n        tooltip: this.lang.lists.ordered + this.representShortcut('insertOrderedList'),\n        click: this.context.createInvokeHandler('editor.insertOrderedList'),\n      }).render();\n    });\n\n    const justifyLeft = this.button({\n      contents: this.ui.icon(this.options.icons.alignLeft),\n      tooltip: this.lang.paragraph.left + this.representShortcut('justifyLeft'),\n      click: this.context.createInvokeHandler('editor.justifyLeft'),\n    });\n\n    const justifyCenter = this.button({\n      contents: this.ui.icon(this.options.icons.alignCenter),\n      tooltip: this.lang.paragraph.center + this.representShortcut('justifyCenter'),\n      click: this.context.createInvokeHandler('editor.justifyCenter'),\n    });\n\n    const justifyRight = this.button({\n      contents: this.ui.icon(this.options.icons.alignRight),\n      tooltip: this.lang.paragraph.right + this.representShortcut('justifyRight'),\n      click: this.context.createInvokeHandler('editor.justifyRight'),\n    });\n\n    const justifyFull = this.button({\n      contents: this.ui.icon(this.options.icons.alignJustify),\n      tooltip: this.lang.paragraph.justify + this.representShortcut('justifyFull'),\n      click: this.context.createInvokeHandler('editor.justifyFull'),\n    });\n\n    const outdent = this.button({\n      contents: this.ui.icon(this.options.icons.outdent),\n      tooltip: this.lang.paragraph.outdent + this.representShortcut('outdent'),\n      click: this.context.createInvokeHandler('editor.outdent'),\n    });\n\n    const indent = this.button({\n      contents: this.ui.icon(this.options.icons.indent),\n      tooltip: this.lang.paragraph.indent + this.representShortcut('indent'),\n      click: this.context.createInvokeHandler('editor.indent'),\n    });\n\n    this.context.memo('button.justifyLeft', func.invoke(justifyLeft, 'render'));\n    this.context.memo('button.justifyCenter', func.invoke(justifyCenter, 'render'));\n    this.context.memo('button.justifyRight', func.invoke(justifyRight, 'render'));\n    this.context.memo('button.justifyFull', func.invoke(justifyFull, 'render'));\n    this.context.memo('button.outdent', func.invoke(outdent, 'render'));\n    this.context.memo('button.indent', func.invoke(indent, 'render'));\n\n    this.context.memo('button.paragraph', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.alignLeft), this.options),\n          tooltip: this.lang.paragraph.paragraph,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdown([\n          this.ui.buttonGroup({\n            className: 'note-align',\n            children: [justifyLeft, justifyCenter, justifyRight, justifyFull],\n          }),\n          this.ui.buttonGroup({\n            className: 'note-list',\n            children: [outdent, indent],\n          }),\n        ]),\n      ]).render();\n    });\n\n    this.context.memo('button.height', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.textHeight), this.options),\n          tooltip: this.lang.font.height,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdownCheck({\n          items: this.options.lineHeights,\n          checkClassName: this.options.icons.menuCheck,\n          className: 'dropdown-line-height',\n          title: this.lang.font.height,\n          click: this.context.createInvokeHandler('editor.lineHeight'),\n        }),\n      ]).render();\n    });\n\n    this.context.memo('button.table', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.table), this.options),\n          tooltip: this.lang.table.table,\n          data: {\n            toggle: 'dropdown',\n          },\n        }),\n        this.ui.dropdown({\n          title: this.lang.table.table,\n          className: 'note-table',\n          items: [\n            '<div class=\"note-dimension-picker\">',\n              '<div class=\"note-dimension-picker-mousecatcher\" data-event=\"insertTable\" data-value=\"1x1\"></div>',\n              '<div class=\"note-dimension-picker-highlighted\"></div>',\n              '<div class=\"note-dimension-picker-unhighlighted\"></div>',\n            '</div>',\n            '<div class=\"note-dimension-display\">1 x 1</div>',\n          ].join(''),\n        }),\n      ], {\n        callback: ($node) => {\n          const $catcher = $node.find('.note-dimension-picker-mousecatcher');\n          $catcher.css({\n            width: this.options.insertTableMaxSize.col + 'em',\n            height: this.options.insertTableMaxSize.row + 'em',\n          }).mousedown(this.context.createInvokeHandler('editor.insertTable'))\n            .on('mousemove', this.tableMoveHandler.bind(this));\n        },\n      }).render();\n    });\n\n    this.context.memo('button.link', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.link),\n        tooltip: this.lang.link.link + this.representShortcut('linkDialog.show'),\n        click: this.context.createInvokeHandler('linkDialog.show'),\n      }).render();\n    });\n\n    this.context.memo('button.picture', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.picture),\n        tooltip: this.lang.image.image,\n        click: this.context.createInvokeHandler('imageDialog.show'),\n      }).render();\n    });\n\n    this.context.memo('button.video', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.video),\n        tooltip: this.lang.video.video,\n        click: this.context.createInvokeHandler('videoDialog.show'),\n      }).render();\n    });\n\n    this.context.memo('button.hr', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.minus),\n        tooltip: this.lang.hr.insert + this.representShortcut('insertHorizontalRule'),\n        click: this.context.createInvokeHandler('editor.insertHorizontalRule'),\n      }).render();\n    });\n\n    this.context.memo('button.fullscreen', () => {\n      return this.button({\n        className: 'btn-fullscreen note-codeview-keep',\n        contents: this.ui.icon(this.options.icons.arrowsAlt),\n        tooltip: this.lang.options.fullscreen,\n        click: this.context.createInvokeHandler('fullscreen.toggle'),\n      }).render();\n    });\n\n    this.context.memo('button.codeview', () => {\n      return this.button({\n        className: 'btn-codeview note-codeview-keep',\n        contents: this.ui.icon(this.options.icons.code),\n        tooltip: this.lang.options.codeview,\n        click: this.context.createInvokeHandler('codeview.toggle'),\n      }).render();\n    });\n\n    this.context.memo('button.redo', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.redo),\n        tooltip: this.lang.history.redo + this.representShortcut('redo'),\n        click: this.context.createInvokeHandler('editor.redo'),\n      }).render();\n    });\n\n    this.context.memo('button.undo', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.undo),\n        tooltip: this.lang.history.undo + this.representShortcut('undo'),\n        click: this.context.createInvokeHandler('editor.undo'),\n      }).render();\n    });\n\n    this.context.memo('button.help', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.question),\n        tooltip: this.lang.options.help,\n        click: this.context.createInvokeHandler('helpDialog.show'),\n      }).render();\n    });\n  }\n\n  /**\n   * image: [\n   *   ['imageResize', ['resizeFull', 'resizeHalf', 'resizeQuarter', 'resizeNone']],\n   *   ['float', ['floatLeft', 'floatRight', 'floatNone']],\n   *   ['remove', ['removeMedia']],\n   * ],\n   */\n  addImagePopoverButtons() {\n    // Image Size Buttons\n    this.context.memo('button.resizeFull', () => {\n      return this.button({\n        contents: '<span class=\"note-fontsize-10\">100%</span>',\n        tooltip: this.lang.image.resizeFull,\n        click: this.context.createInvokeHandler('editor.resize', '1'),\n      }).render();\n    });\n    this.context.memo('button.resizeHalf', () => {\n      return this.button({\n        contents: '<span class=\"note-fontsize-10\">50%</span>',\n        tooltip: this.lang.image.resizeHalf,\n        click: this.context.createInvokeHandler('editor.resize', '0.5'),\n      }).render();\n    });\n    this.context.memo('button.resizeQuarter', () => {\n      return this.button({\n        contents: '<span class=\"note-fontsize-10\">25%</span>',\n        tooltip: this.lang.image.resizeQuarter,\n        click: this.context.createInvokeHandler('editor.resize', '0.25'),\n      }).render();\n    });\n    this.context.memo('button.resizeNone', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.rollback),\n        tooltip: this.lang.image.resizeNone,\n        click: this.context.createInvokeHandler('editor.resize', '0'),\n      }).render();\n    });\n\n    // Float Buttons\n    this.context.memo('button.floatLeft', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.floatLeft),\n        tooltip: this.lang.image.floatLeft,\n        click: this.context.createInvokeHandler('editor.floatMe', 'left'),\n      }).render();\n    });\n\n    this.context.memo('button.floatRight', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.floatRight),\n        tooltip: this.lang.image.floatRight,\n        click: this.context.createInvokeHandler('editor.floatMe', 'right'),\n      }).render();\n    });\n\n    this.context.memo('button.floatNone', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.rollback),\n        tooltip: this.lang.image.floatNone,\n        click: this.context.createInvokeHandler('editor.floatMe', 'none'),\n      }).render();\n    });\n\n    // Remove Buttons\n    this.context.memo('button.removeMedia', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.trash),\n        tooltip: this.lang.image.remove,\n        click: this.context.createInvokeHandler('editor.removeMedia'),\n      }).render();\n    });\n  }\n\n  addLinkPopoverButtons() {\n    this.context.memo('button.linkDialogShow', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.link),\n        tooltip: this.lang.link.edit,\n        click: this.context.createInvokeHandler('linkDialog.show'),\n      }).render();\n    });\n\n    this.context.memo('button.unlink', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.unlink),\n        tooltip: this.lang.link.unlink,\n        click: this.context.createInvokeHandler('editor.unlink'),\n      }).render();\n    });\n  }\n\n  /**\n   * table : [\n   *  ['add', ['addRowDown', 'addRowUp', 'addColLeft', 'addColRight']],\n   *  ['delete', ['deleteRow', 'deleteCol', 'deleteTable']]\n   * ],\n   */\n  addTablePopoverButtons() {\n    this.context.memo('button.addRowUp', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.rowAbove),\n        tooltip: this.lang.table.addRowAbove,\n        click: this.context.createInvokeHandler('editor.addRow', 'top'),\n      }).render();\n    });\n    this.context.memo('button.addRowDown', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.rowBelow),\n        tooltip: this.lang.table.addRowBelow,\n        click: this.context.createInvokeHandler('editor.addRow', 'bottom'),\n      }).render();\n    });\n    this.context.memo('button.addColLeft', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.colBefore),\n        tooltip: this.lang.table.addColLeft,\n        click: this.context.createInvokeHandler('editor.addCol', 'left'),\n      }).render();\n    });\n    this.context.memo('button.addColRight', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.colAfter),\n        tooltip: this.lang.table.addColRight,\n        click: this.context.createInvokeHandler('editor.addCol', 'right'),\n      }).render();\n    });\n    this.context.memo('button.deleteRow', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.rowRemove),\n        tooltip: this.lang.table.delRow,\n        click: this.context.createInvokeHandler('editor.deleteRow'),\n      }).render();\n    });\n    this.context.memo('button.deleteCol', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.colRemove),\n        tooltip: this.lang.table.delCol,\n        click: this.context.createInvokeHandler('editor.deleteCol'),\n      }).render();\n    });\n    this.context.memo('button.deleteTable', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.trash),\n        tooltip: this.lang.table.delTable,\n        click: this.context.createInvokeHandler('editor.deleteTable'),\n      }).render();\n    });\n  }\n\n  build($container, groups) {\n    for (let groupIdx = 0, groupLen = groups.length; groupIdx < groupLen; groupIdx++) {\n      const group = groups[groupIdx];\n      const groupName = Array.isArray(group) ? group[0] : group;\n      const buttons = Array.isArray(group) ? ((group.length === 1) ? [group[0]] : group[1]) : [group];\n\n      const $group = this.ui.buttonGroup({\n        className: 'note-' + groupName,\n      }).render();\n\n      for (let idx = 0, len = buttons.length; idx < len; idx++) {\n        const btn = this.context.memo('button.' + buttons[idx]);\n        if (btn) {\n          $group.append(typeof btn === 'function' ? btn(this.context) : btn);\n        }\n      }\n      $group.appendTo($container);\n    }\n  }\n\n  /**\n   * @param {jQuery} [$container]\n   */\n  updateCurrentStyle($container) {\n    const $cont = $container || this.$toolbar;\n\n    const styleInfo = this.context.invoke('editor.currentStyle');\n    this.updateBtnStates($cont, {\n      '.note-btn-bold': () => {\n        return styleInfo['font-bold'] === 'bold';\n      },\n      '.note-btn-italic': () => {\n        return styleInfo['font-italic'] === 'italic';\n      },\n      '.note-btn-underline': () => {\n        return styleInfo['font-underline'] === 'underline';\n      },\n      '.note-btn-subscript': () => {\n        return styleInfo['font-subscript'] === 'subscript';\n      },\n      '.note-btn-superscript': () => {\n        return styleInfo['font-superscript'] === 'superscript';\n      },\n      '.note-btn-strikethrough': () => {\n        return styleInfo['font-strikethrough'] === 'strikethrough';\n      },\n    });\n\n    if (styleInfo['font-family']) {\n      const fontNames = styleInfo['font-family'].split(',').map((name) => {\n        return name.replace(/[\\'\\\"]/g, '')\n          .replace(/\\s+$/, '')\n          .replace(/^\\s+/, '');\n      });\n      const fontName = lists.find(fontNames, this.isFontInstalled.bind(this));\n\n      $cont.find('.dropdown-fontname a').each((idx, item) => {\n        const $item = $(item);\n        // always compare string to avoid creating another func.\n        const isChecked = ($item.data('value') + '') === (fontName + '');\n        $item.toggleClass('checked', isChecked);\n      });\n      $cont.find('.note-current-fontname').text(fontName).css('font-family', fontName);\n    }\n\n    if (styleInfo['font-size']) {\n      const fontSize = styleInfo['font-size'];\n      $cont.find('.dropdown-fontsize a').each((idx, item) => {\n        const $item = $(item);\n        // always compare with string to avoid creating another func.\n        const isChecked = ($item.data('value') + '') === (fontSize + '');\n        $item.toggleClass('checked', isChecked);\n      });\n      $cont.find('.note-current-fontsize').text(fontSize);\n\n      const fontSizeUnit = styleInfo['font-size-unit'];\n      $cont.find('.dropdown-fontsizeunit a').each((idx, item) => {\n        const $item = $(item);\n        const isChecked = ($item.data('value') + '') === (fontSizeUnit + '');\n        $item.toggleClass('checked', isChecked);\n      });\n      $cont.find('.note-current-fontsizeunit').text(fontSizeUnit);\n    }\n\n    if (styleInfo['line-height']) {\n      const lineHeight = styleInfo['line-height'];\n      $cont.find('.dropdown-line-height li a').each((idx, item) => {\n        // always compare with string to avoid creating another func.\n        const isChecked = ($(item).data('value') + '') === (lineHeight + '');\n        this.className = isChecked ? 'checked' : '';\n      });\n    }\n  }\n\n  updateBtnStates($container, infos) {\n    $.each(infos, (selector, pred) => {\n      this.ui.toggleBtnActive($container.find(selector), pred());\n    });\n  }\n\n  tableMoveHandler(event) {\n    const PX_PER_EM = 18;\n    const $picker = $(event.target.parentNode); // target is mousecatcher\n    const $dimensionDisplay = $picker.next();\n    const $catcher = $picker.find('.note-dimension-picker-mousecatcher');\n    const $highlighted = $picker.find('.note-dimension-picker-highlighted');\n    const $unhighlighted = $picker.find('.note-dimension-picker-unhighlighted');\n\n    let posOffset;\n    // HTML5 with jQuery - e.offsetX is undefined in Firefox\n    if (event.offsetX === undefined) {\n      const posCatcher = $(event.target).offset();\n      posOffset = {\n        x: event.pageX - posCatcher.left,\n        y: event.pageY - posCatcher.top,\n      };\n    } else {\n      posOffset = {\n        x: event.offsetX,\n        y: event.offsetY,\n      };\n    }\n\n    const dim = {\n      c: Math.ceil(posOffset.x / PX_PER_EM) || 1,\n      r: Math.ceil(posOffset.y / PX_PER_EM) || 1,\n    };\n\n    $highlighted.css({ width: dim.c + 'em', height: dim.r + 'em' });\n    $catcher.data('value', dim.c + 'x' + dim.r);\n\n    if (dim.c > 3 && dim.c < this.options.insertTableMaxSize.col) {\n      $unhighlighted.css({ width: dim.c + 1 + 'em' });\n    }\n\n    if (dim.r > 3 && dim.r < this.options.insertTableMaxSize.row) {\n      $unhighlighted.css({ height: dim.r + 1 + 'em' });\n    }\n\n    $dimensionDisplay.html(dim.c + ' x ' + dim.r);\n  }\n}\n", "import $ from 'jquery';\nexport default class Toolbar {\n  constructor(context) {\n    this.context = context;\n\n    this.$window = $(window);\n    this.$document = $(document);\n\n    this.ui = $.summernote.ui;\n    this.$note = context.layoutInfo.note;\n    this.$editor = context.layoutInfo.editor;\n    this.$toolbar = context.layoutInfo.toolbar;\n    this.$editable = context.layoutInfo.editable;\n    this.$statusbar = context.layoutInfo.statusbar;\n    this.options = context.options;\n\n    this.isFollowing = false;\n    this.followScroll = this.followScroll.bind(this);\n  }\n\n  shouldInitialize() {\n    return !this.options.airMode;\n  }\n\n  initialize() {\n    this.options.toolbar = this.options.toolbar || [];\n\n    if (!this.options.toolbar.length) {\n      this.$toolbar.hide();\n    } else {\n      this.context.invoke('buttons.build', this.$toolbar, this.options.toolbar);\n    }\n\n    if (this.options.toolbarContainer) {\n      this.$toolbar.appendTo(this.options.toolbarContainer);\n    }\n\n    this.changeContainer(false);\n\n    this.$note.on('summernote.keyup summernote.mouseup summernote.change', () => {\n      this.context.invoke('buttons.updateCurrentStyle');\n    });\n\n    this.context.invoke('buttons.updateCurrentStyle');\n    if (this.options.followingToolbar) {\n      this.$window.on('scroll resize', this.followScroll);\n    }\n  }\n\n  destroy() {\n    this.$toolbar.children().remove();\n\n    if (this.options.followingToolbar) {\n      this.$window.off('scroll resize', this.followScroll);\n    }\n  }\n\n  followScroll() {\n    if (this.$editor.hasClass('fullscreen')) {\n      return false;\n    }\n\n    const editorHeight = this.$editor.outerHeight();\n    const editorWidth = this.$editor.width();\n    const toolbarHeight = this.$toolbar.height();\n    const statusbarHeight = this.$statusbar.height();\n\n    // check if the web app is currently using another static bar\n    let otherBarHeight = 0;\n    if (this.options.otherStaticBar) {\n      otherBarHeight = $(this.options.otherStaticBar).outerHeight();\n    }\n\n    const currentOffset = this.$document.scrollTop();\n    const editorOffsetTop = this.$editor.offset().top;\n    const editorOffsetBottom = editorOffsetTop + editorHeight;\n    const activateOffset = editorOffsetTop - otherBarHeight;\n    const deactivateOffsetBottom = editorOffsetBottom - otherBarHeight - toolbarHeight - statusbarHeight;\n\n    if (!this.isFollowing &&\n      (currentOffset > activateOffset) && (currentOffset < deactivateOffsetBottom - toolbarHeight)) {\n      this.isFollowing = true;\n      this.$editable.css({\n        marginTop: this.$toolbar.outerHeight(),\n      });\n      this.$toolbar.css({\n        position: 'fixed',\n        top: otherBarHeight,\n        width: editorWidth,\n        zIndex: 1000,\n      });\n    } else if (this.isFollowing &&\n      ((currentOffset < activateOffset) || (currentOffset > deactivateOffsetBottom))) {\n      this.isFollowing = false;\n      this.$toolbar.css({\n        position: 'relative',\n        top: 0,\n        width: '100%',\n        zIndex: 'auto',\n      });\n      this.$editable.css({\n        marginTop: '',\n      });\n    }\n  }\n\n  changeContainer(isFullscreen) {\n    if (isFullscreen) {\n      this.$toolbar.prependTo(this.$editor);\n    } else {\n      if (this.options.toolbarContainer) {\n        this.$toolbar.appendTo(this.options.toolbarContainer);\n      }\n    }\n    if (this.options.followingToolbar) {\n      this.followScroll();\n    }\n  }\n\n  updateFullscreen(isFullscreen) {\n    this.ui.toggleBtnActive(this.$toolbar.find('.btn-fullscreen'), isFullscreen);\n\n    this.changeContainer(isFullscreen);\n  }\n\n  updateCodeview(isCodeview) {\n    this.ui.toggleBtnActive(this.$toolbar.find('.btn-codeview'), isCodeview);\n    if (isCodeview) {\n      this.deactivate();\n    } else {\n      this.activate();\n    }\n  }\n\n  activate(isIncludeCodeview) {\n    let $btn = this.$toolbar.find('button');\n    if (!isIncludeCodeview) {\n      $btn = $btn.not('.note-codeview-keep');\n    }\n    this.ui.toggleBtn($btn, true);\n  }\n\n  deactivate(isIncludeCodeview) {\n    let $btn = this.$toolbar.find('button');\n    if (!isIncludeCodeview) {\n      $btn = $btn.not('.note-codeview-keep');\n    }\n    this.ui.toggleBtn($btn, false);\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\nimport func from '../core/func';\n\nexport default class LinkDialog {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n\n    context.memo('help.linkDialog.show', this.options.langInfo.help['linkDialog.show']);\n  }\n\n  initialize() {\n    const $container = this.options.dialogsInBody ? this.$body : this.options.container;\n    const body = [\n      '<div class=\"form-group note-form-group\">',\n        `<label for=\"note-dialog-link-txt-${this.options.id}\" class=\"note-form-label\">${this.lang.link.textToDisplay}</label>`,\n        `<input id=\"note-dialog-link-txt-${this.options.id}\" class=\"note-link-text form-control note-form-control note-input\" type=\"text\"/>`,\n      '</div>',\n      '<div class=\"form-group note-form-group\">',\n        `<label for=\"note-dialog-link-url-${this.options.id}\" class=\"note-form-label\">${this.lang.link.url}</label>`,\n        `<input id=\"note-dialog-link-url-${this.options.id}\" class=\"note-link-url form-control note-form-control note-input\" type=\"text\" value=\"http://\"/>`,\n      '</div>',\n      !this.options.disableLinkTarget\n        ? $('<div/>').append(this.ui.checkbox({\n          className: 'sn-checkbox-open-in-new-window',\n          text: this.lang.link.openInNewWindow,\n          checked: true,\n        }).render()).html()\n        : '',\n      $('<div/>').append(this.ui.checkbox({\n        className: 'sn-checkbox-use-protocol',\n        text: this.lang.link.useProtocol,\n        checked: true,\n      }).render()).html(),\n    ].join('');\n\n    const buttonClass = 'btn btn-primary note-btn note-btn-primary note-link-btn';\n    const footer = `<input type=\"button\" href=\"#\" class=\"${buttonClass}\" value=\"${this.lang.link.insert}\" disabled>`;\n\n    this.$dialog = this.ui.dialog({\n      className: 'link-dialog',\n      title: this.lang.link.insert,\n      fade: this.options.dialogsFade,\n      body: body,\n      footer: footer,\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  bindEnterKey($input, $btn) {\n    $input.on('keypress', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        event.preventDefault();\n        $btn.trigger('click');\n      }\n    });\n  }\n\n  /**\n   * toggle update button\n   */\n  toggleLinkBtn($linkBtn, $linkText, $linkUrl) {\n    this.ui.toggleBtn($linkBtn, $linkText.val() && $linkUrl.val());\n  }\n\n  /**\n   * Show link dialog and set event handlers on dialog controls.\n   *\n   * @param {Object} linkInfo\n   * @return {Promise}\n   */\n  showLinkDialog(linkInfo) {\n    return $.Deferred((deferred) => {\n      const $linkText = this.$dialog.find('.note-link-text');\n      const $linkUrl = this.$dialog.find('.note-link-url');\n      const $linkBtn = this.$dialog.find('.note-link-btn');\n      const $openInNewWindow = this.$dialog\n        .find('.sn-checkbox-open-in-new-window input[type=checkbox]');\n      const $useProtocol = this.$dialog\n        .find('.sn-checkbox-use-protocol input[type=checkbox]');\n\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n\n        // If no url was given and given text is valid URL then copy that into URL Field\n        if (!linkInfo.url && func.isValidUrl(linkInfo.text)) {\n          linkInfo.url = linkInfo.text;\n        }\n\n        $linkText.on('input paste propertychange', () => {\n          // If linktext was modified by input events,\n          // cloning text from linkUrl will be stopped.\n          linkInfo.text = $linkText.val();\n          this.toggleLinkBtn($linkBtn, $linkText, $linkUrl);\n        }).val(linkInfo.text);\n\n        $linkUrl.on('input paste propertychange', () => {\n          // Display same text on `Text to display` as default\n          // when linktext has no text\n          if (!linkInfo.text) {\n            $linkText.val($linkUrl.val());\n          }\n          this.toggleLinkBtn($linkBtn, $linkText, $linkUrl);\n        }).val(linkInfo.url);\n\n        if (!env.isSupportTouch) {\n          $linkUrl.trigger('focus');\n        }\n\n        this.toggleLinkBtn($linkBtn, $linkText, $linkUrl);\n        this.bindEnterKey($linkUrl, $linkBtn);\n        this.bindEnterKey($linkText, $linkBtn);\n\n        const isNewWindowChecked = linkInfo.isNewWindow !== undefined\n          ? linkInfo.isNewWindow : this.context.options.linkTargetBlank;\n\n        $openInNewWindow.prop('checked', isNewWindowChecked);\n\n        const useProtocolChecked = linkInfo.url\n          ? false : this.context.options.useProtocol;\n\n        $useProtocol.prop('checked', useProtocolChecked);\n\n        $linkBtn.one('click', (event) => {\n          event.preventDefault();\n\n          deferred.resolve({\n            range: linkInfo.range,\n            url: $linkUrl.val(),\n            text: $linkText.val(),\n            isNewWindow: $openInNewWindow.is(':checked'),\n            checkProtocol: $useProtocol.is(':checked'),\n          });\n          this.ui.hideDialog(this.$dialog);\n        });\n      });\n\n      this.ui.onDialogHidden(this.$dialog, () => {\n        // detach events\n        $linkText.off();\n        $linkUrl.off();\n        $linkBtn.off();\n\n        if (deferred.state() === 'pending') {\n          deferred.reject();\n        }\n      });\n\n      this.ui.showDialog(this.$dialog);\n    }).promise();\n  }\n\n  /**\n   * @param {Object} layoutInfo\n   */\n  show() {\n    const linkInfo = this.context.invoke('editor.getLinkInfo');\n\n    this.context.invoke('editor.saveRange');\n    this.showLinkDialog(linkInfo).then((linkInfo) => {\n      this.context.invoke('editor.restoreRange');\n      this.context.invoke('editor.createLink', linkInfo);\n    }).fail(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\nexport default class LinkPopover {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.options = context.options;\n    this.events = {\n      'summernote.keyup summernote.mouseup summernote.change summernote.scroll': () => {\n        this.update();\n      },\n      'summernote.disable summernote.dialog.shown summernote.blur': () => {\n        this.hide();\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return !lists.isEmpty(this.options.popover.link);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-link-popover',\n      callback: ($node) => {\n        const $content = $node.find('.popover-content,.note-popover-content');\n        $content.prepend('<span><a target=\"_blank\"></a>&nbsp;</span>');\n      },\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content,.note-popover-content');\n\n    this.context.invoke('buttons.build', $content, this.options.popover.link);\n\n    this.$popover.on('mousedown', (e) => { e.preventDefault(); });\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update() {\n    // Prevent focusing on editable when invoke('code') is executed\n    if (!this.context.invoke('editor.hasFocus')) {\n      this.hide();\n      return;\n    }\n\n    const rng = this.context.invoke('editor.getLastRange');\n    if (rng.isCollapsed() && rng.isOnAnchor()) {\n      const anchor = dom.ancestor(rng.sc, dom.isAnchor);\n      const href = $(anchor).attr('href');\n      this.$popover.find('a').attr('href', href).text(href);\n\n      const pos = dom.posFromPlaceholder(anchor);\n      const containerOffset = $(this.options.container).offset();\n      pos.top -= containerOffset.top;\n      pos.left -= containerOffset.left;\n\n      this.$popover.css({\n        display: 'block',\n        left: pos.left,\n        top: pos.top,\n      });\n    } else {\n      this.hide();\n    }\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\n\nexport default class ImageDialog {\n  constructor(context) {\n    this.context = context;\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n  }\n\n  initialize() {\n    let imageLimitation = '';\n    if (this.options.maximumImageFileSize) {\n      const unit = Math.floor(Math.log(this.options.maximumImageFileSize) / Math.log(1024));\n      const readableSize = (this.options.maximumImageFileSize / Math.pow(1024, unit)).toFixed(2) * 1 +\n                         ' ' + ' KMGTP'[unit] + 'B';\n      imageLimitation = `<small>${this.lang.image.maximumFileSize + ' : ' + readableSize}</small>`;\n    }\n\n    const $container = this.options.dialogsInBody ? this.$body : this.options.container;\n    const body = [\n      '<div class=\"form-group note-form-group note-group-select-from-files\">',\n        '<label for=\"note-dialog-image-file-' + this.options.id + '\" class=\"note-form-label\">' + this.lang.image.selectFromFiles + '</label>',\n        '<input id=\"note-dialog-image-file-' + this.options.id + '\" class=\"note-image-input form-control-file note-form-control note-input\" ',\n        ' type=\"file\" name=\"files\" accept=\"image/*\" multiple=\"multiple\"/>',\n        imageLimitation,\n      '</div>',\n      '<div class=\"form-group note-group-image-url\">',\n        '<label for=\"note-dialog-image-url-' + this.options.id + '\" class=\"note-form-label\">' + this.lang.image.url + '</label>',\n        '<input id=\"note-dialog-image-url-' + this.options.id + '\" class=\"note-image-url form-control note-form-control note-input\" type=\"text\"/>',\n      '</div>',\n    ].join('');\n    const buttonClass = 'btn btn-primary note-btn note-btn-primary note-image-btn';\n    const footer = `<input type=\"button\" href=\"#\" class=\"${buttonClass}\" value=\"${this.lang.image.insert}\" disabled>`;\n\n    this.$dialog = this.ui.dialog({\n      title: this.lang.image.insert,\n      fade: this.options.dialogsFade,\n      body: body,\n      footer: footer,\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  bindEnterKey($input, $btn) {\n    $input.on('keypress', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        event.preventDefault();\n        $btn.trigger('click');\n      }\n    });\n  }\n\n  show() {\n    this.context.invoke('editor.saveRange');\n    this.showImageDialog().then((data) => {\n      // [workaround] hide dialog before restore range for IE range focus\n      this.ui.hideDialog(this.$dialog);\n      this.context.invoke('editor.restoreRange');\n\n      if (typeof data === 'string') { // image url\n        // If onImageLinkInsert set,\n        if (this.options.callbacks.onImageLinkInsert) {\n          this.context.triggerEvent('image.link.insert', data);\n        } else {\n          this.context.invoke('editor.insertImage', data);\n        }\n      } else { // array of files\n        this.context.invoke('editor.insertImagesOrCallback', data);\n      }\n    }).fail(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n\n  /**\n   * show image dialog\n   *\n   * @param {jQuery} $dialog\n   * @return {Promise}\n   */\n  showImageDialog() {\n    return $.Deferred((deferred) => {\n      const $imageInput = this.$dialog.find('.note-image-input');\n      const $imageUrl = this.$dialog.find('.note-image-url');\n      const $imageBtn = this.$dialog.find('.note-image-btn');\n\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n\n        // Cloning imageInput to clear element.\n        $imageInput.replaceWith($imageInput.clone().on('change', (event) => {\n          deferred.resolve(event.target.files || event.target.value);\n        }).val(''));\n\n        $imageUrl.on('input paste propertychange', () => {\n          this.ui.toggleBtn($imageBtn, $imageUrl.val());\n        }).val('');\n\n        if (!env.isSupportTouch) {\n          $imageUrl.trigger('focus');\n        }\n\n        $imageBtn.click((event) => {\n          event.preventDefault();\n          deferred.resolve($imageUrl.val());\n        });\n\n        this.bindEnterKey($imageUrl, $imageBtn);\n      });\n\n      this.ui.onDialogHidden(this.$dialog, () => {\n        $imageInput.off();\n        $imageUrl.off();\n        $imageBtn.off();\n\n        if (deferred.state() === 'pending') {\n          deferred.reject();\n        }\n      });\n\n      this.ui.showDialog(this.$dialog);\n    });\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\n/**\n * Image popover module\n *  mouse events that show/hide popover will be handled by Handle.js.\n *  Handle.js will receive the events and invoke 'imagePopover.update'.\n */\nexport default class ImagePopover {\n  constructor(context) {\n    this.context = context;\n    this.ui = $.summernote.ui;\n\n    this.editable = context.layoutInfo.editable[0];\n    this.options = context.options;\n\n    this.events = {\n      'summernote.disable summernote.blur': () => {\n        this.hide();\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return !lists.isEmpty(this.options.popover.image);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-image-popover',\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content,.note-popover-content');\n    this.context.invoke('buttons.build', $content, this.options.popover.image);\n\n    this.$popover.on('mousedown', (e) => { e.preventDefault(); });\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update(target, event) {\n    if (dom.isImg(target)) {\n      const position = $(target).offset();\n      const containerOffset = $(this.options.container).offset();\n      let pos = {};\n      if (this.options.popatmouse) {\n        pos.left = event.pageX - 20;\n        pos.top = event.pageY;\n      } else {\n        pos = position;\n      }\n      pos.top -= containerOffset.top;\n      pos.left -= containerOffset.left;\n\n      this.$popover.css({\n        display: 'block',\n        left: pos.left,\n        top: pos.top,\n      });\n    } else {\n      this.hide();\n    }\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\nexport default class TablePopover {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.options = context.options;\n    this.events = {\n      'summernote.mousedown': (we, e) => {\n        this.update(e.target);\n      },\n      'summernote.keyup summernote.scroll summernote.change': () => {\n        this.update();\n      },\n      'summernote.disable summernote.blur': () => {\n        this.hide();\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return !lists.isEmpty(this.options.popover.table);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-table-popover',\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content,.note-popover-content');\n\n    this.context.invoke('buttons.build', $content, this.options.popover.table);\n\n    // [workaround] Disable Firefox's default table editor\n    if (env.isFF) {\n      document.execCommand('enableInlineTableEditing', false, false);\n    }\n\n    this.$popover.on('mousedown', (e) => { e.preventDefault(); });\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update(target) {\n    if (this.context.isDisabled()) {\n      return false;\n    }\n\n    const isCell = dom.isCell(target);\n\n    if (isCell) {\n      const pos = dom.posFromPlaceholder(target);\n      const containerOffset = $(this.options.container).offset();\n      pos.top -= containerOffset.top;\n      pos.left -= containerOffset.left;\n\n      this.$popover.css({\n        display: 'block',\n        left: pos.left,\n        top: pos.top,\n      });\n    } else {\n      this.hide();\n    }\n\n    return isCell;\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\n\nexport default class VideoDialog {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n  }\n\n  initialize() {\n    const $container = this.options.dialogsInBody ? this.$body : this.options.container;\n    const body = [\n      '<div class=\"form-group note-form-group row-fluid\">',\n        `<label for=\"note-dialog-video-url-${this.options.id}\" class=\"note-form-label\">${this.lang.video.url} <small class=\"text-muted\">${this.lang.video.providers}</small></label>`,\n        `<input id=\"note-dialog-video-url-${this.options.id}\" class=\"note-video-url form-control note-form-control note-input\" type=\"text\"/>`,\n      '</div>',\n    ].join('');\n    const buttonClass = 'btn btn-primary note-btn note-btn-primary note-video-btn';\n    const footer = `<input type=\"button\" href=\"#\" class=\"${buttonClass}\" value=\"${this.lang.video.insert}\" disabled>`;\n\n    this.$dialog = this.ui.dialog({\n      title: this.lang.video.insert,\n      fade: this.options.dialogsFade,\n      body: body,\n      footer: footer,\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  bindEnterKey($input, $btn) {\n    $input.on('keypress', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        event.preventDefault();\n        $btn.trigger('click');\n      }\n    });\n  }\n\n  createVideoNode(url) {\n    // video url patterns(youtube, instagram, vimeo, dailymotion, youku, mp4, ogg, webm)\n    const ytRegExp = /\\/\\/(?:(?:www|m)\\.)?(?:youtu\\.be\\/|youtube\\.com\\/(?:embed\\/|v\\/|watch\\?v=|watch\\?.+&v=))([\\w|-]{11})(?:(?:[\\?&]t=)(\\S+))?$/;\n    const ytRegExpForStart = /^(?:(\\d+)h)?(?:(\\d+)m)?(?:(\\d+)s)?$/;\n    const ytMatch = url.match(ytRegExp);\n\n    const igRegExp = /(?:www\\.|\\/\\/)instagram\\.com\\/p\\/(.[a-zA-Z0-9_-]*)/;\n    const igMatch = url.match(igRegExp);\n\n    const vRegExp = /\\/\\/vine\\.co\\/v\\/([a-zA-Z0-9]+)/;\n    const vMatch = url.match(vRegExp);\n\n    const vimRegExp = /\\/\\/(player\\.)?vimeo\\.com\\/([a-z]*\\/)*(\\d+)[?]?.*/;\n    const vimMatch = url.match(vimRegExp);\n\n    const dmRegExp = /.+dailymotion.com\\/(video|hub)\\/([^_]+)[^#]*(#video=([^_&]+))?/;\n    const dmMatch = url.match(dmRegExp);\n\n    const youkuRegExp = /\\/\\/v\\.youku\\.com\\/v_show\\/id_(\\w+)=*\\.html/;\n    const youkuMatch = url.match(youkuRegExp);\n\n    const qqRegExp = /\\/\\/v\\.qq\\.com.*?vid=(.+)/;\n    const qqMatch = url.match(qqRegExp);\n\n    const qqRegExp2 = /\\/\\/v\\.qq\\.com\\/x?\\/?(page|cover).*?\\/([^\\/]+)\\.html\\??.*/;\n    const qqMatch2 = url.match(qqRegExp2);\n\n    const mp4RegExp = /^.+.(mp4|m4v)$/;\n    const mp4Match = url.match(mp4RegExp);\n\n    const oggRegExp = /^.+.(ogg|ogv)$/;\n    const oggMatch = url.match(oggRegExp);\n\n    const webmRegExp = /^.+.(webm)$/;\n    const webmMatch = url.match(webmRegExp);\n\n    const fbRegExp = /(?:www\\.|\\/\\/)facebook\\.com\\/([^\\/]+)\\/videos\\/([0-9]+)/;\n    const fbMatch = url.match(fbRegExp);\n\n    let $video;\n    if (ytMatch && ytMatch[1].length === 11) {\n      const youtubeId = ytMatch[1];\n      var start = 0;\n      if (typeof ytMatch[2] !== 'undefined') {\n        const ytMatchForStart = ytMatch[2].match(ytRegExpForStart);\n        if (ytMatchForStart) {\n          for (var n = [3600, 60, 1], i = 0, r = n.length; i < r; i++) {\n            start += (typeof ytMatchForStart[i + 1] !== 'undefined' ? n[i] * parseInt(ytMatchForStart[i + 1], 10) : 0);\n          }\n        }\n      }\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', '//www.youtube.com/embed/' + youtubeId + (start > 0 ? '?start=' + start : ''))\n        .attr('width', '640').attr('height', '360');\n    } else if (igMatch && igMatch[0].length) {\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', 'https://instagram.com/p/' + igMatch[1] + '/embed/')\n        .attr('width', '612').attr('height', '710')\n        .attr('scrolling', 'no')\n        .attr('allowtransparency', 'true');\n    } else if (vMatch && vMatch[0].length) {\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', vMatch[0] + '/embed/simple')\n        .attr('width', '600').attr('height', '600')\n        .attr('class', 'vine-embed');\n    } else if (vimMatch && vimMatch[3].length) {\n      $video = $('<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>')\n        .attr('frameborder', 0)\n        .attr('src', '//player.vimeo.com/video/' + vimMatch[3])\n        .attr('width', '640').attr('height', '360');\n    } else if (dmMatch && dmMatch[2].length) {\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', '//www.dailymotion.com/embed/video/' + dmMatch[2])\n        .attr('width', '640').attr('height', '360');\n    } else if (youkuMatch && youkuMatch[1].length) {\n      $video = $('<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>')\n        .attr('frameborder', 0)\n        .attr('height', '498')\n        .attr('width', '510')\n        .attr('src', '//player.youku.com/embed/' + youkuMatch[1]);\n    } else if ((qqMatch && qqMatch[1].length) || (qqMatch2 && qqMatch2[2].length)) {\n      const vid = ((qqMatch && qqMatch[1].length) ? qqMatch[1] : qqMatch2[2]);\n      $video = $('<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>')\n        .attr('frameborder', 0)\n        .attr('height', '310')\n        .attr('width', '500')\n        .attr('src', 'https://v.qq.com/txp/iframe/player.html?vid=' + vid + '&amp;auto=0');\n    } else if (mp4Match || oggMatch || webmMatch) {\n      $video = $('<video controls>')\n        .attr('src', url)\n        .attr('width', '640').attr('height', '360');\n    } else if (fbMatch && fbMatch[0].length) {\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', 'https://www.facebook.com/plugins/video.php?href=' + encodeURIComponent(fbMatch[0]) + '&show_text=0&width=560')\n        .attr('width', '560').attr('height', '301')\n        .attr('scrolling', 'no')\n        .attr('allowtransparency', 'true');\n    } else {\n      // this is not a known video link. Now what, Cat? Now what?\n      return false;\n    }\n\n    $video.addClass('note-video-clip');\n\n    return $video[0];\n  }\n\n  show() {\n    const text = this.context.invoke('editor.getSelectedText');\n    this.context.invoke('editor.saveRange');\n    this.showVideoDialog(text).then((url) => {\n      // [workaround] hide dialog before restore range for IE range focus\n      this.ui.hideDialog(this.$dialog);\n      this.context.invoke('editor.restoreRange');\n\n      // build node\n      const $node = this.createVideoNode(url);\n\n      if ($node) {\n        // insert video node\n        this.context.invoke('editor.insertNode', $node);\n      }\n    }).fail(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n\n  /**\n   * show video dialog\n   *\n   * @param {jQuery} $dialog\n   * @return {Promise}\n   */\n  showVideoDialog(/* text */) {\n    return $.Deferred((deferred) => {\n      const $videoUrl = this.$dialog.find('.note-video-url');\n      const $videoBtn = this.$dialog.find('.note-video-btn');\n\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n\n        $videoUrl.on('input paste propertychange', () => {\n          this.ui.toggleBtn($videoBtn, $videoUrl.val());\n        });\n\n        if (!env.isSupportTouch) {\n          $videoUrl.trigger('focus');\n        }\n\n        $videoBtn.click((event) => {\n          event.preventDefault();\n          deferred.resolve($videoUrl.val());\n        });\n\n        this.bindEnterKey($videoUrl, $videoBtn);\n      });\n\n      this.ui.onDialogHidden(this.$dialog, () => {\n        $videoUrl.off();\n        $videoBtn.off();\n\n        if (deferred.state() === 'pending') {\n          deferred.reject();\n        }\n      });\n\n      this.ui.showDialog(this.$dialog);\n    });\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\n\nexport default class HelpDialog {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n  }\n\n  initialize() {\n    const $container = this.options.dialogsInBody ? this.$body : this.options.container;\n    const body = [\n      '<p class=\"text-center\">',\n        '<a href=\"http://summernote.org/\" target=\"_blank\">Summernote @@VERSION@@</a> · ',\n        '<a href=\"https://github.com/summernote/summernote\" target=\"_blank\">Project</a> · ',\n        '<a href=\"https://github.com/summernote/summernote/issues\" target=\"_blank\">Issues</a>',\n      '</p>',\n    ].join('');\n\n    this.$dialog = this.ui.dialog({\n      title: this.lang.options.help,\n      fade: this.options.dialogsFade,\n      body: this.createShortcutList(),\n      footer: body,\n      callback: ($node) => {\n        $node.find('.modal-body,.note-modal-body').css({\n          'max-height': 300,\n          'overflow': 'scroll',\n        });\n      },\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  createShortcutList() {\n    const keyMap = this.options.keyMap[env.isMac ? 'mac' : 'pc'];\n    return Object.keys(keyMap).map((key) => {\n      const command = keyMap[key];\n      const $row = $('<div><div class=\"help-list-item\"></div></div>');\n      $row.append($('<label><kbd>' + key + '</kdb></label>').css({\n        'width': 180,\n        'margin-right': 10,\n      })).append($('<span/>').html(this.context.memo('help.' + command) || command));\n      return $row.html();\n    }).join('');\n  }\n\n  /**\n   * show help dialog\n   *\n   * @return {Promise}\n   */\n  showHelpDialog() {\n    return $.Deferred((deferred) => {\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n        deferred.resolve();\n      });\n      this.ui.showDialog(this.$dialog);\n    }).promise();\n  }\n\n  show() {\n    this.context.invoke('editor.saveRange');\n    this.showHelpDialog().then(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\n\nconst AIRMODE_POPOVER_X_OFFSET = -5;\nconst AIRMODE_POPOVER_Y_OFFSET = 5;\n\nexport default class AirPopover {\n  constructor(context) {\n    this.context = context;\n    this.ui = $.summernote.ui;\n    this.options = context.options;\n\n    this.hidable = true;\n    this.onContextmenu = false;\n    this.pageX = null;\n    this.pageY = null;\n\n    this.events = {\n      'summernote.contextmenu': (e) => {\n        if (this.options.editing) {\n          e.preventDefault();\n          e.stopPropagation();\n          this.onContextmenu = true;\n          this.update(true);\n        }\n      },\n      'summernote.mousedown': (we, e) => {\n        this.pageX = e.pageX;\n        this.pageY = e.pageY;\n      },\n      'summernote.keyup summernote.mouseup summernote.scroll': (we, e) => {\n        if (this.options.editing && !this.onContextmenu) {\n          this.pageX = e.pageX;\n          this.pageY = e.pageY;\n          this.update();\n        }\n        this.onContextmenu = false;\n      },\n      'summernote.disable summernote.change summernote.dialog.shown summernote.blur': () => {\n        this.hide();\n      },\n      'summernote.focusout': () => {\n        if (!this.$popover.is(':active,:focus')) {\n          this.hide();\n        }\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return this.options.airMode && !lists.isEmpty(this.options.popover.air);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-air-popover',\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content');\n\n    this.context.invoke('buttons.build', $content, this.options.popover.air);\n\n    // disable hiding this popover preemptively by 'summernote.blur' event.\n    this.$popover.on('mousedown', () => { this.hidable = false; });\n    // (re-)enable hiding after 'summernote.blur' has been handled (aka. ignored).\n    this.$popover.on('mouseup', () => { this.hidable = true; });\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update(forcelyOpen) {\n    const styleInfo = this.context.invoke('editor.currentStyle');\n    if (styleInfo.range && (!styleInfo.range.isCollapsed() || forcelyOpen)) {\n      let rect = {\n        left: this.pageX,\n        top: this.pageY,\n      };\n\n      const containerOffset = $(this.options.container).offset();\n      rect.top -= containerOffset.top;\n      rect.left -= containerOffset.left;\n\n      this.$popover.css({\n        display: 'block',\n        left: Math.max(rect.left, 0) + AIRMODE_POPOVER_X_OFFSET,\n        top: rect.top + AIRMODE_POPOVER_Y_OFFSET,\n      });\n      this.context.invoke('buttons.updateCurrentStyle', this.$popover);\n    } else {\n      this.hide();\n    }\n  }\n\n  updateCodeview(isCodeview) {\n    this.ui.toggleBtnActive(this.$popover.find('.btn-codeview'), isCodeview);\n    if (isCodeview) {\n      this.hide();\n    } \n  }\n\n  hide() {\n    if (this.hidable) {\n      this.$popover.hide();\n    }\n  }\n}\n", "import $ from 'jquery';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport key from '../core/key';\n\nconst POPOVER_DIST = 5;\n\nexport default class HintPopover {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n    this.hint = this.options.hint || [];\n    this.direction = this.options.hintDirection || 'bottom';\n    this.hints = Array.isArray(this.hint) ? this.hint : [this.hint];\n\n    this.events = {\n      'summernote.keyup': (we, e) => {\n        if (!e.isDefaultPrevented()) {\n          this.handleKeyup(e);\n        }\n      },\n      'summernote.keydown': (we, e) => {\n        this.handleKeydown(e);\n      },\n      'summernote.disable summernote.dialog.shown summernote.blur': () => {\n        this.hide();\n      },\n    };\n  }\n\n  shouldInitialize() {\n    return this.hints.length > 0;\n  }\n\n  initialize() {\n    this.lastWordRange = null;\n    this.matchingWord = null;\n    this.$popover = this.ui.popover({\n      className: 'note-hint-popover',\n      hideArrow: true,\n      direction: '',\n    }).render().appendTo(this.options.container);\n\n    this.$popover.hide();\n    this.$content = this.$popover.find('.popover-content,.note-popover-content');\n    this.$content.on('click', '.note-hint-item', (e) => {\n      this.$content.find('.active').removeClass('active');\n      $(e.currentTarget).addClass('active');\n      this.replace();\n    });\n\n    this.$popover.on('mousedown', (e) => { e.preventDefault(); });\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  selectItem($item) {\n    this.$content.find('.active').removeClass('active');\n    $item.addClass('active');\n\n    this.$content[0].scrollTop = $item[0].offsetTop - (this.$content.innerHeight() / 2);\n  }\n\n  moveDown() {\n    const $current = this.$content.find('.note-hint-item.active');\n    const $next = $current.next();\n\n    if ($next.length) {\n      this.selectItem($next);\n    } else {\n      let $nextGroup = $current.parent().next();\n\n      if (!$nextGroup.length) {\n        $nextGroup = this.$content.find('.note-hint-group').first();\n      }\n\n      this.selectItem($nextGroup.find('.note-hint-item').first());\n    }\n  }\n\n  moveUp() {\n    const $current = this.$content.find('.note-hint-item.active');\n    const $prev = $current.prev();\n\n    if ($prev.length) {\n      this.selectItem($prev);\n    } else {\n      let $prevGroup = $current.parent().prev();\n\n      if (!$prevGroup.length) {\n        $prevGroup = this.$content.find('.note-hint-group').last();\n      }\n\n      this.selectItem($prevGroup.find('.note-hint-item').last());\n    }\n  }\n\n  replace() {\n    const $item = this.$content.find('.note-hint-item.active');\n\n    if ($item.length) {\n      var node = this.nodeFromItem($item);\n      // If matchingWord length = 0 -> capture OK / open hint / but as mention capture \"\" (\\w*)\n      if (this.matchingWord !== null && this.matchingWord.length === 0) {\n        this.lastWordRange.so = this.lastWordRange.eo;\n      // Else si > 0 and normal case -> adjust range \"before\" for correct position of insertion\n      } else if (this.matchingWord !== null && this.matchingWord.length > 0 && !this.lastWordRange.isCollapsed()) {\n        let rangeCompute = this.lastWordRange.eo - this.lastWordRange.so - this.matchingWord.length;\n        if (rangeCompute > 0) {\n          this.lastWordRange.so += rangeCompute;\n        }\n      }\n      this.lastWordRange.insertNode(node);\n\n      if (this.options.hintSelect === 'next') {\n        var blank = document.createTextNode('');\n        $(node).after(blank);\n        range.createFromNodeBefore(blank).select();\n      } else {\n        range.createFromNodeAfter(node).select();\n      }\n\n      this.lastWordRange = null;\n      this.hide();\n      this.context.invoke('editor.focus');\n    }\n  }\n\n  nodeFromItem($item) {\n    const hint = this.hints[$item.data('index')];\n    const item = $item.data('item');\n    let node = hint.content ? hint.content(item) : item;\n    if (typeof node === 'string') {\n      node = dom.createText(node);\n    }\n    return node;\n  }\n\n  createItemTemplates(hintIdx, items) {\n    const hint = this.hints[hintIdx];\n    return items.map((item /*, idx */) => {\n      const $item = $('<div class=\"note-hint-item\"/>');\n      $item.append(hint.template ? hint.template(item) : item + '');\n      $item.data({\n        'index': hintIdx,\n        'item': item,\n      });\n      return $item;\n    });\n  }\n\n  handleKeydown(e) {\n    if (!this.$popover.is(':visible')) {\n      return;\n    }\n\n    if (e.keyCode === key.code.ENTER) {\n      e.preventDefault();\n      this.replace();\n    } else if (e.keyCode === key.code.UP) {\n      e.preventDefault();\n      this.moveUp();\n    } else if (e.keyCode === key.code.DOWN) {\n      e.preventDefault();\n      this.moveDown();\n    }\n  }\n\n  searchKeyword(index, keyword, callback) {\n    const hint = this.hints[index];\n    if (hint && hint.match.test(keyword) && hint.search) {\n      const matches = hint.match.exec(keyword);\n      this.matchingWord = matches[0];\n      hint.search(matches[1], callback);\n    } else {\n      callback();\n    }\n  }\n\n  createGroup(idx, keyword) {\n    const $group = $('<div class=\"note-hint-group note-hint-group-' + idx + '\"></div>');\n    this.searchKeyword(idx, keyword, (items) => {\n      items = items || [];\n      if (items.length) {\n        $group.html(this.createItemTemplates(idx, items));\n        this.show();\n      }\n    });\n\n    return $group;\n  }\n\n  handleKeyup(e) {\n    if (!lists.contains([key.code.ENTER, key.code.UP, key.code.DOWN], e.keyCode)) {\n      let range = this.context.invoke('editor.getLastRange');\n      let wordRange, keyword;\n      if (this.options.hintMode === 'words') {\n        wordRange = range.getWordsRange(range);\n        keyword = wordRange.toString();\n\n        this.hints.forEach((hint) => {\n          if (hint.match.test(keyword)) {\n            wordRange = range.getWordsMatchRange(hint.match);\n            return false;\n          }\n        });\n\n        if (!wordRange) {\n          this.hide();\n          return;\n        }\n\n        keyword = wordRange.toString();\n      } else {\n        wordRange = range.getWordRange();\n        keyword = wordRange.toString();\n      }\n\n      if (this.hints.length && keyword) {\n        this.$content.empty();\n\n        const bnd = func.rect2bnd(lists.last(wordRange.getClientRects()));\n        const containerOffset = $(this.options.container).offset();\n        if (bnd) {\n          bnd.top -= containerOffset.top;\n          bnd.left -= containerOffset.left;\n\n          this.$popover.hide();\n          this.lastWordRange = wordRange;\n          this.hints.forEach((hint, idx) => {\n            if (hint.match.test(keyword)) {\n              this.createGroup(idx, keyword).appendTo(this.$content);\n            }\n          });\n          // select first .note-hint-item\n          this.$content.find('.note-hint-item:first').addClass('active');\n\n          // set position for popover after group is created\n          if (this.direction === 'top') {\n            this.$popover.css({\n              left: bnd.left,\n              top: bnd.top - this.$popover.outerHeight() - POPOVER_DIST,\n            });\n          } else {\n            this.$popover.css({\n              left: bnd.left,\n              top: bnd.top + bnd.height + POPOVER_DIST,\n            });\n          }\n        }\n      } else {\n        this.hide();\n      }\n    }\n  }\n\n  show() {\n    this.$popover.show();\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport './summernote-en-US';\nimport '../summernote';\nimport dom from './core/dom';\nimport range from './core/range';\nimport lists from './core/lists';\nimport Editor from './module/Editor';\nimport Clipboard from './module/Clipboard';\nimport Dropzone from './module/Dropzone';\nimport Codeview from './module/Codeview';\nimport Statusbar from './module/Statusbar';\nimport Fullscreen from './module/Fullscreen';\nimport Handle from './module/Handle';\nimport AutoLink from './module/AutoLink';\nimport AutoSync from './module/AutoSync';\nimport AutoReplace from './module/AutoReplace';\nimport Placeholder from './module/Placeholder';\nimport Buttons from './module/Buttons';\nimport Toolbar from './module/Toolbar';\nimport LinkDialog from './module/LinkDialog';\nimport LinkPopover from './module/LinkPopover';\nimport ImageDialog from './module/ImageDialog';\nimport ImagePopover from './module/ImagePopover';\nimport TablePopover from './module/TablePopover';\nimport VideoDialog from './module/VideoDialog';\nimport HelpDialog from './module/HelpDialog';\nimport AirPopover from './module/AirPopover';\nimport HintPopover from './module/HintPopover';\n\n$.summernote = $.extend($.summernote, {\n  version: '@@VERSION@@',\n  plugins: {},\n\n  dom: dom,\n  range: range,\n  lists: lists,\n\n  options: {\n    langInfo: $.summernote.lang['en-US'],\n    editing: true,\n    modules: {\n      'editor': Editor,\n      'clipboard': Clipboard,\n      'dropzone': Dropzone,\n      'codeview': Codeview,\n      'statusbar': Statusbar,\n      'fullscreen': Fullscreen,\n      'handle': Handle,\n      // FIXME: HintPopover must be front of autolink\n      //  - Script error about range when Enter key is pressed on hint popover\n      'hintPopover': HintPopover,\n      'autoLink': AutoLink,\n      'autoSync': AutoSync,\n      'autoReplace': AutoReplace,\n      'placeholder': Placeholder,\n      'buttons': Buttons,\n      'toolbar': Toolbar,\n      'linkDialog': LinkDialog,\n      'linkPopover': LinkPopover,\n      'imageDialog': ImageDialog,\n      'imagePopover': ImagePopover,\n      'tablePopover': TablePopover,\n      'videoDialog': VideoDialog,\n      'helpDialog': HelpDialog,\n      'airPopover': AirPopover,\n    },\n\n    buttons: {},\n\n    lang: 'en-US',\n\n    followingToolbar: false,\n    toolbarPosition: 'top',\n    otherStaticBar: '',\n\n    // toolbar\n    codeviewKeepButton: false,\n    toolbar: [\n      ['style', ['style']],\n      ['font', ['bold', 'underline', 'clear']],\n      ['fontname', ['fontname']],\n      ['color', ['color']],\n      ['para', ['ul', 'ol', 'paragraph']],\n      ['table', ['table']],\n      ['insert', ['link', 'picture', 'video']],\n      ['view', ['fullscreen', 'codeview', 'help']],\n    ],\n\n    // popover\n    popatmouse: true,\n    popover: {\n      image: [\n        ['resize', ['resizeFull', 'resizeHalf', 'resizeQuarter', 'resizeNone']],\n        ['float', ['floatLeft', 'floatRight', 'floatNone']],\n        ['remove', ['removeMedia']],\n      ],\n      link: [\n        ['link', ['linkDialogShow', 'unlink']],\n      ],\n      table: [\n        ['add', ['addRowDown', 'addRowUp', 'addColLeft', 'addColRight']],\n        ['delete', ['deleteRow', 'deleteCol', 'deleteTable']],\n      ],\n      air: [\n        ['color', ['color']],\n        ['font', ['bold', 'underline', 'clear']],\n        ['para', ['ul', 'paragraph']],\n        ['table', ['table']],\n        ['insert', ['link', 'picture']],\n        ['view', ['fullscreen', 'codeview']],\n      ],\n    },\n\n    // air mode: inline editor\n    airMode: false,\n    overrideContextMenu: false, // TBD\n\n    width: null,\n    height: null,\n    linkTargetBlank: true,\n    useProtocol: true,\n    defaultProtocol: 'http://',\n\n    focus: false,\n    tabDisabled: false,\n    tabSize: 4,\n    styleWithCSS: false,\n    shortcuts: true,\n    textareaAutoSync: true,\n    tooltip: 'auto',\n    container: null,\n    maxTextLength: 0,\n    blockquoteBreakingLevel: 2,\n    spellCheck: true,\n    disableGrammar: false,\n    placeholder: null,\n    inheritPlaceholder: false,\n    // TODO: need to be documented\n    recordEveryKeystroke: false,\n    historyLimit: 200,\n\n    // TODO: need to be documented\n    showDomainOnlyForAutolink: false,\n\n    // TODO: need to be documented\n    hintMode: 'word',\n    hintSelect: 'after',\n    hintDirection: 'bottom',\n\n    styleTags: ['p', 'blockquote', 'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],\n\n    fontNames: [\n      'Arial', 'Arial Black', 'Comic Sans MS', 'Courier New',\n      'Helvetica Neue', 'Helvetica', 'Impact', 'Lucida Grande',\n      'Tahoma', 'Times New Roman', 'Verdana',\n    ],\n    fontNamesIgnoreCheck: [],\n    addDefaultFonts: true,\n\n    fontSizes: ['8', '9', '10', '11', '12', '14', '18', '24', '36'],\n\n    fontSizeUnits: ['px', 'pt'],\n\n    // pallete colors(n x n)\n    colors: [\n      ['#000000', '#424242', '#636363', '#9C9C94', '#CEC6CE', '#EFEFEF', '#F7F7F7', '#FFFFFF'],\n      ['#FF0000', '#FF9C00', '#FFFF00', '#00FF00', '#00FFFF', '#0000FF', '#9C00FF', '#FF00FF'],\n      ['#F7C6CE', '#FFE7CE', '#FFEFC6', '#D6EFD6', '#CEDEE7', '#CEE7F7', '#D6D6E7', '#E7D6DE'],\n      ['#E79C9C', '#FFC69C', '#FFE79C', '#B5D6A5', '#A5C6CE', '#9CC6EF', '#B5A5D6', '#D6A5BD'],\n      ['#E76363', '#F7AD6B', '#FFD663', '#94BD7B', '#73A5AD', '#6BADDE', '#8C7BC6', '#C67BA5'],\n      ['#CE0000', '#E79439', '#EFC631', '#6BA54A', '#4A7B8C', '#3984C6', '#634AA5', '#A54A7B'],\n      ['#9C0000', '#B56308', '#BD9400', '#397B21', '#104A5A', '#085294', '#311873', '#731842'],\n      ['#630000', '#7B3900', '#846300', '#295218', '#083139', '#003163', '#21104A', '#4A1031'],\n    ],\n\n    // http://chir.ag/projects/name-that-color/\n    colorsName: [\n      ['Black', 'Tundora', 'Dove Gray', 'Star Dust', 'Pale Slate', 'Gallery', 'Alabaster', 'White'],\n      ['Red', 'Orange Peel', 'Yellow', 'Green', 'Cyan', 'Blue', 'Electric Violet', 'Magenta'],\n      ['Azalea', 'Karry', 'Egg White', 'Zanah', 'Botticelli', 'Tropical Blue', 'Mischka', 'Twilight'],\n      ['Tonys Pink', 'Peach Orange', 'Cream Brulee', 'Sprout', 'Casper', 'Perano', 'Cold Purple', 'Careys Pink'],\n      ['Mandy', 'Rajah', 'Dandelion', 'Olivine', 'Gulf Stream', 'Viking', 'Blue Marguerite', 'Puce'],\n      ['Guardsman Red', 'Fire Bush', 'Golden Dream', 'Chelsea Cucumber', 'Smalt Blue', 'Boston Blue', 'Butterfly Bush', 'Cadillac'],\n      ['Sangria', 'Mai Tai', 'Buddha Gold', 'Forest Green', 'Eden', 'Venice Blue', 'Meteorite', 'Claret'],\n      ['Rosewood', 'Cinnamon', 'Olive', 'Parsley', 'Tiber', 'Midnight Blue', 'Valentino', 'Loulou'],\n    ],\n\n    colorButton: {\n      foreColor: '#000000',\n      backColor: '#FFFF00',\n    },\n\n    lineHeights: ['1.0', '1.2', '1.4', '1.5', '1.6', '1.8', '2.0', '3.0'],\n\n    tableClassName: 'table table-bordered',\n\n    insertTableMaxSize: {\n      col: 10,\n      row: 10,\n    },\n\n    // By default, dialogs are attached in container.\n    dialogsInBody: false,\n    dialogsFade: false,\n\n    maximumImageFileSize: null,\n\n    callbacks: {\n      onBeforeCommand: null,\n      onBlur: null,\n      onBlurCodeview: null,\n      onChange: null,\n      onChangeCodeview: null,\n      onDialogShown: null,\n      onEnter: null,\n      onFocus: null,\n      onImageLinkInsert: null,\n      onImageUpload: null,\n      onImageUploadError: null,\n      onInit: null,\n      onKeydown: null,\n      onKeyup: null,\n      onMousedown: null,\n      onMouseup: null,\n      onPaste: null,\n      onScroll: null,\n    },\n\n    codemirror: {\n      mode: 'text/html',\n      htmlMode: true,\n      lineNumbers: true,\n    },\n\n    codeviewFilter: false,\n    codeviewFilterRegex: /<\\/*(?:applet|b(?:ase|gsound|link)|embed|frame(?:set)?|ilayer|l(?:ayer|ink)|meta|object|s(?:cript|tyle)|t(?:itle|extarea)|xml)[^>]*?>/gi,\n    codeviewIframeFilter: true,\n    codeviewIframeWhitelistSrc: [],\n    codeviewIframeWhitelistSrcBase: [\n      'www.youtube.com',\n      'www.youtube-nocookie.com',\n      'www.facebook.com',\n      'vine.co',\n      'instagram.com',\n      'player.vimeo.com',\n      'www.dailymotion.com',\n      'player.youku.com',\n      'v.qq.com',\n    ],\n\n    keyMap: {\n      pc: {\n        'ESC': 'escape',\n        'ENTER': 'insertParagraph',\n        'CTRL+Z': 'undo',\n        'CTRL+Y': 'redo',\n        'TAB': 'tab',\n        'SHIFT+TAB': 'untab',\n        'CTRL+B': 'bold',\n        'CTRL+I': 'italic',\n        'CTRL+U': 'underline',\n        'CTRL+SHIFT+S': 'strikethrough',\n        'CTRL+BACKSLASH': 'removeFormat',\n        'CTRL+SHIFT+L': 'justifyLeft',\n        'CTRL+SHIFT+E': 'justifyCenter',\n        'CTRL+SHIFT+R': 'justifyRight',\n        'CTRL+SHIFT+J': 'justifyFull',\n        'CTRL+SHIFT+NUM7': 'insertUnorderedList',\n        'CTRL+SHIFT+NUM8': 'insertOrderedList',\n        'CTRL+LEFTBRACKET': 'outdent',\n        'CTRL+RIGHTBRACKET': 'indent',\n        'CTRL+NUM0': 'formatPara',\n        'CTRL+NUM1': 'formatH1',\n        'CTRL+NUM2': 'formatH2',\n        'CTRL+NUM3': 'formatH3',\n        'CTRL+NUM4': 'formatH4',\n        'CTRL+NUM5': 'formatH5',\n        'CTRL+NUM6': 'formatH6',\n        'CTRL+ENTER': 'insertHorizontalRule',\n        'CTRL+K': 'linkDialog.show',\n      },\n\n      mac: {\n        'ESC': 'escape',\n        'ENTER': 'insertParagraph',\n        'CMD+Z': 'undo',\n        'CMD+SHIFT+Z': 'redo',\n        'TAB': 'tab',\n        'SHIFT+TAB': 'untab',\n        'CMD+B': 'bold',\n        'CMD+I': 'italic',\n        'CMD+U': 'underline',\n        'CMD+SHIFT+S': 'strikethrough',\n        'CMD+BACKSLASH': 'removeFormat',\n        'CMD+SHIFT+L': 'justifyLeft',\n        'CMD+SHIFT+E': 'justifyCenter',\n        'CMD+SHIFT+R': 'justifyRight',\n        'CMD+SHIFT+J': 'justifyFull',\n        'CMD+SHIFT+NUM7': 'insertUnorderedList',\n        'CMD+SHIFT+NUM8': 'insertOrderedList',\n        'CMD+LEFTBRACKET': 'outdent',\n        'CMD+RIGHTBRACKET': 'indent',\n        'CMD+NUM0': 'formatPara',\n        'CMD+NUM1': 'formatH1',\n        'CMD+NUM2': 'formatH2',\n        'CMD+NUM3': 'formatH3',\n        'CMD+NUM4': 'formatH4',\n        'CMD+NUM5': 'formatH5',\n        'CMD+NUM6': 'formatH6',\n        'CMD+ENTER': 'insertHorizontalRule',\n        'CMD+K': 'linkDialog.show',\n      },\n    },\n    icons: {\n      'align': 'note-icon-align',\n      'alignCenter': 'note-icon-align-center',\n      'alignJustify': 'note-icon-align-justify',\n      'alignLeft': 'note-icon-align-left',\n      'alignRight': 'note-icon-align-right',\n      'rowBelow': 'note-icon-row-below',\n      'colBefore': 'note-icon-col-before',\n      'colAfter': 'note-icon-col-after',\n      'rowAbove': 'note-icon-row-above',\n      'rowRemove': 'note-icon-row-remove',\n      'colRemove': 'note-icon-col-remove',\n      'indent': 'note-icon-align-indent',\n      'outdent': 'note-icon-align-outdent',\n      'arrowsAlt': 'note-icon-arrows-alt',\n      'bold': 'note-icon-bold',\n      'caret': 'note-icon-caret',\n      'circle': 'note-icon-circle',\n      'close': 'note-icon-close',\n      'code': 'note-icon-code',\n      'eraser': 'note-icon-eraser',\n      'floatLeft': 'note-icon-float-left',\n      'floatRight': 'note-icon-float-right',\n      'font': 'note-icon-font',\n      'frame': 'note-icon-frame',\n      'italic': 'note-icon-italic',\n      'link': 'note-icon-link',\n      'unlink': 'note-icon-chain-broken',\n      'magic': 'note-icon-magic',\n      'menuCheck': 'note-icon-menu-check',\n      'minus': 'note-icon-minus',\n      'orderedlist': 'note-icon-orderedlist',\n      'pencil': 'note-icon-pencil',\n      'picture': 'note-icon-picture',\n      'question': 'note-icon-question',\n      'redo': 'note-icon-redo',\n      'rollback': 'note-icon-rollback',\n      'square': 'note-icon-square',\n      'strikethrough': 'note-icon-strikethrough',\n      'subscript': 'note-icon-subscript',\n      'superscript': 'note-icon-superscript',\n      'table': 'note-icon-table',\n      'textHeight': 'note-icon-text-height',\n      'trash': 'note-icon-trash',\n      'underline': 'note-icon-underline',\n      'undo': 'note-icon-undo',\n      'unorderedlist': 'note-icon-unorderedlist',\n      'video': 'note-icon-video',\n    },\n  },\n});\n", "import $ from 'jquery';\nimport renderer from '../base/renderer';\n\nconst editor = renderer.create('<div class=\"note-editor note-frame card\"/>');\nconst toolbar = renderer.create('<div class=\"note-toolbar card-header\" role=\"toolbar\"/>');\nconst editingArea = renderer.create('<div class=\"note-editing-area\"/>');\nconst codable = renderer.create('<textarea class=\"note-codable\" aria-multiline=\"true\"/>');\nconst editable = renderer.create('<div class=\"note-editable card-block\" contentEditable=\"true\" role=\"textbox\" aria-multiline=\"true\"/>');\nconst statusbar = renderer.create([\n  '<output class=\"note-status-output\" role=\"status\" aria-live=\"polite\"></output>',\n  '<div class=\"note-statusbar\" role=\"status\">',\n    '<div class=\"note-resizebar\" aria-label=\"Resize\">',\n      '<div class=\"note-icon-bar\"></div>',\n      '<div class=\"note-icon-bar\"></div>',\n      '<div class=\"note-icon-bar\"></div>',\n    '</div>',\n  '</div>',\n].join(''));\n\nconst airEditor = renderer.create('<div class=\"note-editor note-airframe\"/>');\nconst airEditable = renderer.create([\n  '<div class=\"note-editable\" contentEditable=\"true\" role=\"textbox\" aria-multiline=\"true\"></div>',\n  '<output class=\"note-status-output\" role=\"status\" aria-live=\"polite\"></output>',\n].join(''));\n\nconst buttonGroup = renderer.create('<div class=\"note-btn-group btn-group\">');\n\nconst dropdown = renderer.create('<div class=\"note-dropdown-menu dropdown-menu\" role=\"list\">', function($node, options) {\n  const markup = Array.isArray(options.items) ? options.items.map(function(item) {\n    const value = (typeof item === 'string') ? item : (item.value || '');\n    const content = options.template ? options.template(item) : item;\n    const option = (typeof item === 'object') ? item.option : undefined;\n\n    const dataValue = 'data-value=\"' + value + '\"';\n    const dataOption = (option !== undefined) ? ' data-option=\"' + option + '\"' : '';\n    return '<a class=\"dropdown-item\" href=\"#\" ' + (dataValue + dataOption) + ' role=\"listitem\" aria-label=\"' + value + '\">' + content + '</a>';\n  }).join('') : options.items;\n\n  $node.html(markup).attr({ 'aria-label': options.title });\n\n  if (options && options.codeviewKeepButton) {\n    $node.addClass('note-codeview-keep');\n  }\n});\n\nconst dropdownButtonContents = function(contents) {\n  return contents;\n};\n\nconst dropdownCheck = renderer.create('<div class=\"note-dropdown-menu dropdown-menu note-check\" role=\"list\">', function($node, options) {\n  const markup = Array.isArray(options.items) ? options.items.map(function(item) {\n    const value = (typeof item === 'string') ? item : (item.value || '');\n    const content = options.template ? options.template(item) : item;\n    return '<a class=\"dropdown-item\" href=\"#\" data-value=\"' + value + '\" role=\"listitem\" aria-label=\"' + item + '\">' + icon(options.checkClassName) + ' ' + content + '</a>';\n  }).join('') : options.items;\n  $node.html(markup).attr({ 'aria-label': options.title });\n\n  if (options && options.codeviewKeepButton) {\n    $node.addClass('note-codeview-keep');\n  }\n});\n\nconst dialog = renderer.create('<div class=\"modal note-modal\" aria-hidden=\"false\" tabindex=\"-1\" role=\"dialog\"/>', function($node, options) {\n  if (options.fade) {\n    $node.addClass('fade');\n  }\n  $node.attr({\n    'aria-label': options.title,\n  });\n  $node.html([\n    '<div class=\"modal-dialog\">',\n      '<div class=\"modal-content\">',\n        (options.title ? '<div class=\"modal-header\">' +\n          '<h4 class=\"modal-title\">' + options.title + '</h4>' +\n          '<button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\" aria-hidden=\"true\">&times;</button>' +\n        '</div>' : ''),\n        '<div class=\"modal-body\">' + options.body + '</div>',\n        (options.footer ? '<div class=\"modal-footer\">' + options.footer + '</div>' : ''),\n      '</div>',\n    '</div>',\n  ].join(''));\n});\n\nconst popover = renderer.create([\n  '<div class=\"note-popover popover in\">',\n    '<div class=\"arrow\"></div>',\n    '<div class=\"popover-content note-children-container\"></div>',\n  '</div>',\n].join(''), function($node, options) {\n  const direction = typeof options.direction !== 'undefined' ? options.direction : 'bottom';\n\n  $node.addClass(direction);\n\n  if (options.hideArrow) {\n    $node.find('.arrow').hide();\n  }\n});\n\nconst checkbox = renderer.create('<div class=\"form-check\"></div>', function($node, options) {\n  $node.html([\n    '<label class=\"form-check-label\"' + (options.id ? ' for=\"note-' + options.id + '\"' : '') + '>',\n      '<input type=\"checkbox\" class=\"form-check-input\"' + (options.id ? ' id=\"note-' + options.id + '\"' : ''),\n        (options.checked ? ' checked' : ''),\n        ' aria-label=\"' + (options.text ? options.text : '') + '\"',\n        ' aria-checked=\"' + (options.checked ? 'true' : 'false') + '\"/>',\n      ' ' + (options.text ? options.text : '') +\n    '</label>',\n  ].join(''));\n});\n\nconst icon = function(iconClassName, tagName) {\n  tagName = tagName || 'i';\n  return '<' + tagName + ' class=\"' + iconClassName + '\"></' + tagName+'>';\n};\n\nconst ui = function(editorOptions) {\n  return {\n    editor: editor,\n    toolbar: toolbar,\n    editingArea: editingArea,\n    codable: codable,\n    editable: editable,\n    statusbar: statusbar,\n    airEditor: airEditor,\n    airEditable: airEditable,\n    buttonGroup: buttonGroup,\n    dropdown: dropdown,\n    dropdownButtonContents: dropdownButtonContents,\n    dropdownCheck: dropdownCheck,\n    dialog: dialog,\n    popover: popover,\n    icon: icon,\n    checkbox: checkbox,\n    options: editorOptions,\n\n    palette: function($node, options) {\n      return renderer.create('<div class=\"note-color-palette\"/>', function($node, options) {\n        const contents = [];\n        for (let row = 0, rowSize = options.colors.length; row < rowSize; row++) {\n          const eventName = options.eventName;\n          const colors = options.colors[row];\n          const colorsName = options.colorsName[row];\n          const buttons = [];\n          for (let col = 0, colSize = colors.length; col < colSize; col++) {\n            const color = colors[col];\n            const colorName = colorsName[col];\n            buttons.push([\n              '<button type=\"button\" class=\"note-color-btn\"',\n              'style=\"background-color:', color, '\" ',\n              'data-event=\"', eventName, '\" ',\n              'data-value=\"', color, '\" ',\n              'title=\"', colorName, '\" ',\n              'aria-label=\"', colorName, '\" ',\n              'data-toggle=\"button\" tabindex=\"-1\"></button>',\n            ].join(''));\n          }\n          contents.push('<div class=\"note-color-row\">' + buttons.join('') + '</div>');\n        }\n        $node.html(contents.join(''));\n\n        if (options.tooltip) {\n          $node.find('.note-color-btn').tooltip({\n            container: options.container || editorOptions.container,\n            trigger: 'hover',\n            placement: 'bottom',\n          });\n        }\n      })($node, options);\n    },\n\n    button: function($node, options) {\n      return renderer.create('<button type=\"button\" class=\"note-btn btn btn-light btn-sm\" tabindex=\"-1\">', function($node, options) {\n        if (options && options.tooltip) {\n          $node.attr({\n            title: options.tooltip,\n            'aria-label': options.tooltip,\n          }).tooltip({\n            container: options.container || editorOptions.container,\n            trigger: 'hover',\n            placement: 'bottom',\n          }).on('click', (e) => {\n            $(e.currentTarget).tooltip('hide');\n          });\n        }\n        if (options && options.codeviewButton) {\n          $node.addClass('note-codeview-keep');\n        }\n      })($node, options);\n    },\n\n    toggleBtn: function($btn, isEnable) {\n      $btn.toggleClass('disabled', !isEnable);\n      $btn.attr('disabled', !isEnable);\n    },\n\n    toggleBtnActive: function($btn, isActive) {\n      $btn.toggleClass('active', isActive);\n    },\n\n    onDialogShown: function($dialog, handler) {\n      $dialog.one('shown.bs.modal', handler);\n    },\n\n    onDialogHidden: function($dialog, handler) {\n      $dialog.one('hidden.bs.modal', handler);\n    },\n\n    showDialog: function($dialog) {\n      $dialog.modal('show');\n    },\n\n    hideDialog: function($dialog) {\n      $dialog.modal('hide');\n    },\n\n    createLayout: function($note) {\n      const $editor = (editorOptions.airMode ? airEditor([\n        editingArea([\n          codable(),\n          airEditable(),\n        ]),\n      ]) : (editorOptions.toolbarPosition === 'bottom'\n        ? editor([\n          editingArea([\n            codable(),\n            editable(),\n          ]),\n          toolbar(),\n          statusbar(),\n        ])\n        : editor([\n          toolbar(),\n          editingArea([\n            codable(),\n            editable(),\n          ]),\n          statusbar(),\n        ])\n      )).render();\n\n      $editor.insertAfter($note);\n\n      return {\n        note: $note,\n        editor: $editor,\n        toolbar: $editor.find('.note-toolbar'),\n        editingArea: $editor.find('.note-editing-area'),\n        editable: $editor.find('.note-editable'),\n        codable: $editor.find('.note-codable'),\n        statusbar: $editor.find('.note-statusbar'),\n      };\n    },\n\n    removeLayout: function($note, layoutInfo) {\n      $note.html(layoutInfo.editable.html());\n      layoutInfo.editor.remove();\n      $note.show();\n    },\n  };\n};\n\nexport default ui;\n", "import $ from 'j<PERSON>y';\nimport ui from './ui';\nimport '../base/settings.js';\n\nimport '../../styles/summernote-bs4.scss';\n\n$.summernote = $.extend($.summernote, {\n  ui_template: ui,\n  interface: 'bs4',\n});\n\n$.summernote.options.styleTags = [\n  'p',\n  { title: 'Blockquote', tag: 'blockquote', className: 'blockquote', value: 'blockquote' },\n  'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',\n];\n"], "sourceRoot": ""}